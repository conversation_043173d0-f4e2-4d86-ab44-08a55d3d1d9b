"use client";

import {
   generateUploadUrl,
   saveImageMetadata,
} from "@/lib/actions/upload-actions";
import { extractImageMetadata } from "@/lib/utils/image-processing";
import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";

interface DirectUploadOptions {
   albumId?: string | null;
   collectionIds?: string[];
}

interface UploadResult {
   success: boolean;
   // eslint-disable-next-line @typescript-eslint/no-explicit-any
   data?: any;
   error?: string;
}

export function useDirectUpload() {
   return useMutation({
      mutationFn: async ({
         file,
         options = {},
      }: {
         file: File;
         options?: DirectUploadOptions;
      }): Promise<UploadResult> => {
         try {
            // Step 1: Generate pre-signed URL
            const urlResult = await generateUploadUrl(
               file.name,
               file.type,
               file.size
            );

            if (!urlResult.success) {
               return {
                  success: false,
                  error: urlResult.error,
               };
            }

            const { uploadUrl, key } = urlResult.data as {
               uploadUrl: string;
               key: string;
            };

            // Step 2: Upload file directly to R2
            const uploadResponse = await fetch(uploadUrl, {
               method: "PUT",
               body: file,
               headers: {
                  "Content-Type": file.type,
               },
            });

            if (!uploadResponse.ok) {
               throw new Error(`Upload failed: ${uploadResponse.statusText}`);
            }

            // Step 3: Extract image metadata
            const metadata = await extractImageMetadata(file);

            // Step 4: Save metadata to database
            const saveResult = await saveImageMetadata(
               key,
               file.name,
               file.type,
               file.size,
               metadata.width,
               metadata.height,
               options.albumId || undefined,
               options.collectionIds
            );

            if (!saveResult.success) {
               return {
                  success: false,
                  error: saveResult.error,
               };
            }

            return {
               success: true,
               data: saveResult.data,
            };
         } catch (error) {
            console.error("Direct upload error:", error);
            return {
               success: false,
               error: error instanceof Error ? error.message : "Upload failed",
            };
         }
      },
      onSuccess: (data) => {
         if (data.success) {
            toast.success("Image uploaded successfully");
         } else {
            toast.error(data.error || "Failed to upload image");
         }
      },
      onError: (error) => {
         console.error("Upload error:", error);
         toast.error(
            error instanceof Error ? error.message : "Failed to upload image"
         );
      },
   });
}
