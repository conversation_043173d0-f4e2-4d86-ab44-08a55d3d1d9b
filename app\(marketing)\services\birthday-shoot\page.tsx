import ElementReveal from "@/components/animations/element-reveal";
import StaggerReveal from "@/components/animations/stagger-reveal";
import TextReveal from "@/components/animations/text-reveal";
import { ImageGallery } from "@/components/image-gallery";
import { Button } from "@/components/ui/button";
import {
   Card,
   CardContent,
   CardDescription,
   CardHeader,
   CardTitle,
} from "@/components/ui/card";
import { ArrowRight, Camera, Clock, Heart, Sparkles } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

const galleryImages = [
   {
      src: "/images/birthday-shoots/birthday-shoot-1.JPG",
      alt: "Birthday celebration photography",
   },
   {
      src: "/images/birthday-shoots/birthday-shoot-2.JPG",
      alt: "Birthday party moments",
   },
   {
      src: "/images/birthday-shoots/birthday-shoot-3.JPG",
      alt: "Birthday cake celebration",
   },
   {
      src: "/images/birthday-shoots/birthday-shoot-4.JPG",
      alt: "Birthday decorations and setup",
   },
   {
      src: "/images/birthday-shoots/birthday-shoot-5.JPG",
      alt: "Birthday party activities",
   },
   {
      src: "/images/birthday-shoots/birthday-shoot-6.JPG",
      alt: "Birthday celebration memories",
   },
];

const features = [
   {
      icon: Heart,
      title: "Personalized Experience",
      description:
         "We tailor each birthday shoot to reflect the personality and preferences of the birthday person.",
   },
   {
      icon: Camera,
      title: "Professional Quality",
      description:
         "High-quality photography that captures every special moment and emotion of the celebration.",
   },
   {
      icon: Clock,
      title: "Flexible Timing",
      description:
         "We work around your schedule, whether it's a morning brunch or evening party celebration.",
   },
   {
      icon: Sparkles,
      title: "Creative Styling",
      description:
         "From themed decorations to artistic compositions, we help make your birthday photos magical.",
   },
];

const eventTypes = [
   {
      title: "Children's Birthdays",
      description:
         "Capture the joy and wonder of childhood birthdays with fun, candid shots and posed portraits.",
   },
   {
      title: "Teen Birthdays",
      description:
         "Modern, stylish photography that reflects the personality and interests of teenagers.",
   },
   {
      title: "Adult Birthdays",
      description:
         "Sophisticated photography for milestone birthdays and elegant adult celebrations.",
   },
   {
      title: "Surprise Parties",
      description:
         "Discrete photography that captures the genuine reactions and emotions of surprise celebrations.",
   },
   {
      title: "Themed Parties",
      description:
         "Creative photography that enhances and showcases your party theme and decorations.",
   },
   {
      title: "Intimate Gatherings",
      description:
         "Warm, personal photography for small, meaningful birthday celebrations with close friends and family.",
   },
];

export default function BirthdayShootPage() {
   return (
      <div className="min-h-screen">
         {/* Hero Section */}
         <section className="relative !pt-38 pb-16">
            <div className="absolute inset-0 z-0">
               <Image
                  src="/images/birthday-shoots/birthday-shoot-1.JPG"
                  alt="Birthday photography hero"
                  fill
                  className="object-cover object-[50%_20%]"
                  priority
                  sizes="100vw"
               />
               <div className="absolute inset-0 bg-black/50" />
            </div>

            <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
               <TextReveal>
                  <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
                     Birthday Photography
                  </h1>
               </TextReveal>
               <TextReveal className="mb-8">
                  <p className="text-lg max-w-3xl mx-auto leading-relaxed">
                     Capture the joy, laughter, and special moments of birthday
                     celebrations with professional photography that tells your
                     story
                  </p>
               </TextReveal>
               <ElementReveal>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                     <Button asChild size="lg">
                        <Link href="/contact">Book Your Birthday Shoot</Link>
                     </Button>
                     <Button
                        asChild
                        variant="outline"
                        size="lg"
                        className="text-white hover:bg-white hover:text-black"
                     >
                        <Link href="/portfolio">View Birthday Gallery</Link>
                     </Button>
                  </div>
               </ElementReveal>
            </div>
         </section>

         {/* Features Section */}
         <section className="py-20">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
               <div className="text-center mb-16">
                  <TextReveal>
                     <h2 className="text-3xl md:text-4xl font-bold mb-4">
                        Why Choose Us for Your Birthday
                     </h2>
                  </TextReveal>
                  <TextReveal>
                     <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                        We specialize in capturing the authentic moments and
                        emotions that make birthday celebrations truly special
                        and memorable.
                     </p>
                  </TextReveal>
               </div>

               <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                  {features.map((feature, index) => (
                     <StaggerReveal key={feature.title} index={index}>
                        <Card className="text-center">
                           <CardHeader>
                              <div className="flex justify-center mb-4">
                                 <feature.icon className="h-12 w-12 text-primary" />
                              </div>
                              <CardTitle>{feature.title}</CardTitle>
                           </CardHeader>
                           <CardContent>
                              <CardDescription className="text-center">
                                 {feature.description}
                              </CardDescription>
                           </CardContent>
                        </Card>
                     </StaggerReveal>
                  ))}
               </div>
            </div>
         </section>

         {/* Event Types Section */}
         <section className="py-20 bg-astral-grey">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
               <div className="text-center mb-16">
                  <TextReveal>
                     <h2 className="text-3xl md:text-4xl font-bold mb-4">
                        Perfect for Every Birthday
                     </h2>
                  </TextReveal>
                  <TextReveal>
                     <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                        From intimate family gatherings to grand celebrations,
                        we capture the essence of every birthday milestone.
                     </p>
                  </TextReveal>
               </div>

               <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {eventTypes.map((event, index) => (
                     <StaggerReveal key={event.title} index={index}>
                        <Card>
                           <CardHeader>
                              <CardTitle>{event.title}</CardTitle>
                           </CardHeader>
                           <CardContent>
                              <CardDescription>
                                 {event.description}
                              </CardDescription>
                           </CardContent>
                        </Card>
                     </StaggerReveal>
                  ))}
               </div>
            </div>
         </section>

         {/* Gallery Section */}
         <section className="py-20 bg-background">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
               <div className="text-center mb-16">
                  <TextReveal>
                     <h2 className="text-3xl md:text-4xl font-bold mb-4">
                        Recent Birthday Photography
                     </h2>
                  </TextReveal>
                  <TextReveal>
                     <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                        Take a look at some of our recent birthday photography
                        work that showcases our style and ability to capture
                        special moments.
                     </p>
                  </TextReveal>
               </div>

               <ImageGallery images={galleryImages} />
            </div>
         </section>

         {/* CTA Section */}
         <section className="py-20 bg-astral-grey">
            <div className="container mx-auto px-4 text-center">
               <div className="max-w-4xl mx-auto">
                  <TextReveal>
                     <h2 className="text-4xl font-playfair font-bold text-foreground mb-6">
                        Ready to Capture Your{" "}
                        <span className="bg-gradient-accent bg-clip-text text-transparent">
                           Birthday Memories?
                        </span>
                     </h2>
                  </TextReveal>
                  <TextReveal className="mb-8">
                     <p className="text-lg text-muted-foreground font-montserrat leading-relaxed">
                        Let&apos;s create beautiful memories of your special day
                        that you&apos;ll treasure for years to come.
                     </p>
                  </TextReveal>
                  <ElementReveal>
                     <div className="flex flex-col sm:flex-row gap-4 items-center justify-center">
                        <Button
                           asChild
                           size="lg"
                           className="bg-gradient-accent hover:opacity-90 font-montserrat font-semibold text-lg px-8"
                        >
                           <Link href="/contact">
                              Contact Now{" "}
                              <ArrowRight className="ml-2 h-5 w-5" />
                           </Link>
                        </Button>
                        <Button
                           asChild
                           variant="outline"
                           size="lg"
                           className="border-primary/20 hover:bg-primary/5 font-montserrat font-semibold text-lg px-8"
                        >
                           <Link href="/portfolio">View Portfolio</Link>
                        </Button>
                     </div>
                  </ElementReveal>
               </div>
            </div>
         </section>
      </div>
   );
}
