"use client";

import { cn } from "@/lib/utils";
import { motion } from "framer-motion";

interface StaggerRevealProps {
   children: React.ReactNode;
   index: number;
   className?: string;
   baseDelay?: number;
   stagger?: number;
   duration?: number;
   offset?: number;
}

export default function StaggerReveal({
   children,
   index,
   className,
   baseDelay = 0,
   stagger = 0.1,
   duration = 0.3,
   offset = 10,
}: StaggerRevealProps) {
   return (
      <motion.div
         initial={{ opacity: 0, y: offset }}
         whileInView={{ opacity: 1, y: 0 }}
         transition={{ duration, delay: baseDelay + index * stagger }}
         viewport={{ once: true }}
         className={cn(className)}
      >
         {children}
      </motion.div>
   );
}
