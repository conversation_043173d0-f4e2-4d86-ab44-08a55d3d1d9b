"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { Search, X } from "lucide-react";
import { useEffect, useState } from "react";

interface GallerySearchProps {
   onSearch: (query: string) => void;
   placeholder?: string;
   className?: string;
   initialValue?: string;
   showClearButton?: boolean;
}

export default function GallerySearch({
   onSearch,
   placeholder = "Search albums and collections...",
   className,
   initialValue = "",
   showClearButton = true,
}: GallerySearchProps) {
   const [query, setQuery] = useState(initialValue);
   const [isFocused, setIsFocused] = useState(false);

   // Debounced search
   useEffect(() => {
      const timer = setTimeout(() => {
         onSearch(query);
      }, 300);

      return () => clearTimeout(timer);
   }, [query, onSearch]);

   const handleClear = () => {
      setQuery("");
      onSearch("");
   };

   return (
      <div className={cn("relative max-w-xl mx-auto", className)}>
         <div
            className={cn(
               "relative flex items-center transition-all duration-300"
            )}
         >
            {/* Search Icon */}
            <div className="absolute left-6 z-10">
               <Search
                  className={cn(
                     "w-5 h-5 transition-colors duration-200",
                     isFocused ? "text-primary" : "text-muted-foreground"
                  )}
               />
            </div>

            {/* Input Field */}
            <Input
               type="text"
               value={query}
               onChange={(e) => setQuery(e.target.value)}
               onFocus={() => setIsFocused(true)}
               onBlur={() => setIsFocused(false)}
               placeholder={placeholder}
               className={cn(
                  "pl-14 pr-12 py-4 text-lg rounded-2xl border-2 transition-all duration-300",
                  "bg-background/80 backdrop-blur-sm",
                  "focus:border-primary focus:ring-10 focus:ring-primary/20",
                  "placeholder:text-muted-foreground/60",
                  "text-sm sm:text-lg",
                  isFocused
                     ? "border-primary bg-background"
                     : "border-border hover:border-primary/50"
               )}
            />

            {/* Clear Button */}
            {showClearButton && query && (
               <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={handleClear}
                  className="absolute right-2 z-10 h-8 w-8 p-0 hover:bg-destructive/10 hover:text-destructive rounded-full"
               >
                  <X className="w-4 h-4" />
               </Button>
            )}
         </div>
      </div>
   );
}
