"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";
import { Calendar, Filter, Tag, User, X } from "lucide-react";
import { useState } from "react";

export interface FilterOption {
   id: string;
   label: string;
   value: string;
}

export interface ActiveFilter {
   type: string;
   value: string;
   label: string;
}

interface GalleryFiltersProps {
   onFiltersChange: (filters: Record<string, string>) => void;
   dateOptions?: FilterOption[];
   categoryOptions?: FilterOption[];
   clientOptions?: FilterOption[];
   showDateFilter?: boolean;
   showCategoryFilter?: boolean;
   showClientFilter?: boolean;
   className?: string;
}

export default function GalleryFilters({
   onFiltersChange,
   dateOptions = [
      { id: "all", label: "All Time", value: "all" },
      { id: "this-year", label: "This Year", value: "2024" },
      { id: "last-year", label: "Last Year", value: "2023" },
      { id: "older", label: "Older", value: "2022" },
   ],
   categoryOptions = [
      { id: "all", label: "All Categories", value: "all" },
      { id: "wedding", label: "Wedding", value: "wedding" },
      { id: "pre-wedding", label: "Pre-Wedding", value: "pre-wedding" },
      { id: "pregnancy", label: "Pregnancy", value: "pregnancy" },
      {
         id: "child-dedication",
         label: "Child Dedication",
         value: "child-dedication",
      },
   ],
   clientOptions = [],
   showDateFilter = true,
   showCategoryFilter = true,
   showClientFilter = false,
   className,
}: GalleryFiltersProps) {
   const [filters, setFilters] = useState<Record<string, string>>({});
   const [isExpanded, setIsExpanded] = useState(false);

   const handleFilterChange = (type: string, value: string) => {
      const newFilters = { ...filters };
      if (value && value !== "all") {
         newFilters[type] = value;
      } else {
         delete newFilters[type];
      }
      setFilters(newFilters);
      onFiltersChange(newFilters);
   };

   const clearFilter = (type: string) => {
      handleFilterChange(type, "");
   };

   const clearAllFilters = () => {
      setFilters({});
      onFiltersChange({});
   };

   const activeFilters: ActiveFilter[] = Object.entries(filters).map(
      ([type, value]) => {
         let label = value;

         // Find the proper label for the filter value
         if (type === "date") {
            const option = dateOptions.find((opt) => opt.value === value);
            label = option?.label || value;
         } else if (type === "category") {
            const option = categoryOptions.find((opt) => opt.value === value);
            label = option?.label || value;
         } else if (type === "client") {
            const option = clientOptions.find((opt) => opt.value === value);
            label = option?.label || value;
         }

         return { type, value, label };
      }
   );

   const hasActiveFilters = activeFilters.length > 0;

   return (
      <div className={cn("space-y-4", className)}>
         {/* Filter Toggle Button */}
         <div className="flex items-center justify-between">
            <Button
               variant="outline"
               onClick={() => setIsExpanded(!isExpanded)}
               className="flex items-center gap-2"
            >
               <Filter className="w-4 h-4" />
               Filters
               {hasActiveFilters && (
                  <Badge variant="secondary" className="ml-2">
                     {activeFilters.length}
                  </Badge>
               )}
            </Button>

            {hasActiveFilters && (
               <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearAllFilters}
                  className="text-muted-foreground hover:text-foreground"
               >
                  Clear All
               </Button>
            )}
         </div>

         {/* Active Filters */}
         {hasActiveFilters && (
            <div className="flex flex-wrap gap-2">
               {activeFilters.map((filter) => (
                  <Badge
                     key={`${filter.type}-${filter.value}`}
                     variant="secondary"
                     className="flex items-center gap-1 pr-1"
                  >
                     {filter.type === "date" && (
                        <Calendar className="w-3 h-3" />
                     )}
                     {filter.type === "category" && <Tag className="w-3 h-3" />}
                     {filter.type === "client" && <User className="w-3 h-3" />}
                     {filter.label}
                     <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => clearFilter(filter.type)}
                        className="h-4 w-4 p-0 hover:bg-destructive/20 hover:text-destructive ml-1"
                     >
                        <X className="w-3 h-3" />
                     </Button>
                  </Badge>
               ))}
            </div>
         )}

         {/* Filter Controls */}
         {isExpanded && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-card rounded-lg border">
               {/* Date Filter */}
               {showDateFilter && (
                  <div className="space-y-2">
                     <label className="text-sm font-medium flex items-center gap-2">
                        <Calendar className="w-4 h-4" />
                        Date
                     </label>
                     <Select
                        value={filters.date || "all"}
                        onValueChange={(value) =>
                           handleFilterChange("date", value)
                        }
                     >
                        <SelectTrigger>
                           <SelectValue placeholder="Select date range" />
                        </SelectTrigger>
                        <SelectContent>
                           {dateOptions.map((option) => (
                              <SelectItem key={option.id} value={option.value}>
                                 {option.label}
                              </SelectItem>
                           ))}
                        </SelectContent>
                     </Select>
                  </div>
               )}

               {/* Category Filter */}
               {showCategoryFilter && (
                  <div className="space-y-2">
                     <label className="text-sm font-medium flex items-center gap-2">
                        <Tag className="w-4 h-4" />
                        Category
                     </label>
                     <Select
                        value={filters.category || "all"}
                        onValueChange={(value) =>
                           handleFilterChange("category", value)
                        }
                     >
                        <SelectTrigger>
                           <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                           {categoryOptions.map((option) => (
                              <SelectItem key={option.id} value={option.value}>
                                 {option.label}
                              </SelectItem>
                           ))}
                        </SelectContent>
                     </Select>
                  </div>
               )}

               {/* Client Filter */}
               {showClientFilter && clientOptions.length > 0 && (
                  <div className="space-y-2">
                     <label className="text-sm font-medium flex items-center gap-2">
                        <User className="w-4 h-4" />
                        Client
                     </label>
                     <Select
                        value={filters.client || "all"}
                        onValueChange={(value) =>
                           handleFilterChange("client", value)
                        }
                     >
                        <SelectTrigger>
                           <SelectValue placeholder="Select client" />
                        </SelectTrigger>
                        <SelectContent>
                           {clientOptions.map((option) => (
                              <SelectItem key={option.id} value={option.value}>
                                 {option.label}
                              </SelectItem>
                           ))}
                        </SelectContent>
                     </Select>
                  </div>
               )}
            </div>
         )}
      </div>
   );
}
