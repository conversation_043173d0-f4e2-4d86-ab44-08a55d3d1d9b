"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
   <PERSON>alog,
   DialogContent,
   DialogDescription,
   DialogFooter,
   <PERSON><PERSON>Header,
   <PERSON><PERSON>Title,
   DialogTrigger,
} from "@/components/ui/dialog";
// import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Skeleton } from "@/components/ui/skeleton";
import { usePortfolioImagesByService } from "@/lib/hooks/use-portfolio";
import { Check, Image as ImageIcon, X } from "lucide-react";
import Image from "next/image";
import { useState } from "react";

interface CoverImageSelectorProps {
   serviceId?: string;
   currentCoverImageUrl?: string;
   onCoverImageSelect: (imageUrl: string) => void;
   onCoverImageRemove: () => void;
   disabled?: boolean;
}

export default function CoverImageSelector({
   serviceId,
   currentCoverImageUrl,
   onCoverImageSelect,
   onCoverImageRemove,
   disabled = false,
}: CoverImageSelectorProps) {
   const [isOpen, setIsOpen] = useState(false);
   // const [customUrl, setCustomUrl] = useState("");
   const [selectedImageUrl, setSelectedImageUrl] = useState(
      currentCoverImageUrl || ""
   );

   const {
      data: imagesData,
      isLoading: imagesLoading,
      error: imagesError,
   } = usePortfolioImagesByService(serviceId || "", { limit: 50 });

   const images = imagesData?.data || [];

   const handleImageSelect = (imageUrl: string) => {
      setSelectedImageUrl(imageUrl);
   };

   // const handleCustomUrlChange = (url: string) => {
   //    setCustomUrl(url);
   //    setSelectedImageUrl(url);
   // };

   const handleSave = () => {
      if (selectedImageUrl) {
         onCoverImageSelect(selectedImageUrl);
      }
      setIsOpen(false);
   };

   const handleRemove = () => {
      setSelectedImageUrl("");
      onCoverImageRemove();
      setIsOpen(false);
   };

   const handleCancel = () => {
      setSelectedImageUrl(currentCoverImageUrl || "");
      // setCustomUrl("");
      setIsOpen(false);
   };

   return (
      <div className="space-y-3">
         <Label>Cover Image</Label>

         {/* Current cover image display */}
         {currentCoverImageUrl ? (
            <div className="relative aspect-square h-58 bg-astral-grey/50 rounded-lg overflow-hidden border border-border/30">
               <Image
                  src={currentCoverImageUrl}
                  alt="Cover image"
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, 400px"
               />
               <div className="absolute inset-0 bg-black/20 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center space-x-2">
                  <Dialog open={isOpen} onOpenChange={setIsOpen}>
                     <DialogTrigger asChild>
                        <Button
                           variant="outline"
                           className="bg-background/80 backdrop-blur-sm"
                           disabled={disabled}
                        >
                           Change
                        </Button>
                     </DialogTrigger>
                  </Dialog>
                  <Button
                     variant="outline"
                     className="bg-background/80 backdrop-blur-sm text-destructive"
                     onClick={handleRemove}
                     disabled={disabled}
                  >
                     <X className="w-4 h-4" />
                  </Button>
               </div>
            </div>
         ) : (
            <div className="w-full h-32 bg-astral-grey/50 rounded-lg border-2 border-dashed border-border/50 flex items-center justify-center">
               <Dialog open={isOpen} onOpenChange={setIsOpen}>
                  <DialogTrigger asChild>
                     <Button
                        variant="outline"
                        className="flex items-center space-x-2"
                        disabled={disabled}
                     >
                        <ImageIcon className="w-4 h-4" />
                        <span>Select Cover Image</span>
                     </Button>
                  </DialogTrigger>
               </Dialog>
            </div>
         )}

         {/* Selection Dialog */}
         <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
               <DialogHeader>
                  <DialogTitle>Select Cover Image</DialogTitle>
                  <DialogDescription>
                     Choose an image from this service
                     {/* or enter a custom URL */}
                  </DialogDescription>
               </DialogHeader>

               <div className="space-y-6 overflow-y-auto max-h-[60vh]">
                  {/* Custom URL Input */}
                  {/* <div className="space-y-3">
                     <Label htmlFor="customUrl">Custom Image URL</Label>
                     <Input
                        id="customUrl"
                        placeholder="https://example.com/image.jpg"
                        value={customUrl}
                        onChange={(e) => handleCustomUrlChange(e.target.value)}
                     />
                  </div> */}

                  {/* Service Images */}
                  {serviceId && (
                     <div className="space-y-3">
                        <Label>Images from this service</Label>

                        {imagesLoading ? (
                           <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                              {Array.from({ length: 8 }).map((_, i) => (
                                 <div key={i} className="aspect-square">
                                    <Skeleton className="w-full h-full rounded-lg" />
                                 </div>
                              ))}
                           </div>
                        ) : imagesError ? (
                           <div className="text-center text-destructive py-8">
                              <p>Failed to load images</p>
                           </div>
                        ) : images.length === 0 ? (
                           <div className="text-center py-8 text-muted-foreground">
                              <ImageIcon className="w-12 h-12 mx-auto mb-2 opacity-50" />
                              <p>No images in this service yet</p>
                              <p className="text-sm">
                                 Upload images first or use a custom URL
                              </p>
                           </div>
                        ) : (
                           <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                              {images.map((image) => (
                                 <div
                                    key={image._id?.toString()}
                                    className={`relative aspect-square bg-astral-grey/50 rounded-lg overflow-hidden cursor-pointer border-2 transition-all ${
                                       selectedImageUrl === image.url
                                          ? "border-primary ring-2 ring-primary/20"
                                          : "border-transparent hover:border-border"
                                    }`}
                                    onClick={() => handleImageSelect(image.url)}
                                 >
                                    <Image
                                       src={image.url}
                                       alt={image.altText}
                                       fill
                                       className="object-cover"
                                       sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
                                    />

                                    {selectedImageUrl === image.url && (
                                       <div className="absolute inset-0 bg-primary/20 flex items-center justify-center">
                                          <div className="bg-primary text-primary-foreground rounded-full p-1">
                                             <Check className="w-4 h-4" />
                                          </div>
                                       </div>
                                    )}

                                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-2 opacity-0 hover:opacity-100 transition-opacity">
                                       <p className="text-white text-xs font-medium truncate">
                                          {image.name}
                                       </p>
                                    </div>
                                 </div>
                              ))}
                           </div>
                        )}
                     </div>
                  )}
               </div>

               <DialogFooter>
                  <Button variant="outline" onClick={handleCancel}>
                     Cancel
                  </Button>
                  <Button
                     onClick={handleSave}
                     disabled={!selectedImageUrl}
                     className="bg-gradient-accent hover:opacity-90"
                  >
                     Select Cover Image
                  </Button>
               </DialogFooter>
            </DialogContent>
         </Dialog>

         <p className="text-xs text-muted-foreground">
            Select a cover image to represent this portfolio service
         </p>
      </div>
   );
}
