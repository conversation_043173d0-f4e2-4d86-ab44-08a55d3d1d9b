"use client";

import { PaginationOptions, UpdateCollectionInput } from "@/lib/models";
import {
   createCollection,
   deleteCollection,
   getCollectionById,
   getCollections,
   getPublicCollections,
   searchCollections,
   updateCollection,
} from "@/lib/services/collection-service";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { invalidationKeys, queryKeys } from "./query-keys";

/**
 * Hook to fetch collections with pagination
 */
export function useCollections(options: PaginationOptions = {}) {
   return useQuery({
      queryKey: queryKeys.collections.list(options),
      queryFn: () => getCollections(options),
      staleTime: 5 * 60 * 1000, // 5 minutes
   });
}

/**
 * Hook to fetch a single collection by ID
 */
export function useCollection(id: string) {
   return useQuery({
      queryKey: queryKeys.collections.detail(id),
      queryFn: () => getCollectionById(id),
      enabled: !!id,
      staleTime: 10 * 60 * 1000, // 10 minutes
   });
}

/**
 * Hook to create a new collection
 */
export function useCreateCollection() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: createCollection,
      onSuccess: () => {
         // Invalidate collections list
         queryClient.invalidateQueries({
            queryKey: invalidationKeys.collectionsList(),
         });
         toast.success("Collection created successfully");
      },
      onError: (error) => {
         toast.error(
            error instanceof Error
               ? error.message
               : "Failed to create collection"
         );
      },
   });
}

/**
 * Hook to update a collection
 */
export function useUpdateCollection() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: ({
         id,
         input,
      }: {
         id: string;
         input: UpdateCollectionInput;
      }) => updateCollection(id, input),
      onSuccess: (data, variables) => {
         if (data) {
            // Update the specific collection in cache
            queryClient.setQueryData(
               queryKeys.collections.detail(variables.id),
               data
            );

            // Invalidate lists to ensure consistency
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.collectionsList(),
            });

            toast.success("Collection updated successfully");
         }
      },
      onError: (error) => {
         toast.error(
            error instanceof Error
               ? error.message
               : "Failed to update collection"
         );
      },
   });
}

/**
 * Hook to delete a collection
 */
export function useDeleteCollection() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: deleteCollection,
      onSuccess: (success) => {
         if (success) {
            // Invalidate all collection queries
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.allCollections(),
            });
            toast.success("Collection deleted successfully");
         } else {
            toast.error("Failed to delete collection");
         }
      },
      onError: (error) => {
         toast.error(
            error instanceof Error
               ? error.message
               : "Failed to delete collection"
         );
      },
   });
}

/**
 * Hook to fetch public collections only (for marketing gallery)
 */
export function usePublicCollections(options: PaginationOptions = {}) {
   return useQuery({
      queryKey: queryKeys.collections.public(options),
      queryFn: () => getPublicCollections(options),
      staleTime: 5 * 60 * 1000, // 5 minutes
   });
}

/**
 * Hook to search collections by name
 */
export function useSearchCollections(
   query: string,
   options: PaginationOptions = {}
) {
   return useQuery({
      queryKey: [...queryKeys.collections.all, "search", query, options],
      queryFn: () => searchCollections(query, options),
      enabled: !!query.trim(),
      staleTime: 2 * 60 * 1000, // 2 minutes
   });
}
