"use server";

import {
   ApiResponse,
   CreateCommentInput,
   UpdateCommentInput,
} from "@/lib/models";
import {
   createComment,
   deleteComment,
   getCommentById,
   getCommentCountByAlbum,
   getCommentsByAlbum,
   updateComment,
} from "@/lib/services/comment-service";

/**
 * Get comments for an album
 */
export async function getAlbumComments(albumId: string): Promise<ApiResponse> {
   try {
      if (!albumId) {
         return {
            success: false,
            error: "Album ID is required",
         };
      }

      const comments = await getCommentsByAlbum(albumId);

      return {
         success: true,
         data: comments,
         message: "Comments fetched successfully",
      };
   } catch (error) {
      console.error("Error fetching album comments:", error);
      return {
         success: false,
         error:
            error instanceof Error ? error.message : "Failed to fetch comments",
      };
   }
}

/**
 * Get comment count for an album
 */
export async function getAlbumCommentCount(
   albumId: string
): Promise<ApiResponse> {
   try {
      if (!albumId) {
         return {
            success: false,
            error: "Album ID is required",
         };
      }

      const count = await getCommentCountByAlbum(albumId);

      return {
         success: true,
         data: { count },
         message: "Comment count fetched successfully",
      };
   } catch (error) {
      console.error("Error fetching comment count:", error);
      return {
         success: false,
         error:
            error instanceof Error
               ? error.message
               : "Failed to fetch comment count",
      };
   }
}

/**
 * Create a new comment
 */
export async function createCommentAction(
   input: CreateCommentInput
): Promise<ApiResponse> {
   try {
      if (!input.albumId) {
         return {
            success: false,
            error: "Album ID is required",
         };
      }

      if (!input.content || input.content.trim().length === 0) {
         return {
            success: false,
            error: "Comment content is required",
         };
      }

      if (!input.authorName || input.authorName.trim().length === 0) {
         return {
            success: false,
            error: "Author name is required",
         };
      }

      const comment = await createComment(input);

      return {
         success: true,
         data: comment,
         message: "Comment created successfully",
      };
   } catch (error) {
      console.error("Error creating comment:", error);
      return {
         success: false,
         error:
            error instanceof Error ? error.message : "Failed to create comment",
      };
   }
}

/**
 * Update a comment
 */
export async function updateCommentAction(
   id: string,
   input: UpdateCommentInput
): Promise<ApiResponse> {
   try {
      if (!id) {
         return {
            success: false,
            error: "Comment ID is required",
         };
      }

      const comment = await updateComment(id, input);

      if (!comment) {
         return {
            success: false,
            error: "Comment not found",
         };
      }

      return {
         success: true,
         data: comment,
         message: "Comment updated successfully",
      };
   } catch (error) {
      console.error("Error updating comment:", error);
      return {
         success: false,
         error:
            error instanceof Error ? error.message : "Failed to update comment",
      };
   }
}

/**
 * Delete a comment
 */
export async function deleteCommentAction(id: string): Promise<ApiResponse> {
   try {
      if (!id) {
         return {
            success: false,
            error: "Comment ID is required",
         };
      }

      const deleted = await deleteComment(id);

      if (!deleted) {
         return {
            success: false,
            error: "Comment not found or already deleted",
         };
      }

      return {
         success: true,
         message: "Comment deleted successfully",
      };
   } catch (error) {
      console.error("Error deleting comment:", error);
      return {
         success: false,
         error:
            error instanceof Error ? error.message : "Failed to delete comment",
      };
   }
}

/**
 * Get comment by ID
 */
export async function getCommentAction(id: string): Promise<ApiResponse> {
   try {
      if (!id) {
         return {
            success: false,
            error: "Comment ID is required",
         };
      }

      const comment = await getCommentById(id);

      if (!comment) {
         return {
            success: false,
            error: "Comment not found",
         };
      }

      return {
         success: true,
         data: comment,
         message: "Comment fetched successfully",
      };
   } catch (error) {
      console.error("Error fetching comment:", error);
      return {
         success: false,
         error:
            error instanceof Error ? error.message : "Failed to fetch comment",
      };
   }
}
