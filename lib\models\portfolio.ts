import { ObjectId } from "mongodb";

// Portfolio Service interface for database storage
export interface PortfolioService {
   _id?: ObjectId | string;
   name: string;
   description?: string;
   slug: string; // URL-friendly identifier
   coverImageUrl?: string; // URL of the cover image for the service
   isActive: boolean;
   displayOrder: number;
   createdAt: Date;
   updatedAt: Date;
}

// Portfolio Image interface for database storage
export interface PortfolioImage {
   _id?: ObjectId | string;
   url: string;
   name: string;
   altText: string; // Generated with service name pattern
   serviceId: string; // Reference to portfolio service
   width: number;
   height: number;
   fileSize: number;
   mimeType: string;
   displayOrder: number; // Order within the service
   createdAt: Date;
}

// Portfolio Service creation input
export interface CreatePortfolioServiceInput {
   name: string;
   description?: string;
   coverImageUrl?: string;
   isActive?: boolean;
   displayOrder?: number;
}

// Portfolio Service update input
export interface UpdatePortfolioServiceInput {
   name?: string;
   description?: string;
   coverImageUrl?: string;
   isActive?: boolean;
   displayOrder?: number;
}

// Portfolio Image creation input
export interface CreatePortfolioImageInput {
   url: string;
   name: string;
   serviceId: string;
   width: number;
   height: number;
   fileSize: number;
   mimeType: string;
   displayOrder?: number;
}

// Portfolio Image update input
export interface UpdatePortfolioImageInput {
   name?: string;
   altText?: string;
   serviceId?: string;
   displayOrder?: number;
}

// Portfolio Service with stats for display
export interface PortfolioServiceWithStats extends PortfolioService {
   imageCount: number;
}

/**
 * Generate URL-friendly slug from service name
 */
export function generateSlug(name: string): string {
   return name
      .toLowerCase()
      .trim()
      .replace(/[^\w\s-]/g, "") // Remove special characters
      .replace(/[\s_-]+/g, "-") // Replace spaces and underscores with hyphens
      .replace(/^-+|-+$/g, ""); // Remove leading/trailing hyphens
}

/**
 * Generate alt text for portfolio image with service name pattern
 */
export function generatePortfolioAltText(
   serviceName: string,
   imageName?: string
): string {
   const baseText = imageName
      ? imageName.replace(/\.[^/.]+$/, "")
      : "Portfolio image";
   return `${serviceName} - ${baseText}`;
}

/**
 * Create portfolio service metadata from input
 */
export function createPortfolioServiceMetadata(
   input: CreatePortfolioServiceInput
): Omit<PortfolioService, "_id"> {
   const now = new Date();

   return {
      name: input.name,
      description: input.description,
      slug: generateSlug(input.name),
      coverImageUrl: input.coverImageUrl,
      isActive: input.isActive ?? true,
      displayOrder: input.displayOrder ?? 0,
      createdAt: now,
      updatedAt: now,
   };
}

/**
 * Create portfolio image metadata from input
 */
export function createPortfolioImageMetadata(
   input: CreatePortfolioImageInput,
   serviceName: string
): Omit<PortfolioImage, "_id"> {
   const now = new Date();

   return {
      url: input.url,
      name: input.name,
      altText: generatePortfolioAltText(serviceName, input.name),
      serviceId: input.serviceId,
      width: input.width,
      height: input.height,
      fileSize: input.fileSize,
      mimeType: input.mimeType,
      displayOrder: input.displayOrder ?? 0,
      createdAt: now,
   };
}

/**
 * Validate portfolio service input
 */
export function validatePortfolioServiceInput(
   input: CreatePortfolioServiceInput
): string[] {
   const errors: string[] = [];

   if (!input.name || typeof input.name !== "string") {
      errors.push("Name is required and must be a string");
   }

   if (input.name && input.name.trim().length === 0) {
      errors.push("Name cannot be empty");
   }

   if (input.name && input.name.length > 100) {
      errors.push("Name cannot exceed 100 characters");
   }

   if (
      input.description !== undefined &&
      typeof input.description !== "string"
   ) {
      errors.push("Description must be a string");
   }

   if (input.description && input.description.length > 500) {
      errors.push("Description cannot exceed 500 characters");
   }

   if (input.isActive !== undefined && typeof input.isActive !== "boolean") {
      errors.push("isActive must be a boolean");
   }

   if (
      input.displayOrder !== undefined &&
      (typeof input.displayOrder !== "number" || input.displayOrder < 0)
   ) {
      errors.push("displayOrder must be a non-negative number");
   }

   return errors;
}

/**
 * Validate portfolio image input
 */
export function validatePortfolioImageInput(
   input: CreatePortfolioImageInput
): string[] {
   const errors: string[] = [];

   if (!input.url || typeof input.url !== "string") {
      errors.push("URL is required and must be a string");
   }

   if (!input.name || typeof input.name !== "string") {
      errors.push("Name is required and must be a string");
   }

   if (input.name && input.name.trim().length === 0) {
      errors.push("Name cannot be empty");
   }

   if (!input.serviceId || typeof input.serviceId !== "string") {
      errors.push("Service ID is required and must be a string");
   }

   if (typeof input.width !== "number" || input.width <= 0) {
      errors.push("Width must be a positive number");
   }

   if (typeof input.height !== "number" || input.height <= 0) {
      errors.push("Height must be a positive number");
   }

   if (typeof input.fileSize !== "number" || input.fileSize <= 0) {
      errors.push("File size must be a positive number");
   }

   if (!input.mimeType || typeof input.mimeType !== "string") {
      errors.push("MIME type is required and must be a string");
   }

   // Validate MIME type is an image
   if (input.mimeType && !input.mimeType.startsWith("image/")) {
      errors.push("MIME type must be an image type");
   }

   return errors;
}
