"use server";

import { createSession, destroySession, getSession } from "@/lib/auth/session";
import {
   ApiResponse,
   FirstTimeSetupInput,
   LoginInput,
   UpdateAdminInput,
} from "@/lib/models";
import {
   createFirstAdmin,
   hasAdminAccount,
   updateAdmin,
   verifyAdminLogin,
} from "@/lib/services/auth-service";
import { redirect } from "next/navigation";

/**
 * Check if first-time setup is needed
 */
export async function checkFirstTimeSetup(): Promise<ApiResponse<{ needsSetup: boolean }>> {
   try {
      const adminExists = await hasAdminAccount();
      
      return {
         success: true,
         data: { needsSetup: !adminExists },
      };
   } catch (error) {
      console.error("Error checking first-time setup:", error);
      return {
         success: false,
         error: "Failed to check setup status",
      };
   }
}

/**
 * Perform first-time setup
 */
export async function performFirstTimeSetup(
   input: FirstTimeSetupInput
): Promise<ApiResponse> {
   try {
      // Check if admin already exists
      const adminExists = await hasAdminAccount();
      if (adminExists) {
         return {
            success: false,
            error: "Admin account already exists",
         };
      }

      // Create the first admin
      const admin = await createFirstAdmin(input);

      // Create session for the new admin
      await createSession({
         id: admin._id as string,
         name: admin.name,
         email: admin.email,
      });

      return {
         success: true,
         message: "Admin account created successfully",
      };
   } catch (error) {
      console.error("Error performing first-time setup:", error);
      return {
         success: false,
         error: error instanceof Error ? error.message : "Setup failed",
      };
   }
}

/**
 * Login action
 */
export async function loginAction(input: LoginInput): Promise<ApiResponse> {
   try {
      // Verify login credentials
      const adminSession = await verifyAdminLogin(input);

      if (!adminSession) {
         return {
            success: false,
            error: "Invalid password",
         };
      }

      // Create session
      await createSession(adminSession);

      return {
         success: true,
         message: "Login successful",
      };
   } catch (error) {
      console.error("Error during login:", error);
      return {
         success: false,
         error: "Login failed",
      };
   }
}

/**
 * Logout action
 */
export async function logoutAction(): Promise<void> {
   try {
      await destroySession();
   } catch (error) {
      console.error("Error during logout:", error);
   }
   
   // Redirect to login page
   redirect("/login");
}

/**
 * Update admin account
 */
export async function updateAdminAccount(
   input: UpdateAdminInput
): Promise<ApiResponse> {
   try {
      // Get current session
      const session = await getSession();
      if (!session) {
         return {
            success: false,
            error: "Not authenticated",
         };
      }

      // Update admin account
      const updatedAdmin = await updateAdmin(session.id, input);

      if (!updatedAdmin) {
         return {
            success: false,
            error: "Failed to update account",
         };
      }

      // Update session with new information
      await createSession({
         id: updatedAdmin._id as string,
         name: updatedAdmin.name,
         email: updatedAdmin.email,
      });

      return {
         success: true,
         message: "Account updated successfully",
         data: {
            name: updatedAdmin.name,
            email: updatedAdmin.email,
         },
      };
   } catch (error) {
      console.error("Error updating admin account:", error);
      return {
         success: false,
         error: error instanceof Error ? error.message : "Update failed",
      };
   }
}

/**
 * Get current admin session
 */
export async function getCurrentAdmin(): Promise<ApiResponse> {
   try {
      const session = await getSession();
      
      if (!session) {
         return {
            success: false,
            error: "Not authenticated",
         };
      }

      return {
         success: true,
         data: session,
      };
   } catch (error) {
      console.error("Error getting current admin:", error);
      return {
         success: false,
         error: "Failed to get admin information",
      };
   }
}
