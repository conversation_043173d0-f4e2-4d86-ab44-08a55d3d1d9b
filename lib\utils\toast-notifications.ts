/**
 * Enhanced toast notification utilities for portfolio operations
 */

import { toast } from "sonner";
import { PortfolioError, formatErrorMessage } from "./portfolio-error-handling";

export interface ToastOptions {
   duration?: number;
   onRetry?: () => void | Promise<void>;
   onDismiss?: () => void;
   persistent?: boolean;
}

export class ToastManager {
   private activeToasts = new Map<string, string | number>();

   /**
    * Show a loading toast with optional cancellation
    */
   startLoading(
      id: string,
      message: string,
      options: { onCancel?: () => void } = {}
   ): string | number {
      // Dismiss any existing toast with this ID
      this.dismiss(id);

      const toastId = toast.loading(message, {
         duration: Infinity,
         action: options.onCancel
            ? {
                 label: "Cancel",
                 onClick: options.onCancel,
              }
            : undefined,
      });

      this.activeToasts.set(id, toastId);
      return toastId;
   }

   /**
    * Show a success toast
    */
   success(
      id: string,
      message: string,
      options: ToastOptions = {}
   ): string | number {
      this.dismiss(id);

      const toastId = toast.success(message, {
         duration: options.duration || 4000,
         onDismiss: options.onDismiss,
      });

      if (!options.persistent) {
         this.activeToasts.set(id, toastId);
      }

      return toastId;
   }

   /**
    * Show an error toast with optional retry
    */
   error(
      id: string,
      error: PortfolioError | string,
      options: ToastOptions = {}
   ): string | number {
      this.dismiss(id);

      const errorMessage =
         typeof error === "string" ? error : formatErrorMessage(error);
      const isRetryable = typeof error === "object" && error.retryable;

      const toastId = toast.error(errorMessage, {
         duration: options.duration || (isRetryable ? 8000 : 6000),
         action:
            options.onRetry && isRetryable
               ? {
                    label: "Retry",
                    onClick: async () => {
                       try {
                          await options.onRetry!();
                       } catch (retryError) {
                          console.error("Retry failed:", retryError);
                       }
                    },
                 }
               : undefined,
         onDismiss: options.onDismiss,
      });

      if (!options.persistent) {
         this.activeToasts.set(id, toastId);
      }

      return toastId;
   }

   /**
    * Show a warning toast
    */
   warning(
      id: string,
      message: string,
      options: ToastOptions = {}
   ): string | number {
      this.dismiss(id);

      const toastId = toast.warning(message, {
         duration: options.duration || 5000,
         onDismiss: options.onDismiss,
      });

      if (!options.persistent) {
         this.activeToasts.set(id, toastId);
      }

      return toastId;
   }

   /**
    * Show an info toast
    */
   info(
      id: string,
      message: string,
      options: ToastOptions = {}
   ): string | number {
      this.dismiss(id);

      const toastId = toast.info(message, {
         duration: options.duration || 4000,
         onDismiss: options.onDismiss,
      });

      if (!options.persistent) {
         this.activeToasts.set(id, toastId);
      }

      return toastId;
   }

   /**
    * Update an existing loading toast to success
    */
   updateToSuccess(id: string, message: string): void {
      const toastId = this.activeToasts.get(id);
      if (toastId) {
         toast.success(message, { id: toastId });
         this.activeToasts.delete(id);
      } else {
         this.success(id, message);
      }
   }

   /**
    * Update an existing loading toast to error
    */
   updateToError(
      id: string,
      error: PortfolioError | string,
      options: ToastOptions = {}
   ): void {
      const toastId = this.activeToasts.get(id);
      if (toastId) {
         const errorMessage =
            typeof error === "string" ? error : formatErrorMessage(error);
         const isRetryable = typeof error === "object" && error.retryable;

         toast.error(errorMessage, {
            id: toastId,
            duration: options.duration || (isRetryable ? 8000 : 6000),
            action:
               options.onRetry && isRetryable
                  ? {
                       label: "Retry",
                       onClick: async () => {
                          try {
                             await options.onRetry!();
                          } catch (retryError) {
                             console.error("Retry failed:", retryError);
                          }
                       },
                    }
                  : undefined,
         });
         this.activeToasts.delete(id);
      } else {
         this.error(id, error, options);
      }
   }

   /**
    * Dismiss a specific toast
    */
   dismiss(id: string): void {
      const toastId = this.activeToasts.get(id);
      if (toastId) {
         toast.dismiss(toastId);
         this.activeToasts.delete(id);
      }
   }

   /**
    * Dismiss all toasts managed by this instance
    */
   dismissAll(): void {
      for (const [, toastId] of this.activeToasts) {
         toast.dismiss(toastId);
      }
      this.activeToasts.clear();
   }

   /**
    * Check if a toast is currently active
    */
   isActive(id: string): boolean {
      return this.activeToasts.has(id);
   }
}

/**
 * Global toast manager instance
 */
export const globalToastManager = new ToastManager();

/**
 * Utility functions for common toast patterns
 */
export const portfolioToasts = {
   /**
    * Show upload progress toast
    */
   uploadProgress(filename: string, progress: number): string | number {
      return toast.loading(
         `Uploading ${filename}... ${Math.round(progress)}%`,
         {
            duration: Infinity,
         }
      );
   },

   /**
    * Show upload success
    */
   uploadSuccess(filename: string): string | number {
      return toast.success(`Successfully uploaded ${filename}`);
   },

   /**
    * Show upload error with retry
    */
   uploadError(filename: string, onRetry?: () => void): string | number {
      return toast.error(`Failed to upload ${filename}`, {
         duration: 8000,
         action: onRetry
            ? {
                 label: "Retry",
                 onClick: onRetry,
              }
            : undefined,
      });
   },

   /**
    * Show migration progress
    */
   migrationProgress(): string | number {
      return toast.loading("Migrating portfolio data...", {
         duration: Infinity,
         description: "This may take a few moments",
      });
   },

   /**
    * Show migration success
    */
   migrationSuccess(
      servicesCreated: number,
      imagesCreated: number
   ): string | number {
      return toast.success("Migration completed successfully!", {
         description: `Created ${servicesCreated} services and ${imagesCreated} images`,
         duration: 6000,
      });
   },

   /**
    * Show migration error
    */
   migrationError(error: string, onRetry?: () => void): string | number {
      return toast.error("Migration failed", {
         description: error,
         duration: 10000,
         action: onRetry
            ? {
                 label: "Try Again",
                 onClick: onRetry,
              }
            : undefined,
      });
   },

   /**
    * Show network error with retry
    */
   networkError(operation: string, onRetry?: () => void): string | number {
      return toast.error(`Network error during ${operation}`, {
         description: "Please check your connection and try again",
         duration: 8000,
         action: onRetry
            ? {
                 label: "Retry",
                 onClick: onRetry,
              }
            : undefined,
      });
   },

   /**
    * Show validation error
    */
   validationError(message: string): string | number {
      return toast.error("Validation Error", {
         description: message,
         duration: 6000,
      });
   },

   /**
    * Show permission error
    */
   permissionError(): string | number {
      return toast.error("Permission Denied", {
         description: "You don't have permission to perform this action",
         duration: 6000,
      });
   },

   /**
    * Show rate limit error
    */
   rateLimitError(retryAfter?: number): string | number {
      const description = retryAfter
         ? `Please wait ${retryAfter} seconds before trying again`
         : "Please wait before trying again";

      return toast.error("Too Many Requests", {
         description,
         duration: 8000,
      });
   },
};

/**
 * Hook for using toast manager in React components
 */
export function useToastManager() {
   return new ToastManager();
}

/**
 * Legacy function for backward compatibility
 */
export function showErrorToast(
   error: string | PortfolioError,
   options?: {
      onRetry?: () => void;
      retryLabel?: string;
      duration?: number;
   }
) {
   const message =
      typeof error === "string" ? error : formatErrorMessage(error);
   const isRetryable = typeof error === "object" && error.retryable;

   return toast.error(message, {
      duration: options?.duration || 6000,
      action:
         options?.onRetry && (isRetryable || typeof error === "string")
            ? {
                 label: options.retryLabel || "Retry",
                 onClick: options.onRetry,
              }
            : undefined,
   });
}
