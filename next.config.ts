import type { NextConfig } from "next";

const nextConfig: NextConfig = {
   images: {
      loader: "custom",
      loaderFile: "./cloudflare-loader.ts",
      deviceSizes: [640, 750, 1080, 1200, 2048],
      imageSizes: [16, 32, 64, 128, 256],
      minimumCacheTTL: 2678400, // 31 days
      remotePatterns: [
         {
            protocol: "https",
            hostname: "picsum.photos",
         },
         {
            protocol: "https",
            hostname: "*.r2.cloudflarestorage.com",
         },
         {
            protocol: "https",
            hostname: "pub-257dba4baebe41cfa2bc4fbad3bf847a.r2.dev",
         },
         {
            protocol: "https",
            hostname: "images.astralstudios.co.uk",
         },
      ],
      // Unoptimized patterns for static assets like logos
      unoptimized: false,
   },
   experimental: {
      serverActions: {
         bodySizeLimit: "50mb",
      },
   },
};

export default nextConfig;
