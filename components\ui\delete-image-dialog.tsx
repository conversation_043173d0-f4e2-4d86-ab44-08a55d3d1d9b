"use client";

import {
   AlertDialog,
   AlertDialogAction,
   AlertDialogCancel,
   AlertDialogContent,
   AlertDialogFooter,
   AlertDialogHeader,
   AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Trash2 } from "lucide-react";

interface DeleteImageDialogProps {
   open: boolean;
   onOpenChange: (open: boolean) => void;
   imageName: string;
   onConfirm: () => Promise<void>;
   isDeleting?: boolean;
}

export default function DeleteImageDialog({
   open,
   onOpenChange,
   imageName,
   onConfirm,
   isDeleting = false,
}: DeleteImageDialogProps) {
   const handleConfirm = async (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();

      try {
         await onConfirm();
         onOpenChange(false);
      } catch (error) {
         // Error handling is done in the mutation hook
         console.error("Error during delete:", error);
      }
   };

   const handleOpenChange = (newOpen: boolean) => {
      // Prevent closing the dialog when it's in a loading state
      if (isDeleting && !newOpen) {
         return;
      }
      onOpenChange(newOpen);
   };

   return (
      <AlertDialog open={open} onOpenChange={handleOpenChange}>
         <AlertDialogContent>
            <AlertDialogHeader>
               <div className="flex items-center space-x-2">
                  <div className="flex h-10 w-10 items-center justify-center rounded-full bg-destructive/10">
                     <Trash2 className="h-5 w-5 text-destructive" />
                  </div>
                  <div>
                     <AlertDialogTitle>Delete Image</AlertDialogTitle>
                  </div>
               </div>
               <div className="text-left text-sm space-y-1 leading-relaxed">
                  <div>
                     Are you sure you want to delete{" "}
                     <strong>&quot;{imageName}&quot;</strong>?
                  </div>
                  <div>
                     This action cannot be undone and the image will be
                     permanently removed from both the database and storage.
                  </div>
               </div>
            </AlertDialogHeader>
            <AlertDialogFooter>
               <AlertDialogCancel disabled={isDeleting}>
                  Cancel
               </AlertDialogCancel>
               <AlertDialogAction
                  onClick={handleConfirm}
                  disabled={isDeleting}
                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
               >
                  {isDeleting ? (
                     <div className="flex items-center space-x-2">
                        <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                        <span>Deleting...</span>
                     </div>
                  ) : (
                     "Delete Image"
                  )}
               </AlertDialogAction>
            </AlertDialogFooter>
         </AlertDialogContent>
      </AlertDialog>
   );
}
