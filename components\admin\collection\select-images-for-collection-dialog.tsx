"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import {
   <PERSON><PERSON>,
   DialogContent,
   <PERSON><PERSON>Footer,
   <PERSON><PERSON>Header,
   DialogTitle,
} from "@/components/ui/dialog";
import { Skeleton } from "@/components/ui/skeleton";
import { useCollections } from "@/lib/hooks/use-collections";
import { useImages, useUpdateImageCollections } from "@/lib/hooks/use-images";
import type { CollectionWithStats } from "@/lib/models/collection";
import type { Image as ImageType } from "@/lib/models/image";
import Image from "next/image";
import { useEffect, useMemo, useState } from "react";

interface SelectImagesForCollectionDialogProps {
   open: boolean;
   onOpenChange: (open: boolean) => void;
   collection: CollectionWithStats;
   onSuccess?: () => void;
}

export default function SelectImagesForCollectionDialog({
   open,
   onOpenChange,
   collection,
   onSuccess,
}: SelectImagesForCollectionDialogProps) {
   // Fetch all images (first 100 for now)
   const { data: imagesData, isLoading: imagesLoading } = useImages({
      page: 1,
      limit: 100,
   });
   const { data: collectionsData } = useCollections({ page: 1, limit: 100 });
   const updateCollectionsMutation = useUpdateImageCollections();

   const images: ImageType[] = useMemo(
      () => imagesData?.data || [],
      [imagesData]
   );
   const allCollections: CollectionWithStats[] = collectionsData?.data || [];

   // Set of selected image IDs
   const [selected, setSelected] = useState<Set<string>>(new Set());

   // Pre-select images already in this collection
   useEffect(() => {
      if (open && images.length > 0) {
         setSelected(
            new Set(
               images
                  .filter((img) =>
                     img.collectionIds?.includes(collection._id as string)
                  )
                  .map((img) => img._id as string)
            )
         );
      }
   }, [open, images, collection._id]);

   // Toggle selection
   const toggleImage = (imageId: string) => {
      setSelected((prev) => {
         const newSet = new Set(prev);
         if (newSet.has(imageId)) {
            newSet.delete(imageId);
         } else {
            newSet.add(imageId);
         }
         return newSet;
      });
   };

   // Save handler
   const handleSave = async () => {
      // For each image, update its collectionIds to include or exclude this collection
      const promises = images.map((img) => {
         const isSelected = selected.has(img._id as string);
         const alreadyIn = img.collectionIds?.includes(
            collection._id as string
         );
         if (isSelected && !alreadyIn) {
            // Add collection
            return updateCollectionsMutation.mutateAsync({
               imageId: img._id as string,
               collectionIds: [
                  ...(img.collectionIds || []),
                  collection._id as string,
               ],
            });
         } else if (!isSelected && alreadyIn) {
            // Remove collection
            return updateCollectionsMutation.mutateAsync({
               imageId: img._id as string,
               collectionIds: (img.collectionIds || []).filter(
                  (cid) => cid !== collection._id
               ),
            });
         }
         return Promise.resolve();
      });
      await Promise.all(promises);
      if (onSuccess) onSuccess();
      onOpenChange(false);
   };

   return (
      <Dialog open={open} onOpenChange={onOpenChange}>
         <DialogContent className="!max-w-5xl">
            <DialogHeader>
               <DialogTitle>Select Images for Collection</DialogTitle>
            </DialogHeader>
            <div className="mb-4">
               <p className="text-muted-foreground text-sm">
                  Select images to add or remove from{" "}
                  <span className="font-semibold">{collection.name}</span>.
               </p>
            </div>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 max-h-[60vh] overflow-y-auto">
               {imagesLoading
                  ? Array.from({ length: 12 }).map((_, i) => (
                       <Card key={i} className="h-48">
                          <CardContent className="p-0 h-full flex items-center justify-center">
                             <Skeleton className="w-full h-full" />
                          </CardContent>
                       </Card>
                    ))
                  : images.map((img) => {
                       const isChecked = selected.has(img._id as string);
                       const imgCollections = allCollections.filter((c) =>
                          img.collectionIds?.includes(c._id as string)
                       );
                       return (
                          <Card
                             key={img._id}
                             className="relative h-48 group py-0 overflow-hidden cursor-pointer"
                             tabIndex={0}
                             role="checkbox"
                             aria-checked={isChecked}
                             onClick={() => toggleImage(img._id as string)}
                             onKeyDown={(e) => {
                                if (e.key === " " || e.key === "Enter") {
                                   e.preventDefault();
                                   toggleImage(img._id as string);
                                }
                             }}
                          >
                             <CardContent className="p-0 h-full relative">
                                <div
                                   className="absolute top-2 right-2 z-10"
                                   onClick={(e) => e.stopPropagation()}
                                >
                                   <Checkbox
                                      checked={isChecked}
                                      onCheckedChange={() =>
                                         toggleImage(img._id as string)
                                      }
                                      tabIndex={-1}
                                      className="size-4 rounded-full"
                                   />
                                </div>
                                <Image
                                   src={img.url as string}
                                   alt={img.name}
                                   fill
                                   className="object-cover"
                                />
                                <div className="p-2 absolute bottom-0 left-0 right-0">
                                   <div className="relative z-10">
                                      <div className="flex flex-wrap gap-1 mt-1">
                                         {imgCollections.map((c) => (
                                            <span
                                               key={c._id as string}
                                               className="bg-primary/70 font-bold text-white/80 text-[10px] px-2 py-0.5 rounded-full"
                                            >
                                               {c.name}
                                            </span>
                                         ))}
                                      </div>
                                   </div>
                                </div>
                             </CardContent>
                          </Card>
                       );
                    })}
            </div>
            <DialogFooter className="mt-6 flex flex-row justify-end">
               <Button
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                  disabled={updateCollectionsMutation.isPending}
               >
                  Cancel
               </Button>
               <Button
                  onClick={handleSave}
                  disabled={updateCollectionsMutation.isPending}
                  className="bg-gradient-accent hover:opacity-90"
               >
                  {updateCollectionsMutation.isPending ? (
                     <>
                        <span className="mr-2">Saving</span>
                        <span className="animate-spin inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full" />
                     </>
                  ) : (
                     "Save"
                  )}
               </Button>
            </DialogFooter>
         </DialogContent>
      </Dialog>
   );
}
