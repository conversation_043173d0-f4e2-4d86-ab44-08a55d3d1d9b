"use client";

import CreateAlbumDialog from "@/components/admin/album/create-album-dialog";
import {
   AlertDialog,
   AlertDialogCancel,
   AlertDialogContent,
   AlertDialogDescription,
   AlertDialogFooter,
   AlertDialogHeader,
   AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button, buttonVariants } from "@/components/ui/button";
import {
   Card,
   CardContent,
   CardDescription,
   CardHeader,
   CardTitle,
} from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import {
   DropdownMenu,
   DropdownMenuContent,
   DropdownMenuItem,
   DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Label } from "@/components/ui/label";
import { Skeleton } from "@/components/ui/skeleton";
import { useAlbums, useDeleteAlbum } from "@/lib/hooks/use-albums";
import { bulkDeleteImagesAndFiles } from "@/lib/services/image-service";
import { LockClosedIcon, TrashIcon } from "@heroicons/react/24/solid";
import { Album, Loader2, MoreVertical } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";

export default function AlbumsPage() {
   const { data: albumsData, isLoading, error } = useAlbums();
   const deleteAlbumMutation = useDeleteAlbum();
   const [deleteDialog, setDeleteDialog] = useState({
      open: false,
      albumId: "",
      albumName: "",
      imageCount: 0,
   });
   const [deleteImagesFromStorage, setDeleteImagesFromStorage] =
      useState(false);
   const [isDeletingImages, setIsDeletingImages] = useState(false);
   const [dropdownOpen, setDropdownOpen] = useState<{
      [albumId: string]: boolean;
   }>({});

   // Close dialog only after successful deletion
   useEffect(() => {
      if (deleteAlbumMutation.isSuccess && deleteDialog.open) {
         setDeleteDialog({
            open: false,
            albumId: "",
            albumName: "",
            imageCount: 0,
         });
         setDeleteImagesFromStorage(false);
         setIsDeletingImages(false);
         deleteAlbumMutation.reset();
      }
   }, [deleteAlbumMutation.isSuccess, deleteDialog.open, deleteAlbumMutation]);

   const handleDeleteAlbum = async () => {
      if (deleteImagesFromStorage && deleteDialog.imageCount > 0) {
         setIsDeletingImages(true);
         try {
            // First, get all images in the album
            const { getImages } = await import("@/lib/services/image-service");
            const imagesResponse = await getImages({
               albumId: deleteDialog.albumId,
               limit: 1000,
            });
            const imageIds = imagesResponse.data.map((img) => img._id!);

            if (imageIds.length > 0) {
               // Delete images from database and R2 storage
               await bulkDeleteImagesAndFiles(imageIds);
            }
         } catch (error) {
            console.error("Error deleting images:", error);
            // Continue with album deletion even if image deletion fails
         } finally {
            setIsDeletingImages(false);
         }
      }

      // Delete the album
      deleteAlbumMutation.mutate(deleteDialog.albumId);
   };

   if (error) {
      return (
         <div className="p-8">
            <div className="text-center text-destructive">
               <p>Failed to load albums. Please try again.</p>
            </div>
         </div>
      );
   }

   const albums = albumsData?.data || [];
   const hasAlbums = albums.length > 0;

   return (
      <div className="p-8 space-y-8">
         {/* Header */}
         <div className="flex flex-col sm:flex-row sm:items-center justify-between">
            <div className="flex items-center space-x-4">
               <div>
                  <h1 className="text-2xl sm:text-3xl font-bold text-foreground">
                     Albums
                  </h1>
                  <p className="text-muted-foreground">
                     Organize and manage your photo albums
                  </p>
               </div>
            </div>

            <div className="flex items-center space-x-3 mt-4 sm:mt-0">
               <CreateAlbumDialog showTrigger={true} />
            </div>
         </div>

         {/* Main Content */}
         {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
               {Array.from({ length: 8 }).map((_, i) => (
                  <Card key={i} className="border-border/30 py-0">
                     <CardContent className="p-0">
                        <Skeleton className="w-full h-48 rounded-t-lg" />
                        <div className="p-4 space-y-2">
                           <Skeleton className="h-5 w-3/4" />
                           <Skeleton className="h-4 w-1/2" />
                        </div>
                     </CardContent>
                  </Card>
               ))}
            </div>
         ) : hasAlbums ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
               {albums.map((album) => (
                  <Card
                     key={album._id?.toString()}
                     className="border-border/30 border-none bg-astral-grey/90 group hover:shadow-lg transition-shadow py-0 relative"
                  >
                     {/* Dropdown menu (not inside the link) */}
                     <div
                        className="absolute top-2 right-2 group-hover:opacity-100 transition-opacity z-10"
                        onClick={(e) => e.stopPropagation()}
                     >
                        <DropdownMenu
                           open={
                              dropdownOpen[album._id?.toString() || ""] || false
                           }
                           onOpenChange={(open) =>
                              setDropdownOpen((prev) => ({
                                 ...prev,
                                 [album._id?.toString() || ""]: open,
                              }))
                           }
                        >
                           <DropdownMenuTrigger asChild>
                              <Button
                                 variant="outline"
                                 size="sm"
                                 className="h-8 w-8 p-0 bg-background/80 border-none rounded-lg backdrop-blur-sm hover:bg-background/90"
                                 onClick={(e) => e.stopPropagation()}
                              >
                                 <MoreVertical className="w-4 h-4" />
                              </Button>
                           </DropdownMenuTrigger>
                           <DropdownMenuContent
                              align="end"
                              onClick={(e) => e.stopPropagation()}
                           >
                              <DropdownMenuItem
                                 className="group/delete-btn text-destructive font-semibold hover:!text-white"
                                 onClick={() => {
                                    setDropdownOpen((prev) => ({
                                       ...prev,
                                       [album._id?.toString() || ""]: false,
                                    }));
                                    setDeleteDialog({
                                       open: true,
                                       albumId: album._id?.toString() || "",
                                       albumName: album.name,
                                       imageCount: album.imageCount || 0,
                                    });
                                 }}
                              >
                                 <TrashIcon className="w-4 h-4 transition-colors" />{" "}
                                 Delete Album
                              </DropdownMenuItem>
                           </DropdownMenuContent>
                        </DropdownMenu>
                     </div>
                     {/* Make the card content a link */}
                     <Link
                        href={`/admin/albums/${album._id}`}
                        className="block group/card focus:outline-none focus:ring-primary rounded-lg"
                     >
                        <CardContent className="p-0 cursor-pointer">
                           <div className="relative">
                              {/* Album Cover */}
                              <div className="w-full h-48 bg-astral-grey-light/50 rounded-t-lg flex items-center justify-center">
                                 {album.coverImageUrl ? (
                                    <Image
                                       src={album.coverImageUrl}
                                       alt={album.name}
                                       fill
                                       className="object-cover rounded-t-lg"
                                    />
                                 ) : (
                                    <Album className="w-12 h-12 text-muted-foreground" />
                                 )}
                              </div>
                           </div>
                           <div className="p-4">
                              <h3 className="font-semibold text-foreground transition-colors line-clamp-1">
                                 {album.name}
                              </h3>
                              <div className="flex items-center justify-between mt-1 text-sm text-muted-foreground">
                                 <span>{album.imageCount} photos</span>
                                 <span>
                                    {album.hasPassword && (
                                       <LockClosedIcon className="w-4 h-4" />
                                    )}
                                 </span>
                              </div>
                           </div>
                        </CardContent>
                     </Link>
                  </Card>
               ))}
            </div>
         ) : (
            <Card className="border-border/30 py-0">
               <CardHeader>
                  <CardTitle className="text-foreground">
                     Album Management
                  </CardTitle>
                  <CardDescription>
                     Create, edit, and organize albums for your photography
                     collections
                  </CardDescription>
               </CardHeader>
               <CardContent>
                  <div className="flex items-center justify-center h-64 text-muted-foreground">
                     <div className="text-center">
                        <Album className="w-16 h-16 mx-auto mb-4 opacity-50" />
                        <h3 className="text-lg font-medium mb-2">
                           No albums yet
                        </h3>
                        <p className="text-sm mb-4">
                           Create your first album to start organizing your
                           photos
                        </p>
                        <CreateAlbumDialog />
                     </div>
                  </div>
               </CardContent>
            </Card>
         )}

         {/* Delete Album Dialog */}
         <AlertDialog
            open={deleteDialog.open}
            onOpenChange={(open) => {
               if (!open) {
                  setDeleteDialog({
                     open: false,
                     albumId: "",
                     albumName: "",
                     imageCount: 0,
                  });
                  setDeleteImagesFromStorage(false);
                  setIsDeletingImages(false);
                  deleteAlbumMutation.reset();
               } else {
                  setDeleteDialog((prev) => ({ ...prev, open: true }));
               }
            }}
         >
            <AlertDialogContent>
               <AlertDialogHeader>
                  <AlertDialogTitle>Delete Album</AlertDialogTitle>
                  <AlertDialogDescription className="space-y-3">
                     <span className="space-y-1 block">
                        <span className="block">
                           Are you sure you want to delete the album{" "}
                           <span className="font-semibold">
                              {deleteDialog.albumName}
                           </span>
                           ?
                        </span>
                        <span className="block">
                           This will also remove the album from all images that
                           belong to it.
                        </span>
                        <span className="block font-bold text-destructive">
                           This action cannot be undone.
                        </span>
                     </span>
                  </AlertDialogDescription>
                  <div>
                     {deleteDialog.imageCount > 0 && (
                        <span className="flex items-center space-x-2 pt-2">
                           <Checkbox
                              id="delete-images-storage"
                              checked={deleteImagesFromStorage}
                              onCheckedChange={(checked) =>
                                 setDeleteImagesFromStorage(checked as boolean)
                              }
                           />
                           <Label
                              htmlFor="delete-images-storage"
                              className="text-sm"
                           >
                              Also delete all {deleteDialog.imageCount} images
                              from the album
                           </Label>
                        </span>
                     )}
                  </div>
               </AlertDialogHeader>
               <AlertDialogFooter>
                  <AlertDialogCancel
                     onClick={() => {
                        setDeleteDialog({
                           open: false,
                           albumId: "",
                           albumName: "",
                           imageCount: 0,
                        });
                        setDeleteImagesFromStorage(false);
                        setIsDeletingImages(false);
                        deleteAlbumMutation.reset();
                     }}
                     disabled={
                        deleteAlbumMutation.isPending || isDeletingImages
                     }
                  >
                     Cancel
                  </AlertDialogCancel>
                  <button
                     type="button"
                     className={buttonVariants({
                        className: "bg-destructive text-white",
                     })}
                     disabled={
                        deleteAlbumMutation.isPending || isDeletingImages
                     }
                     onClick={handleDeleteAlbum}
                  >
                     {deleteAlbumMutation.isPending || isDeletingImages ? (
                        <>
                           <Loader2 className="w-4 h-4 animate-spin " />{" "}
                           {isDeletingImages
                              ? "Deleting images..."
                              : "Deleting..."}
                        </>
                     ) : (
                        "Delete"
                     )}
                  </button>
               </AlertDialogFooter>
            </AlertDialogContent>
         </AlertDialog>
      </div>
   );
}
