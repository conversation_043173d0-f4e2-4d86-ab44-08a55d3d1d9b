"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, RefreshCw } from "lucide-react";
import React, { Component, ErrorInfo, ReactNode } from "react";

interface Props {
   children: ReactNode;
   fallback?: ReactNode;
   onError?: (error: Error, errorInfo: ErrorInfo) => void;
   resetKeys?: Array<string | number>;
   resetOnPropsChange?: boolean;
}

interface State {
   hasError: boolean;
   error: Error | null;
   errorInfo: ErrorInfo | null;
}

export class ErrorBoundary extends Component<Props, State> {
   private resetTimeoutId: number | null = null;

   constructor(props: Props) {
      super(props);
      this.state = { hasError: false, error: null, errorInfo: null };
   }

   static getDerivedStateFromError(error: Error): State {
      return {
         hasError: true,
         error,
         errorInfo: null,
      };
   }

   componentDidCatch(error: Error, errorInfo: ErrorInfo) {
      this.setState({
         error,
         errorInfo,
      });

      // Call the onError callback if provided
      if (this.props.onError) {
         this.props.onError(error, errorInfo);
      }

      // Log error to console in development
      if (process.env.NODE_ENV === "development") {
         console.error("ErrorBoundary caught an error:", error, errorInfo);
      }
   }

   componentDidUpdate(prevProps: Props) {
      const { resetKeys, resetOnPropsChange } = this.props;
      const { hasError } = this.state;

      // Reset error state if resetKeys have changed
      if (
         hasError &&
         prevProps.resetKeys !== resetKeys &&
         resetKeys &&
         resetKeys.length > 0
      ) {
         const hasResetKeyChanged = resetKeys.some(
            (key, index) => prevProps.resetKeys?.[index] !== key
         );

         if (hasResetKeyChanged) {
            this.resetErrorBoundary();
         }
      }

      // Reset error state if resetOnPropsChange is true and props have changed
      if (
         hasError &&
         resetOnPropsChange &&
         prevProps.children !== this.props.children
      ) {
         this.resetErrorBoundary();
      }
   }

   resetErrorBoundary = () => {
      // Clear any existing timeout
      if (this.resetTimeoutId) {
         clearTimeout(this.resetTimeoutId);
      }

      this.setState({
         hasError: false,
         error: null,
         errorInfo: null,
      });
   };

   handleRetry = () => {
      this.resetErrorBoundary();
   };

   render() {
      if (this.state.hasError) {
         // Use custom fallback if provided
         if (this.props.fallback) {
            return this.props.fallback;
         }

         // Default error UI
         return (
            <div className="flex flex-col items-center justify-center min-h-[400px] p-8 text-center">
               <div className="max-w-md mx-auto">
                  <AlertTriangle className="w-16 h-16 text-red-500 mx-auto mb-4" />
                  <h2 className="text-2xl font-bold text-foreground mb-2">
                     Something went wrong
                  </h2>
                  <p className="text-muted-foreground mb-6">
                     We encountered an unexpected error. Please try refreshing
                     the page or contact support if the problem persists.
                  </p>

                  {process.env.NODE_ENV === "development" &&
                     this.state.error && (
                        <details className="mb-6 text-left">
                           <summary className="cursor-pointer text-sm font-medium text-muted-foreground mb-2">
                              Error Details (Development)
                           </summary>
                           <div className="bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg p-4 text-sm">
                              <p className="font-medium text-red-800 dark:text-red-200 mb-2">
                                 {this.state.error.name}:{" "}
                                 {this.state.error.message}
                              </p>
                              {this.state.error.stack && (
                                 <pre className="text-xs text-red-700 dark:text-red-300 overflow-auto">
                                    {this.state.error.stack}
                                 </pre>
                              )}
                           </div>
                        </details>
                     )}

                  <div className="flex flex-col sm:flex-row gap-3 justify-center">
                     <Button
                        onClick={this.handleRetry}
                        className="flex items-center gap-2"
                     >
                        <RefreshCw className="w-4 h-4" />
                        Try Again
                     </Button>
                     <Button
                        variant="outline"
                        onClick={() => window.location.reload()}
                        className="flex items-center gap-2"
                     >
                        Refresh Page
                     </Button>
                  </div>
               </div>
            </div>
         );
      }

      return this.props.children;
   }
}

// Hook version for functional components
export function useErrorBoundary() {
   const [error, setError] = React.useState<Error | null>(null);

   const resetError = React.useCallback(() => {
      setError(null);
   }, []);

   const captureError = React.useCallback((error: Error) => {
      setError(error);
   }, []);

   React.useEffect(() => {
      if (error) {
         throw error;
      }
   }, [error]);

   return { captureError, resetError };
}

// Portfolio-specific error boundary with custom styling
interface PortfolioErrorBoundaryProps {
   children: ReactNode;
   title?: string;
   description?: string;
   onRetry?: () => void;
}

export function PortfolioErrorBoundary({
   children,
   title = "Portfolio Error",
   description = "There was an error loading the portfolio content.",
   onRetry,
}: PortfolioErrorBoundaryProps) {
   return (
      <ErrorBoundary
         fallback={
            <div className="flex flex-col items-center justify-center min-h-[300px] p-8 text-center bg-astral-grey rounded-lg">
               <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
               <h3 className="text-xl font-bold text-foreground mb-2">
                  {title}
               </h3>
               <p className="text-muted-foreground mb-6 max-w-md">
                  {description}
               </p>
               <div className="flex gap-3">
                  <Button
                     onClick={onRetry || (() => window.location.reload())}
                     className="flex items-center gap-2"
                  >
                     <RefreshCw className="w-4 h-4" />
                     Try Again
                  </Button>
               </div>
            </div>
         }
      >
         {children}
      </ErrorBoundary>
   );
}
