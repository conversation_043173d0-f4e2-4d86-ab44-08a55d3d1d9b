import { z } from "zod";

/**
 * Schema for creating a new comment
 */
export const createCommentSchema = z.object({
   albumId: z.string().min(1, "Album ID is required"),
   parentId: z.string().optional(),
   content: z
      .string()
      .min(1, "Comment content is required")
      .max(1000, "Comment must be less than 1000 characters")
      .trim(),
   authorName: z
      .string()
      .min(1, "Name is required")
      .max(100, "Name must be less than 100 characters")
      .trim(),
   authorEmail: z
      .string()
      .email("Please enter a valid email address")
      .optional()
      .or(z.literal("")),
});

/**
 * Schema for updating a comment
 */
export const updateCommentSchema = z.object({
   content: z
      .string()
      .min(1, "Comment content is required")
      .max(1000, "Comment must be less than 1000 characters")
      .trim()
      .optional(),
   authorName: z
      .string()
      .min(1, "Name is required")
      .max(100, "Name must be less than 100 characters")
      .trim()
      .optional(),
   authorEmail: z
      .string()
      .email("Please enter a valid email address")
      .optional()
      .or(z.literal("")),
   isApproved: z.boolean().optional(),
   adminResponse: z
      .string()
      .max(1000, "Admin response must be less than 1000 characters")
      .trim()
      .optional(),
   deletedAt: z.date().optional(),
});

/**
 * Type inference for create comment form
 */
export type CreateCommentFormData = z.infer<typeof createCommentSchema>;

/**
 * Schema for admin comment response
 */
export const adminCommentSchema = z.object({
   adminResponse: z
      .string()
      .min(1, "Admin response is required")
      .max(1000, "Admin response must be less than 1000 characters")
      .trim(),
});

/**
 * Schema for admin comment filters
 */
export const adminCommentFiltersSchema = z.object({
   showDeleted: z.boolean().optional(),
   searchQuery: z.string().optional(),
   limit: z.number().min(1).max(100).optional(),
   offset: z.number().min(0).optional(),
});

/**
 * Type inference for update comment form
 */
export type UpdateCommentFormData = z.infer<typeof updateCommentSchema>;

/**
 * Type inference for admin comment form
 */
export type AdminCommentFormData = z.infer<typeof adminCommentSchema>;

/**
 * Type inference for admin comment filters
 */
export type AdminCommentFiltersData = z.infer<typeof adminCommentFiltersSchema>;
