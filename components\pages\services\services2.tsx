import PortfolioGrid from "@/components/portfolio-grid";
import Image from "next/image";
import Link from "next/link";

// Sample images for each service category
const weddingImages = [
   {
      id: "w1",
      src: "/images/wedding-shoots/wedding-shoot-1.JPG",
      alt: "Wedding ceremony",
      category: "wedding",
   },
   {
      id: "w2",
      src: "/images/wedding-shoots/wedding-shoot-2.PNG",
      alt: "Wedding reception",
      category: "wedding",
   },
   {
      id: "w3",
      src: "/images/wedding-shoots/wedding-shoot-3.JPG",
      alt: "Wedding portraits",
      category: "wedding",
   },
   {
      id: "w4",
      src: "/images/wedding-shoots/wedding-shoot-4.JPG",
      alt: "Wedding details",
      category: "wedding",
   },
];

const preWeddingImages = [
   {
      id: "pw1",
      src: "/images/pre-wedding-shoots/pre-wedding-shoot-1.JPG",
      alt: "Pre-wedding shoot",
      category: "pre-wedding",
   },
   {
      id: "pw2",
      src: "/images/pre-wedding-shoots/pre-wedding-shoot-2.JPG",
      alt: "Engagement session",
      category: "pre-wedding",
   },
   {
      id: "pw3",
      src: "/images/pre-wedding-shoots/pre-wedding-shoot-3.JP<PERSON>",
      alt: "Couple portraits",
      category: "pre-wedding",
   },
   {
      id: "pw4",
      src: "/images/pre-wedding-shoots/pre-wedding-shoot-4.JPG",
      alt: "Romantic session",
      category: "pre-wedding",
   },
];

const pregnancyImages = [
   {
      id: "p1",
      src: "/images/pregnancy-shoots/pregnancy-shoot-1.jpg",
      alt: "Maternity session",
      category: "pregnancy",
   },
   {
      id: "p2",
      src: "/images/pregnancy-shoots/pregnancy-shoot-2.JPG",
      alt: "Pregnancy portraits",
      category: "pregnancy",
   },
   {
      id: "p3",
      src: "/images/pregnancy-shoots/pregnancy-shoot-3.jpg",
      alt: "Expecting couple",
      category: "pregnancy",
   },
   {
      id: "p4",
      src: "/images/pregnancy-shoots/pregnancy-shoot-4.jpg",
      alt: "Maternity photography",
      category: "pregnancy",
   },
];

const childDedicationImages = [
   {
      id: "cd1",
      src: "/images/child-dedication/child-dedication-1.JPG",
      alt: "Child dedication ceremony",
      category: "child-dedication",
   },
];

export default function ServicesPage() {
   return (
      <div className="min-h-screen">
         {/* Hero Section */}
         <section className="relative py-20 bg-gradient-to-r from-black to-gray-900 text-white">
            <div className="max-w-7xl mx-auto px-6 lg:px-8 text-center">
               <h1 className="text-4xl md:text-6xl font-serif font-bold mb-6">
                  Our Services
               </h1>
               <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                  Professional photography and videography services tailored to
                  capture your most precious moments
               </p>
            </div>
         </section>

         {/* Photography Services */}
         <section className="py-20 bg-white">
            <div className="max-w-7xl mx-auto px-6 lg:px-8">
               <div className="text-center mb-16">
                  <h2 className="text-3xl md:text-4xl font-serif font-bold text-gray-900 mb-4">
                     Photography Services
                  </h2>
                  <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                     Capturing life&apos;s most beautiful moments with artistic
                     vision and professional expertise
                  </p>
               </div>

               {/* Wedding Photography */}
               <div id="wedding-photography" className="mb-20">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-12">
                     <div>
                        <h3 className="text-2xl md:text-3xl font-serif font-bold text-gray-900 mb-4">
                           Wedding Photography
                        </h3>
                        <p className="text-lg text-gray-700 mb-6 leading-relaxed">
                           Your wedding day is one of the most important days of
                           your life. Our wedding photography service ensures
                           every precious moment is captured with artistry and
                           emotion. From the intimate getting-ready moments to
                           the grand celebration, we document your love story as
                           it unfolds.
                        </p>
                        <div className="space-y-3 mb-6">
                           <div className="flex items-center">
                              <svg
                                 className="w-5 h-5 text-primary mr-3"
                                 fill="currentColor"
                                 viewBox="0 0 20 20"
                              >
                                 <path
                                    fillRule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clipRule="evenodd"
                                 />
                              </svg>
                              <span>
                                 Full-day wedding coverage (up to 12 hours)
                              </span>
                           </div>
                           <div className="flex items-center">
                              <svg
                                 className="w-5 h-5 text-primary mr-3"
                                 fill="currentColor"
                                 viewBox="0 0 20 20"
                              >
                                 <path
                                    fillRule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clipRule="evenodd"
                                 />
                              </svg>
                              <span>
                                 Professional editing and color correction
                              </span>
                           </div>
                           <div className="flex items-center">
                              <svg
                                 className="w-5 h-5 text-primary mr-3"
                                 fill="currentColor"
                                 viewBox="0 0 20 20"
                              >
                                 <path
                                    fillRule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clipRule="evenodd"
                                 />
                              </svg>
                              <span>Online gallery for easy sharing</span>
                           </div>
                           <div className="flex items-center">
                              <svg
                                 className="w-5 h-5 text-primary mr-3"
                                 fill="currentColor"
                                 viewBox="0 0 20 20"
                              >
                                 <path
                                    fillRule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clipRule="evenodd"
                                 />
                              </svg>
                              <span>High-resolution images for printing</span>
                           </div>
                        </div>
                        <Link
                           href="/contact"
                           className="inline-block bg-primary hover:bg-dark text-white font-semibold px-8 py-3 rounded-full transition-colors duration-200"
                        >
                           Book Wedding Photography
                        </Link>
                     </div>
                     <div className="relative">
                        <Image
                           src="/images/wedding-shoots/wedding-shoot-5.JPG"
                           alt="Wedding Photography"
                           width={600}
                           height={400}
                           className="rounded-lg shadow-lg"
                        />
                     </div>
                  </div>
                  <PortfolioGrid
                     items={weddingImages}
                     showFilter={false}
                     columns={4}
                  />
               </div>

               {/* Pre-wedding Shoots */}
               <div id="pre-wedding-shoots" className="mb-20">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-12">
                     <div className="order-2 lg:order-1 relative">
                        <Image
                           src="/images/pre-wedding-shoots/pre-wedding-shoot-5.JPG"
                           alt="Pre-wedding Photography"
                           width={600}
                           height={400}
                           className="rounded-lg shadow-lg"
                        />
                     </div>
                     <div className="order-1 lg:order-2">
                        <h3 className="text-2xl md:text-3xl font-serif font-bold text-gray-900 mb-4">
                           Pre-wedding Shoots
                        </h3>
                        <p className="text-lg text-gray-700 mb-6 leading-relaxed">
                           Celebrate your engagement with a romantic pre-wedding
                           photoshoot. These sessions are perfect for getting
                           comfortable with your photographer before the big day
                           and creating beautiful images for save-the-dates or
                           wedding invitations.
                        </p>
                        <div className="space-y-3 mb-6">
                           <div className="flex items-center">
                              <svg
                                 className="w-5 h-5 text-primary mr-3"
                                 fill="currentColor"
                                 viewBox="0 0 20 20"
                              >
                                 <path
                                    fillRule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clipRule="evenodd"
                                 />
                              </svg>
                              <span>Multiple location options</span>
                           </div>
                           <div className="flex items-center">
                              <svg
                                 className="w-5 h-5 text-primary mr-3"
                                 fill="currentColor"
                                 viewBox="0 0 20 20"
                              >
                                 <path
                                    fillRule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clipRule="evenodd"
                                 />
                              </svg>
                              <span>Outfit changes and styling guidance</span>
                           </div>
                           <div className="flex items-center">
                              <svg
                                 className="w-5 h-5 text-primary mr-3"
                                 fill="currentColor"
                                 viewBox="0 0 20 20"
                              >
                                 <path
                                    fillRule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clipRule="evenodd"
                                 />
                              </svg>
                              <span>Creative concepts and poses</span>
                           </div>
                           <div className="flex items-center">
                              <svg
                                 className="w-5 h-5 text-primary mr-3"
                                 fill="currentColor"
                                 viewBox="0 0 20 20"
                              >
                                 <path
                                    fillRule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clipRule="evenodd"
                                 />
                              </svg>
                              <span>Relaxed and fun atmosphere</span>
                           </div>
                        </div>
                        <Link
                           href="/contact"
                           className="inline-block bg-primary hover:bg-dark text-white font-semibold px-8 py-3 rounded-full transition-colors duration-200"
                        >
                           Book Pre-wedding Shoot
                        </Link>
                     </div>
                  </div>
                  <PortfolioGrid
                     items={preWeddingImages}
                     showFilter={false}
                     columns={4}
                  />
               </div>

               {/* Pregnancy Shoots */}
               <div id="pregnancy-shoots" className="mb-20">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-12">
                     <div>
                        <h3 className="text-2xl md:text-3xl font-serif font-bold text-gray-900 mb-4">
                           Pregnancy Photography
                        </h3>
                        <p className="text-lg text-gray-700 mb-6 leading-relaxed">
                           Document this beautiful journey with our maternity
                           photography sessions. We capture the glow,
                           excitement, and anticipation of expecting parents,
                           creating timeless memories of this special time in
                           your life.
                        </p>
                        <div className="space-y-3 mb-6">
                           <div className="flex items-center">
                              <svg
                                 className="w-5 h-5 text-primary mr-3"
                                 fill="currentColor"
                                 viewBox="0 0 20 20"
                              >
                                 <path
                                    fillRule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clipRule="evenodd"
                                 />
                              </svg>
                              <span>Studio and outdoor session options</span>
                           </div>
                           <div className="flex items-center">
                              <svg
                                 className="w-5 h-5 text-primary mr-3"
                                 fill="currentColor"
                                 viewBox="0 0 20 20"
                              >
                                 <path
                                    fillRule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clipRule="evenodd"
                                 />
                              </svg>
                              <span>Partner and family inclusion</span>
                           </div>
                           <div className="flex items-center">
                              <svg
                                 className="w-5 h-5 text-primary mr-3"
                                 fill="currentColor"
                                 viewBox="0 0 20 20"
                              >
                                 <path
                                    fillRule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clipRule="evenodd"
                                 />
                              </svg>
                              <span>Wardrobe consultation and styling</span>
                           </div>
                           <div className="flex items-center">
                              <svg
                                 className="w-5 h-5 text-primary mr-3"
                                 fill="currentColor"
                                 viewBox="0 0 20 20"
                              >
                                 <path
                                    fillRule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clipRule="evenodd"
                                 />
                              </svg>
                              <span>
                                 Gentle posing and comfort-focused approach
                              </span>
                           </div>
                        </div>
                        <Link
                           href="/contact"
                           className="inline-block bg-primary hover:bg-dark text-white font-semibold px-8 py-3 rounded-full transition-colors duration-200"
                        >
                           Book Pregnancy Shoot
                        </Link>
                     </div>
                     <div className="relative">
                        <Image
                           src="/images/pregnancy-shoots/pregnancy-shoot-5.jpg"
                           alt="Pregnancy Photography"
                           width={600}
                           height={400}
                           className="rounded-lg shadow-lg"
                        />
                     </div>
                  </div>
                  <PortfolioGrid
                     items={pregnancyImages}
                     showFilter={false}
                     columns={4}
                  />
               </div>

               {/* Child Dedication */}
               <div id="child-dedication" className="mb-20">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-12">
                     <div className="order-2 lg:order-1 relative">
                        <Image
                           src="/images/child-dedication/child-dedication-5.JPG"
                           alt="Child Dedication Photography"
                           width={600}
                           height={400}
                           className="rounded-lg shadow-lg"
                        />
                     </div>
                     <div className="order-1 lg:order-2">
                        <h3 className="text-2xl md:text-3xl font-serif font-bold text-gray-900 mb-4">
                           Child Dedication
                        </h3>
                        <p className="text-lg text-gray-700 mb-6 leading-relaxed">
                           Capture the joy and significance of your child&apos;s
                           dedication ceremony. These precious moments of
                           blessing and celebration deserve to be preserved with
                           care and artistry for generations to come.
                        </p>
                        <div className="space-y-3 mb-6">
                           <div className="flex items-center">
                              <svg
                                 className="w-5 h-5 text-primary mr-3"
                                 fill="currentColor"
                                 viewBox="0 0 20 20"
                              >
                                 <path
                                    fillRule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clipRule="evenodd"
                                 />
                              </svg>
                              <span>Ceremony and celebration coverage</span>
                           </div>
                           <div className="flex items-center">
                              <svg
                                 className="w-5 h-5 text-primary mr-3"
                                 fill="currentColor"
                                 viewBox="0 0 20 20"
                              >
                                 <path
                                    fillRule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clipRule="evenodd"
                                 />
                              </svg>
                              <span>Family and group portraits</span>
                           </div>
                           <div className="flex items-center">
                              <svg
                                 className="w-5 h-5 text-primary mr-3"
                                 fill="currentColor"
                                 viewBox="0 0 20 20"
                              >
                                 <path
                                    fillRule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clipRule="evenodd"
                                 />
                              </svg>
                              <span>Candid moments and emotions</span>
                           </div>
                           <div className="flex items-center">
                              <svg
                                 className="w-5 h-5 text-primary mr-3"
                                 fill="currentColor"
                                 viewBox="0 0 20 20"
                              >
                                 <path
                                    fillRule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clipRule="evenodd"
                                 />
                              </svg>
                              <span>Respectful and unobtrusive approach</span>
                           </div>
                        </div>
                        <Link
                           href="/contact"
                           className="inline-block bg-primary hover:bg-dark text-white font-semibold px-8 py-3 rounded-full transition-colors duration-200"
                        >
                           Book Child Dedication
                        </Link>
                     </div>
                  </div>
                  <PortfolioGrid
                     items={childDedicationImages}
                     showFilter={false}
                     columns={4}
                  />
               </div>
            </div>
         </section>

         {/* Videography Services */}
         <section className="py-20 bg-gray-50">
            <div className="max-w-7xl mx-auto px-6 lg:px-8">
               <div className="text-center mb-16">
                  <h2 className="text-3xl md:text-4xl font-serif font-bold text-gray-900 mb-4">
                     Videography Services
                  </h2>
                  <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                     Bring your memories to life with our professional
                     videography services
                  </p>
               </div>

               <div id="videography" className="mb-20">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                     <div>
                        <h3 className="text-2xl md:text-3xl font-serif font-bold text-gray-900 mb-4">
                           Professional Videography
                        </h3>
                        <p className="text-lg text-gray-700 mb-6 leading-relaxed">
                           Capture the motion, emotion, and sound of your
                           special day with our professional videography
                           services. From wedding ceremonies to special events,
                           we create cinematic films that tell your story
                           beautifully.
                        </p>
                        <div className="space-y-3 mb-6">
                           <div className="flex items-center">
                              <svg
                                 className="w-5 h-5 text-primary mr-3"
                                 fill="currentColor"
                                 viewBox="0 0 20 20"
                              >
                                 <path
                                    fillRule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clipRule="evenodd"
                                 />
                              </svg>
                              <span>4K ultra-high definition recording</span>
                           </div>
                           <div className="flex items-center">
                              <svg
                                 className="w-5 h-5 text-primary mr-3"
                                 fill="currentColor"
                                 viewBox="0 0 20 20"
                              >
                                 <path
                                    fillRule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clipRule="evenodd"
                                 />
                              </svg>
                              <span>Drone footage for aerial perspectives</span>
                           </div>
                           <div className="flex items-center">
                              <svg
                                 className="w-5 h-5 text-primary mr-3"
                                 fill="currentColor"
                                 viewBox="0 0 20 20"
                              >
                                 <path
                                    fillRule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clipRule="evenodd"
                                 />
                              </svg>
                              <span>
                                 Professional editing and color grading
                              </span>
                           </div>
                           <div className="flex items-center">
                              <svg
                                 className="w-5 h-5 text-primary mr-3"
                                 fill="currentColor"
                                 viewBox="0 0 20 20"
                              >
                                 <path
                                    fillRule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clipRule="evenodd"
                                 />
                              </svg>
                              <span>Same-day highlight reels available</span>
                           </div>
                        </div>
                        <Link
                           href="/contact"
                           className="inline-block bg-primary hover:bg-dark text-white font-semibold px-8 py-3 rounded-full transition-colors duration-200"
                        >
                           Book Videography
                        </Link>
                     </div>
                     <div className="relative">
                        <Image
                           src="/images/videography.jpg"
                           alt="Professional Videography"
                           width={600}
                           height={400}
                           className="rounded-lg shadow-lg"
                        />
                     </div>
                  </div>
               </div>
            </div>
         </section>

         {/* Special Services */}
         <section className="py-20 bg-white">
            <div className="max-w-7xl mx-auto px-6 lg:px-8">
               <div className="text-center mb-16">
                  <h2 className="text-3xl md:text-4xl font-serif font-bold text-gray-900 mb-4">
                     Special Services
                  </h2>
                  <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                     Enhance your event with our unique special services and
                     equipment
                  </p>
               </div>

               <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
                  {/* 360 Video Booth */}
                  <div id="360-video-booth">
                     <div className="text-center mb-8">
                        <Image
                           src="/images/360-photo-booth.jpg"
                           alt="360 Video Booth"
                           width={500}
                           height={300}
                           className="rounded-lg shadow-lg mx-auto"
                        />
                     </div>
                     <h3 className="text-2xl font-serif font-bold text-gray-900 mb-4 text-center">
                        360 Video Booth
                     </h3>
                     <p className="text-lg text-gray-700 mb-6 leading-relaxed text-center">
                        Add excitement and entertainment to your event with our
                        state-of-the-art 360 video booth. Guests will love
                        creating dynamic, shareable content that captures the
                        fun from every angle.
                     </p>
                     <div className="space-y-3 mb-6">
                        <div className="flex items-center justify-center">
                           <svg
                              className="w-5 h-5 text-primary mr-3"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                           >
                              <path
                                 fillRule="evenodd"
                                 d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                 clipRule="evenodd"
                              />
                           </svg>
                           <span>Instant social media sharing</span>
                        </div>
                        <div className="flex items-center justify-center">
                           <svg
                              className="w-5 h-5 text-primary mr-3"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                           >
                              <path
                                 fillRule="evenodd"
                                 d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                 clipRule="evenodd"
                              />
                           </svg>
                           <span>Custom branding and overlays</span>
                        </div>
                        <div className="flex items-center justify-center">
                           <svg
                              className="w-5 h-5 text-primary mr-3"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                           >
                              <path
                                 fillRule="evenodd"
                                 d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                 clipRule="evenodd"
                              />
                           </svg>
                           <span>Props and accessories included</span>
                        </div>
                        <div className="flex items-center justify-center">
                           <svg
                              className="w-5 h-5 text-primary mr-3"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                           >
                              <path
                                 fillRule="evenodd"
                                 d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                 clipRule="evenodd"
                              />
                           </svg>
                           <span>Professional setup and operation</span>
                        </div>
                     </div>
                     <div className="text-center">
                        <Link
                           href="/contact"
                           className="inline-block bg-primary hover:bg-dark text-white font-semibold px-8 py-3 rounded-full transition-colors duration-200"
                        >
                           Book 360 Video Booth
                        </Link>
                     </div>
                  </div>

                  {/* Dry Ice Machine */}
                  <div id="dry-ice-machine">
                     <div className="text-center mb-8">
                        <Image
                           src="/images/dry-ice-machine.jpg"
                           alt="Dry Ice Machine"
                           width={500}
                           height={300}
                           className="rounded-lg shadow-lg mx-auto"
                        />
                     </div>
                     <h3 className="text-2xl font-serif font-bold text-gray-900 mb-4 text-center">
                        Dry Ice Machine
                     </h3>
                     <p className="text-lg text-gray-700 mb-6 leading-relaxed text-center">
                        Create magical, ethereal effects for your special
                        moments with our professional dry ice machine. Perfect
                        for first dances, dramatic entrances, and creating
                        unforgettable atmosphere.
                     </p>
                     <div className="space-y-3 mb-6">
                        <div className="flex items-center justify-center">
                           <svg
                              className="w-5 h-5 text-primary mr-3"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                           >
                              <path
                                 fillRule="evenodd"
                                 d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                 clipRule="evenodd"
                              />
                           </svg>
                           <span>Safe and professional operation</span>
                        </div>
                        <div className="flex items-center justify-center">
                           <svg
                              className="w-5 h-5 text-primary mr-3"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                           >
                              <path
                                 fillRule="evenodd"
                                 d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                 clipRule="evenodd"
                              />
                           </svg>
                           <span>Dramatic low-lying fog effects</span>
                        </div>
                        <div className="flex items-center justify-center">
                           <svg
                              className="w-5 h-5 text-primary mr-3"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                           >
                              <path
                                 fillRule="evenodd"
                                 d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                 clipRule="evenodd"
                              />
                           </svg>
                           <span>Perfect timing coordination</span>
                        </div>
                        <div className="flex items-center justify-center">
                           <svg
                              className="w-5 h-5 text-primary mr-3"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                           >
                              <path
                                 fillRule="evenodd"
                                 d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                 clipRule="evenodd"
                              />
                           </svg>
                           <span>Venue-appropriate application</span>
                        </div>
                     </div>
                     <div className="text-center">
                        <Link
                           href="/contact"
                           className="inline-block bg-primary hover:bg-dark text-white font-semibold px-8 py-3 rounded-full transition-colors duration-200"
                        >
                           Add Dry Ice Effects
                        </Link>
                     </div>
                  </div>
               </div>
            </div>
         </section>

         {/* Call to Action */}
         <section className="py-20 bg-black text-white">
            <div className="max-w-4xl mx-auto px-6 lg:px-8 text-center">
               <h2 className="text-3xl md:text-4xl font-serif font-bold mb-6">
                  Ready to Book Your Service?
               </h2>
               <p className="text-lg mb-8 text-gray-300 leading-relaxed">
                  Contact us today to discuss your needs and create a custom
                  package that&apos;s perfect for your special occasion.
               </p>
               <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Link
                     href="/contact"
                     className="inline-block bg-primary hover:bg-accent text-white font-semibold px-8 py-3 rounded-full transition-colors duration-200"
                  >
                     Get a Quote
                  </Link>
                  <a
                     href="https://wa.me/447865000828"
                     target="_blank"
                     rel="noopener noreferrer"
                     className="inline-block border-2 border-white hover:bg-white hover:text-black text-white font-semibold px-8 py-3 rounded-full transition-colors duration-200"
                  >
                     WhatsApp Us
                  </a>
               </div>
            </div>
         </section>
      </div>
   );
}
