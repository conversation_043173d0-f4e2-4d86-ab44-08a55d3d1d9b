"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogFooter,
   DialogHeader,
   DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from "@/components/ui/select";
import {
   useBulkDeletePortfolioImages,
   useBulkMovePortfolioImages,
   usePortfolioServices,
} from "@/lib/hooks/use-portfolio";
import { PortfolioImage } from "@/lib/models/portfolio";
import { Loader2, Move, Trash2 } from "lucide-react";
import { useState } from "react";

interface PortfolioBulkOperationsDialogProps {
   selectedImages: PortfolioImage[];
   currentServiceId: string;
   isOpen: boolean;
   onClose: () => void;
   onSuccess: () => void;
}

type BulkOperation = "move" | "delete";

export default function PortfolioBulkOperationsDialog({
   selectedImages,
   currentServiceId,
   isOpen,
   onClose,
   onSuccess,
}: PortfolioBulkOperationsDialogProps) {
   const [operation, setOperation] = useState<BulkOperation>("move");
   const [targetServiceId, setTargetServiceId] = useState<string>("");

   const { data: servicesData } = usePortfolioServices();
   const bulkMoveMutation = useBulkMovePortfolioImages();
   const bulkDeleteMutation = useBulkDeletePortfolioImages();

   const services = servicesData || [];
   const availableServices = services.filter(
      (service) => service._id !== currentServiceId
   );

   const handleSubmit = async () => {
      if (selectedImages.length === 0) return;

      const imageIds = selectedImages.map((img) => img._id!.toString());

      try {
         if (operation === "move") {
            if (!targetServiceId) return;

            const formData = new FormData();
            formData.append("serviceId", targetServiceId);
            formData.append("imageIds", JSON.stringify(imageIds));

            await bulkMoveMutation.mutateAsync(formData);
         } else if (operation === "delete") {
            const formData = new FormData();
            formData.append("imageIds", JSON.stringify(imageIds));

            await bulkDeleteMutation.mutateAsync(formData);
         }

         onSuccess();
         handleClose();
      } catch (error) {
         console.error(`Failed to ${operation} images:`, error);
      }
   };

   const handleClose = () => {
      setOperation("move");
      setTargetServiceId("");
      onClose();
   };

   const isLoading = bulkMoveMutation.isPending || bulkDeleteMutation.isPending;
   const canSubmit =
      selectedImages.length > 0 &&
      (operation === "delete" || (operation === "move" && targetServiceId));

   return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
         <DialogContent className="sm:max-w-md">
            <DialogHeader>
               <DialogTitle>Bulk Operations</DialogTitle>
               <DialogDescription>
                  Perform bulk operations on {selectedImages.length} selected
                  image{selectedImages.length !== 1 ? "s" : ""}.
               </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
               <div className="space-y-2">
                  <Label>Operation</Label>
                  <Select
                     value={operation}
                     onValueChange={(value) =>
                        setOperation(value as BulkOperation)
                     }
                  >
                     <SelectTrigger>
                        <SelectValue placeholder="Select operation" />
                     </SelectTrigger>
                     <SelectContent>
                        <SelectItem value="move">
                           <div className="flex items-center">
                              <Move className="w-4 h-4 mr-2" />
                              Move to another service
                           </div>
                        </SelectItem>
                        <SelectItem value="delete">
                           <div className="flex items-center">
                              <Trash2 className="w-4 h-4 mr-2" />
                              Delete images
                           </div>
                        </SelectItem>
                     </SelectContent>
                  </Select>
               </div>

               {operation === "move" && (
                  <div className="space-y-2">
                     <Label>Target Service</Label>
                     <Select
                        value={targetServiceId}
                        onValueChange={setTargetServiceId}
                     >
                        <SelectTrigger>
                           <SelectValue placeholder="Select target service" />
                        </SelectTrigger>
                        <SelectContent>
                           {availableServices.map((service) => (
                              <SelectItem
                                 key={service._id!.toString()}
                                 value={service._id!.toString()}
                              >
                                 {service.name}
                              </SelectItem>
                           ))}
                        </SelectContent>
                     </Select>
                     {availableServices.length === 0 && (
                        <p className="text-sm text-muted-foreground">
                           No other services available to move images to.
                        </p>
                     )}
                  </div>
               )}

               {operation === "delete" && (
                  <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
                     <p className="text-sm text-destructive font-medium">
                        Warning: This action cannot be undone
                     </p>
                     <p className="text-sm text-destructive/80 mt-1">
                        The selected images will be permanently deleted from
                        both the database and storage.
                     </p>
                  </div>
               )}
            </div>

            <DialogFooter>
               <Button
                  type="button"
                  variant="outline"
                  onClick={handleClose}
                  disabled={isLoading}
               >
                  Cancel
               </Button>
               <Button
                  onClick={handleSubmit}
                  disabled={!canSubmit || isLoading}
                  variant={operation === "delete" ? "destructive" : "default"}
                  className={
                     operation === "move"
                        ? "bg-gradient-accent hover:opacity-90"
                        : ""
                  }
               >
                  {isLoading ? (
                     <>
                        <Loader2 className="w-4 h-4 animate-spin mr-2" />
                        {operation === "move" ? "Moving..." : "Deleting..."}
                     </>
                  ) : (
                     <>
                        {operation === "move" ? (
                           <Move className="w-4 h-4 mr-2" />
                        ) : (
                           <Trash2 className="w-4 h-4 mr-2" />
                        )}
                        {operation === "move"
                           ? `Move ${selectedImages.length} Image${
                                selectedImages.length !== 1 ? "s" : ""
                             }`
                           : `Delete ${selectedImages.length} Image${
                                selectedImages.length !== 1 ? "s" : ""
                             }`}
                     </>
                  )}
               </Button>
            </DialogFooter>
         </DialogContent>
      </Dialog>
   );
}
