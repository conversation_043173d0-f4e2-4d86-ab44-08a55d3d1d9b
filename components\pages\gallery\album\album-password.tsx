import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Album } from "@/lib/models";
import { LockClosedIcon } from "@heroicons/react/24/solid";
import { motion } from "framer-motion";
import { ArrowLeft } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

type AlbumPasswordProps = {
   album: Album;
   handlePasswordSubmit: (e: React.FormEvent) => void;
   password: string;
   setPassword: (password: string) => void;
   passwordError: string;
};

export default function AlbumPassword({
   album,
   handlePasswordSubmit,
   password,
   setPassword,
   passwordError,
}: AlbumPasswordProps) {
   return (
      <>
         <div className="py-8 relative">
            <Image
               src={album.coverImageUrl || ""}
               alt="Album Password"
               fill
               className="object-cover -z-10 opacity-50 brightness-75 blur-sm"
            />
            <div className="container min-h-[calc(100vh-60px)] flex items-center pt-20 mx-auto px-4">
               <div className="max-w-md mx-auto">
                  <motion.div
                     initial={{ opacity: 0, y: 20 }}
                     animate={{ opacity: 1, y: 0 }}
                     transition={{ duration: 0.6 }}
                     className="text-center p-8 bg-astral-grey rounded-2xl backdrop-blur-md shadow-2xl border border-border/30"
                  >
                     <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6">
                        <LockClosedIcon className="w-8 h-8 text-primary" />
                     </div>

                     <h1 className="text-2xl font-bold text-foreground mb-2">
                        Protected Album
                     </h1>
                     <p className="text-muted-foreground text-sm mb-6">
                        This album is password protected. Please enter the
                        password to view the content.
                     </p>

                     <form
                        onSubmit={handlePasswordSubmit}
                        className="space-y-4"
                     >
                        <div className="space-y-4">
                           <Label htmlFor="password">
                              <LockClosedIcon className="w-4 h-4" />
                              Password
                           </Label>
                           <Input
                              id="password"
                              type="password"
                              value={password}
                              onChange={(e) => setPassword(e.target.value)}
                              placeholder="Enter album password"
                              className="mt-1 bg-[#101010] px-6 rounded-lg"
                           />
                           {passwordError && (
                              <p className="text-destructive text-sm mt-2">
                                 {passwordError}
                              </p>
                           )}
                        </div>

                        <Button type="submit" size="lg" className="w-full">
                           Unlock Album
                        </Button>
                     </form>

                     <div className="mt-6 ">
                        <Button
                           asChild
                           variant="ghost"
                           className="hover:bg-transparent"
                        >
                           <Link
                              href="/gallery/albums"
                              className="flex items-center gap-2"
                           >
                              <ArrowLeft className="w-4 h-4" />
                              Back to Albums
                           </Link>
                        </Button>
                     </div>
                  </motion.div>
               </div>
            </div>
         </div>
      </>
   );
}
