"use client";

import { cn } from "@/lib/utils";
import { easeOut, motion } from "framer-motion";
import { ReactNode } from "react";

interface GalleryGridProps {
   children: ReactNode;
   columns?: 2 | 3 | 4;
   gap?: "sm" | "md" | "lg";
   className?: string;
   animate?: boolean;
}

export default function GalleryGrid({
   children,
   columns = 3,
   gap = "md",
   className,
   animate = true,
}: GalleryGridProps) {
   const gridCols = {
      2: "grid-cols-1 md:grid-cols-2",
      3: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
      4: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",
   };

   const gridGap = {
      sm: "gap-4",
      md: "gap-6",
      lg: "gap-8",
   };

   const containerVariants = {
      hidden: { opacity: 0 },
      visible: {
         opacity: 1,
         transition: {
            staggerChildren: 0.1,
         },
      },
   };

   const itemVariants = {
      hidden: { opacity: 0, y: 20 },
      visible: {
         opacity: 1,
         y: 0,
         transition: {
            duration: 0.5,
            ease: easeOut,
         },
      },
   };

   if (animate) {
      return (
         <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className={cn("grid", gridCols[columns], gridGap[gap], className)}
         >
            {Array.isArray(children) ? (
               children.map((child, index) => (
                  <motion.div key={index} variants={itemVariants}>
                     {child}
                  </motion.div>
               ))
            ) : (
               <motion.div variants={itemVariants}>{children}</motion.div>
            )}
         </motion.div>
      );
   }

   return (
      <div className={cn("grid", gridCols[columns], gridGap[gap], className)}>
         {children}
      </div>
   );
}
