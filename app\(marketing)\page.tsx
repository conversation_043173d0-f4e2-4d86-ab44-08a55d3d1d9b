"use client";

import CallToActionSection from "@/components/pages/home/<USER>";
import FeaturedServiceSection from "@/components/pages/home/<USER>";
import { HeroSection } from "@/components/pages/home/<USER>";
import IntroSection from "@/components/pages/home/<USER>";
import PhotoGallery from "@/components/pages/home/<USER>";
import ServiceSection from "@/components/pages/home/<USER>";
import TestimonialSection from "@/components/pages/home/<USER>";
import { SEOHead, seoConfigs } from "@/components/seo";
import Lenis from "@studio-freight/lenis";
import { useEffect } from "react";

export default function Home() {
   useEffect(() => {
      const lenis = new Lenis();

      function raf(time: number) {
         lenis.raf(time);
         requestAnimationFrame(raf);
      }

      requestAnimationFrame(raf);
   });

   return (
      <div className="min-h-screen">
         <SEOHead {...seoConfigs.homepage} />

         <HeroSection />

         <IntroSection />

         <ServiceSection />

         <FeaturedServiceSection />

         <PhotoGallery />

         <TestimonialSection />

         <CallToActionSection />
      </div>
   );
}
