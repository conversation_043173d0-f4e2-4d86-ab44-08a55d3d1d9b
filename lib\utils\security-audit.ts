import { getSession } from "@/lib/auth/session";

/**
 * Security event types for audit logging
 */
export enum SecurityEventType {
   AUTHENTICATION_SUCCESS = "AUTHENTICATION_SUCCESS",
   AUTHENTICATION_FAILURE = "AUTHENTICATION_FAILURE",
   AUTHORIZATION_FAILURE = "AUTHORIZATION_FAILURE",
   RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED",
   SUSPICIOUS_INPUT = "SUSPICIOUS_INPUT",
   FILE_UPLOAD_BLOCKED = "FILE_UPLOAD_BLOCKED",
   XSS_ATTEMPT_BLOCKED = "XSS_ATTEMPT_BLOCKED",
   SQL_INJECTION_ATTEMPT = "SQL_INJECTION_ATTEMPT",
   PATH_TRAVERSAL_ATTEMPT = "PATH_TRAVERSAL_ATTEMPT",
   INVALID_FILE_TYPE = "INVALID_FILE_TYPE",
   OVERSIZED_UPLOAD = "OVERSIZED_UPLOAD",
   PORTFOLIO_ACCESS = "PORTFOLIO_ACCESS",
   PORTFOLIO_MODIFICATION = "PORTFOLIO_MODIFICATION",
   BULK_OPERATION = "BULK_OPERATION",
   ADMIN_ACTION = "ADMIN_ACTION",
}

/**
 * Security event severity levels
 */
export enum SecuritySeverity {
   LOW = "LOW",
   MEDIUM = "MEDIUM",
   HIGH = "HIGH",
   CRITICAL = "CRITICAL",
}

/**
 * Security audit event interface
 */
export interface SecurityAuditEvent {
   timestamp: Date;
   eventType: SecurityEventType;
   severity: SecuritySeverity;
   userId?: string;
   userEmail?: string;
   ipAddress?: string;
   userAgent?: string;
   resource?: string;
   action?: string;
   details?: Record<string, unknown>;
   blocked: boolean;
   message: string;
}

/**
 * Security audit logger class
 */
export class SecurityAuditLogger {
   private static events: SecurityAuditEvent[] = [];
   private static maxEvents = 1000; // Keep last 1000 events in memory

   /**
    * Log a security event
    */
   static async logEvent(
      eventType: SecurityEventType,
      severity: SecuritySeverity,
      message: string,
      options: {
         resource?: string;
         action?: string;
         details?: Record<string, unknown>;
         blocked?: boolean;
         ipAddress?: string;
         userAgent?: string;
      } = {}
   ): Promise<void> {
      try {
         // Get current user session if available
         let userId: string | undefined;
         let userEmail: string | undefined;

         try {
            const session = await getSession();
            if (session) {
               userId = session.id;
               userEmail = session.email;
            }
         } catch {
            // Session not available, continue without user info
         }

         const event: SecurityAuditEvent = {
            timestamp: new Date(),
            eventType,
            severity,
            userId,
            userEmail,
            ipAddress: options.ipAddress,
            userAgent: options.userAgent,
            resource: options.resource,
            action: options.action,
            details: options.details,
            blocked: options.blocked ?? false,
            message,
         };

         // Add to in-memory store
         this.events.push(event);

         // Keep only the last maxEvents
         if (this.events.length > this.maxEvents) {
            this.events = this.events.slice(-this.maxEvents);
         }

         // Log to console for development/debugging
         const logLevel = this.getLogLevel(severity);
         console[logLevel](`[SECURITY AUDIT] ${eventType}: ${message}`, {
            severity,
            userId,
            resource: options.resource,
            action: options.action,
            blocked: options.blocked,
            details: options.details,
         });

         // In production, you might want to send this to an external logging service
         // await this.sendToExternalLogger(event);
      } catch (error) {
         console.error("Failed to log security event:", error);
      }
   }

   /**
    * Get console log level based on severity
    */
   private static getLogLevel(
      severity: SecuritySeverity
   ): "log" | "warn" | "error" {
      switch (severity) {
         case SecuritySeverity.LOW:
            return "log";
         case SecuritySeverity.MEDIUM:
            return "warn";
         case SecuritySeverity.HIGH:
         case SecuritySeverity.CRITICAL:
            return "error";
         default:
            return "log";
      }
   }

   /**
    * Get recent security events
    */
   static getRecentEvents(limit: number = 100): SecurityAuditEvent[] {
      return this.events.slice(-limit).reverse(); // Most recent first
   }

   /**
    * Get events by type
    */
   static getEventsByType(
      eventType: SecurityEventType,
      limit: number = 50
   ): SecurityAuditEvent[] {
      return this.events
         .filter((event) => event.eventType === eventType)
         .slice(-limit)
         .reverse();
   }

   /**
    * Get events by severity
    */
   static getEventsBySeverity(
      severity: SecuritySeverity,
      limit: number = 50
   ): SecurityAuditEvent[] {
      return this.events
         .filter((event) => event.severity === severity)
         .slice(-limit)
         .reverse();
   }

   /**
    * Get events by user
    */
   static getEventsByUser(
      userId: string,
      limit: number = 50
   ): SecurityAuditEvent[] {
      return this.events
         .filter((event) => event.userId === userId)
         .slice(-limit)
         .reverse();
   }

   /**
    * Get blocked events (security incidents)
    */
   static getBlockedEvents(limit: number = 50): SecurityAuditEvent[] {
      return this.events
         .filter((event) => event.blocked)
         .slice(-limit)
         .reverse();
   }

   /**
    * Clear old events (cleanup)
    */
   static clearOldEvents(olderThanHours: number = 24): void {
      const cutoffTime = new Date(Date.now() - olderThanHours * 60 * 60 * 1000);
      this.events = this.events.filter((event) => event.timestamp > cutoffTime);
   }

   /**
    * Get security statistics
    */
   static getSecurityStats(): {
      totalEvents: number;
      eventsByType: Record<string, number>;
      eventsBySeverity: Record<string, number>;
      blockedEvents: number;
      recentActivity: number; // Events in last hour
   } {
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);

      const eventsByType: Record<string, number> = {};
      const eventsBySeverity: Record<string, number> = {};
      let blockedEvents = 0;
      let recentActivity = 0;

      for (const event of this.events) {
         // Count by type
         eventsByType[event.eventType] =
            (eventsByType[event.eventType] || 0) + 1;

         // Count by severity
         eventsBySeverity[event.severity] =
            (eventsBySeverity[event.severity] || 0) + 1;

         // Count blocked events
         if (event.blocked) {
            blockedEvents++;
         }

         // Count recent activity
         if (event.timestamp > oneHourAgo) {
            recentActivity++;
         }
      }

      return {
         totalEvents: this.events.length,
         eventsByType,
         eventsBySeverity,
         blockedEvents,
         recentActivity,
      };
   }
}

/**
 * Convenience functions for common security events
 */
export class SecurityEvents {
   /**
    * Log authentication success
    */
   static async logAuthSuccess(
      userEmail: string,
      ipAddress?: string
   ): Promise<void> {
      await SecurityAuditLogger.logEvent(
         SecurityEventType.AUTHENTICATION_SUCCESS,
         SecuritySeverity.LOW,
         `User ${userEmail} successfully authenticated`,
         {
            action: "login",
            ipAddress,
            details: { userEmail },
         }
      );
   }

   /**
    * Log authentication failure
    */
   static async logAuthFailure(
      attemptedEmail?: string,
      ipAddress?: string
   ): Promise<void> {
      await SecurityAuditLogger.logEvent(
         SecurityEventType.AUTHENTICATION_FAILURE,
         SecuritySeverity.MEDIUM,
         `Authentication failed${
            attemptedEmail ? ` for ${attemptedEmail}` : ""
         }`,
         {
            action: "login_failed",
            ipAddress,
            details: { attemptedEmail },
            blocked: true,
         }
      );
   }

   /**
    * Log rate limit exceeded
    */
   static async logRateLimitExceeded(
      action: string,
      identifier: string
   ): Promise<void> {
      await SecurityAuditLogger.logEvent(
         SecurityEventType.RATE_LIMIT_EXCEEDED,
         SecuritySeverity.MEDIUM,
         `Rate limit exceeded for ${action}`,
         {
            action,
            details: { identifier, action },
            blocked: true,
         }
      );
   }

   /**
    * Log suspicious input detected
    */
   static async logSuspiciousInput(
      input: string,
      field: string,
      reason: string
   ): Promise<void> {
      await SecurityAuditLogger.logEvent(
         SecurityEventType.SUSPICIOUS_INPUT,
         SecuritySeverity.HIGH,
         `Suspicious input detected in ${field}: ${reason}`,
         {
            action: "input_validation",
            details: { field, reason, inputLength: input.length },
            blocked: true,
         }
      );
   }

   /**
    * Log file upload blocked
    */
   static async logFileUploadBlocked(
      filename: string,
      reason: string
   ): Promise<void> {
      await SecurityAuditLogger.logEvent(
         SecurityEventType.FILE_UPLOAD_BLOCKED,
         SecuritySeverity.HIGH,
         `File upload blocked: ${reason}`,
         {
            action: "file_upload",
            details: { filename, reason },
            blocked: true,
         }
      );
   }

   /**
    * Log portfolio access
    */
   static async logPortfolioAccess(
      action: string,
      resource?: string
   ): Promise<void> {
      await SecurityAuditLogger.logEvent(
         SecurityEventType.PORTFOLIO_ACCESS,
         SecuritySeverity.LOW,
         `Portfolio ${action} accessed`,
         {
            resource: "portfolio",
            action,
            details: { specificResource: resource },
         }
      );
   }

   /**
    * Log portfolio modification
    */
   static async logPortfolioModification(
      action: string,
      resource: string,
      details?: Record<string, unknown>
   ): Promise<void> {
      await SecurityAuditLogger.logEvent(
         SecurityEventType.PORTFOLIO_MODIFICATION,
         SecuritySeverity.MEDIUM,
         `Portfolio ${resource} ${action}`,
         {
            resource: "portfolio",
            action,
            details: { resource, ...details },
         }
      );
   }

   /**
    * Log bulk operation
    */
   static async logBulkOperation(
      operation: string,
      count: number,
      resource: string
   ): Promise<void> {
      await SecurityAuditLogger.logEvent(
         SecurityEventType.BULK_OPERATION,
         SecuritySeverity.MEDIUM,
         `Bulk ${operation} performed on ${count} ${resource}(s)`,
         {
            resource,
            action: `bulk_${operation}`,
            details: { operation, count, resource },
         }
      );
   }
}

// Cleanup old events every hour
if (typeof setInterval !== "undefined") {
   setInterval(() => {
      SecurityAuditLogger.clearOldEvents(24); // Keep events for 24 hours
   }, 60 * 60 * 1000); // Run every hour
}
