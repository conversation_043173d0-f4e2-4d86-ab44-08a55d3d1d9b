import { ObjectId } from "mongodb";

// Comment interface for database storage
export interface Comment {
   _id?: ObjectId | string;
   albumId: string; // Reference to the album
   parentId?: string; // Reference to parent comment for replies
   content: string;
   authorName: string;
   authorEmail?: string;
   createdAt: Date;
   updatedAt: Date;
   isApproved?: boolean; // For moderation if needed
   adminResponse?: string; // Admin reply to the comment (one per comment maximum)
   deletedAt?: Date; // Soft deletion timestamp
}

// Comment creation input
export interface CreateCommentInput {
   albumId: string;
   parentId?: string;
   content: string;
   authorName: string;
   authorEmail?: string;
}

// Comment update input
export interface UpdateCommentInput {
   content?: string;
   authorName?: string;
   authorEmail?: string;
   isApproved?: boolean;
   adminResponse?: string;
   deletedAt?: Date;
}

// Comment with nested replies
export interface CommentWithReplies extends Comment {
   replies: CommentWithReplies[];
   replyCount: number;
}

// Admin comment input for adding/updating admin responses
export interface AdminCommentInput {
   adminResponse: string;
}

// Admin comment filter options
export interface AdminCommentFilters {
   showDeleted?: boolean;
   searchQuery?: string;
   limit?: number;
   offset?: number;
}

/**
 * Create comment metadata from input
 */
export function createCommentMetadata(
   input: CreateCommentInput
): Omit<Comment, "_id"> {
   const now = new Date();

   return {
      albumId: input.albumId,
      parentId: input.parentId,
      content: input.content,
      authorName: input.authorName,
      authorEmail: input.authorEmail,
      createdAt: now,
      updatedAt: now,
      isApproved: true, // Auto-approve for now
   };
}

/**
 * Validate comment input
 */
export function validateCommentInput(input: CreateCommentInput): string[] {
   const errors: string[] = [];

   if (!input.albumId || typeof input.albumId !== "string") {
      errors.push("Album ID is required and must be a string");
   }

   if (!input.content || typeof input.content !== "string") {
      errors.push("Content is required and must be a string");
   }

   if (input.content && input.content.trim().length === 0) {
      errors.push("Content cannot be empty");
   }

   if (input.content && input.content.length > 1000) {
      errors.push("Content must be less than 1000 characters");
   }

   if (!input.authorName || typeof input.authorName !== "string") {
      errors.push("Author name is required and must be a string");
   }

   if (input.authorName && input.authorName.trim().length === 0) {
      errors.push("Author name cannot be empty");
   }

   if (input.authorName && input.authorName.length > 100) {
      errors.push("Author name must be less than 100 characters");
   }

   if (
      input.authorEmail !== undefined &&
      input.authorEmail !== null &&
      typeof input.authorEmail !== "string"
   ) {
      errors.push("Author email must be a string");
   }

   if (
      input.authorEmail &&
      !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(input.authorEmail)
   ) {
      errors.push("Author email must be a valid email address");
   }

   if (input.parentId && typeof input.parentId !== "string") {
      errors.push("Parent ID must be a string");
   }

   return errors;
}
