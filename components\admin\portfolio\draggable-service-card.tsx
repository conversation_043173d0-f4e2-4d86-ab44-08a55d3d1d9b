"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
   DropdownMenu,
   DropdownMenuContent,
   DropdownMenuItem,
   DropdownMenuSeparator,
   DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { PortfolioServiceWithStats } from "@/lib/models";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import {
   ArrowLeftIcon,
   ArrowRightIcon,
   // PhotoIcon,
   TrashIcon,
} from "@heroicons/react/24/solid";
import { Briefcase, GripVertical, MoreVertical } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

interface DraggableServiceCardProps {
   service: PortfolioServiceWithStats;
   isActive: boolean;
   onDelete: (service: PortfolioServiceWithStats) => void;
   onToggleActive: (service: PortfolioServiceWithStats) => void;
   onMoveLeft: (service: PortfolioServiceWithStats) => void;
   onMoveRight: (service: PortfolioServiceWithStats) => void;
   canMoveLeft: boolean;
   canMoveRight: boolean;
   dropdownOpen: boolean;
   onDropdownOpenChange: (open: boolean) => void;
}

export function DraggableServiceCard({
   service,
   isActive,
   onDelete,
   onToggleActive,
   onMoveLeft,
   onMoveRight,
   canMoveLeft,
   canMoveRight,
   dropdownOpen,
   onDropdownOpenChange,
}: DraggableServiceCardProps) {
   const {
      attributes,
      listeners,
      setNodeRef,
      transform,
      transition,
      isDragging,
   } = useSortable({
      id: service._id?.toString() || "",
   });

   const style = {
      transform: CSS.Transform.toString(transform),
      transition,
   };

   return (
      <Card
         ref={setNodeRef}
         style={style}
         className={`border-border/30 border-none bg-astral-grey/90 group hover:shadow-lg transition-all duration-200 py-0 relative overflow-hidden ${
            isDragging ? "opacity-50 z-50 scale-105 shadow-2xl" : ""
         } ${!isActive ? "opacity-75" : ""}`}
      >
         {/* Drag Handle */}
         <div
            {...attributes}
            {...listeners}
            className="absolute top-2 left-2 transition-opacity z-20 cursor-grab active:cursor-grabbing"
         >
            <Button
               variant="outline"
               size="sm"
               className="h-8 w-8 p-0 bg-background/80 border-none rounded-lg backdrop-blur-sm hover:bg-background/90"
            >
               <GripVertical className="w-4 h-4" />
            </Button>
         </div>

         {/* Dropdown menu */}
         <div
            className="absolute top-2 right-2 group-hover:opacity-100 transition-opacity z-10"
            onClick={(e) => e.stopPropagation()}
         >
            <DropdownMenu
               open={dropdownOpen}
               onOpenChange={onDropdownOpenChange}
            >
               <DropdownMenuTrigger asChild>
                  <Button
                     variant="outline"
                     size="sm"
                     className="h-8 w-8 p-0 bg-background/80 border-none rounded-lg backdrop-blur-sm hover:bg-background/90"
                     onClick={(e) => e.stopPropagation()}
                  >
                     <MoreVertical className="w-4 h-4" />
                  </Button>
               </DropdownMenuTrigger>
               <DropdownMenuContent
                  align="end"
                  onClick={(e) => e.stopPropagation()}
               >
                  <DropdownMenuItem
                     onClick={() => {
                        onDropdownOpenChange(false);
                        onMoveLeft(service);
                     }}
                     disabled={!canMoveLeft}
                  >
                     <ArrowLeftIcon className="w-4 h-4 mr-2" />
                     Move Left
                  </DropdownMenuItem>
                  <DropdownMenuItem
                     onClick={() => {
                        onDropdownOpenChange(false);
                        onMoveRight(service);
                     }}
                     disabled={!canMoveRight}
                  >
                     <ArrowRightIcon className="w-4 h-4 mr-2" />
                     Move Right
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                     onClick={() => {
                        onDropdownOpenChange(false);
                        onToggleActive(service);
                     }}
                  >
                     {isActive ? "Move to Inactive" : "Move to Active"}
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                     className="group/delete-btn text-destructive font-semibold hover:!text-white"
                     onClick={() => {
                        onDropdownOpenChange(false);
                        onDelete(service);
                     }}
                  >
                     <TrashIcon className="w-4 h-4 mr-2 transition-colors" />
                     Delete Service
                  </DropdownMenuItem>
               </DropdownMenuContent>
            </DropdownMenu>
         </div>

         {/* Make the card content a link */}
         <Link
            href={`/admin/portfolio/${service._id}`}
            className="block group/card focus:outline-none focus:ring-primary rounded-lg"
         >
            <CardContent className="p-0 cursor-pointer relative">
               {/* Service Cover Image or Icon */}
               <div className="w-full h-68 bg-astral-grey-light/50 rounded-t-lg flex items-center justify-center overflow-hidden">
                  {service.coverImageUrl ? (
                     <Image
                        src={service.coverImageUrl}
                        alt={`${service.name} cover`}
                        fill
                        className="object-cover"
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                     />
                  ) : (
                     <Briefcase className="w-12 h-12 text-muted-foreground" />
                  )}
               </div>
               <div className="p-3 absolute bottom-2 left-2 right-2 rounded-xl z-20 bg-black/50 backdrop-blur-sm">
                  <h3 className="font-semibold text-foreground transition-colors line-clamp-1 leading-5 text-center">
                     {service.name}
                  </h3>
               </div>
               {/* <span className="absolute opacity-0 group-hover:opacity-100 top-2 left-12 flex items-center gap-1.5 bg-black/50 backdrop-blur-sm rounded-full px-3 py-1">
                  <PhotoIcon className="size-4" />
                  {service.imageCount || 0}
               </span> */}
               {!isActive && (
                  <div className="absolute inset-0 bg-gray-900/30 backdrop-blur-[1px] flex items-center justify-center">
                     <span className="bg-gray-800/90 text-gray-200 px-3 py-1 rounded-full text-sm font-medium">
                        Inactive
                     </span>
                  </div>
               )}
            </CardContent>
         </Link>
      </Card>
   );
}
