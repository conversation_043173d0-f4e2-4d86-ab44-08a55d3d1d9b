import { ObjectId } from "mongodb";

// Album interface for database storage
export interface Album {
   _id?: ObjectId | string;
   name: string;
   description?: string;
   coverImageUrl?: string; // URL of the cover image
   hasPassword: boolean;
   password?: string;
   createdAt: Date;
   updatedAt: Date;
}

// Album creation input
export interface CreateAlbumInput {
   name: string;
   description?: string;
   coverImageUrl?: string;
   hasPassword?: boolean;
   password?: string;
}

// Album update input
export interface UpdateAlbumInput {
   name?: string;
   description?: string;
   coverImageUrl?: string;
   hasPassword?: boolean;
   password?: string;
}

// Album with image count for display
export interface AlbumWithStats extends Album {
   imageCount: number;
}

/**
 * Create album metadata from input
 */
export function createAlbumMetadata(
   input: CreateAlbumInput
): Omit<Album, "_id"> {
   const now = new Date();

   return {
      name: input.name,
      description: input.description,
      coverImageUrl: input.coverImageUrl,
      hasPassword: input.hasPassword ?? false,
      password: input.hasPassword ? input.password : undefined,
      createdAt: now,
      updatedAt: now,
   };
}

/**
 * Validate album input
 */
export function validateAlbumInput(input: CreateAlbumInput): string[] {
   const errors: string[] = [];

   if (!input.name || typeof input.name !== "string") {
      errors.push("Name is required and must be a string");
   }

   if (input.name && input.name.trim().length === 0) {
      errors.push("Name cannot be empty");
   }

   if (
      input.description !== undefined &&
      typeof input.description !== "string"
   ) {
      errors.push("Description must be a string");
   }

   if (
      input.coverImageUrl !== undefined &&
      typeof input.coverImageUrl !== "string"
   ) {
      errors.push("Cover image URL must be a string");
   }

   if (
      input.hasPassword !== undefined &&
      typeof input.hasPassword !== "boolean"
   ) {
      errors.push("hasPassword must be a boolean");
   }

   if (input.hasPassword) {
      if (
         !input.password ||
         typeof input.password !== "string" ||
         input.password.trim().length === 0
      ) {
         errors.push(
            "Password is required and must be a non-empty string if hasPassword is true"
         );
      }
   } else {
      if (input.password) {
         errors.push("Password should not be set if hasPassword is false");
      }
   }

   return errors;
}
