"use client";

import { cn } from "@/lib/utils";
import { motion } from "motion/react";

interface Props {
   children: React.ReactNode;
   delay?: number;
   className?: string;
}

export default function ClipReveal({
   children,
   delay = 0.6,
   className,
}: Props) {
   return (
      <div className="overflow-hidden">
         <motion.div
            initial={{ clipPath: "inset(100% 0% 0% 0%)" }}
            whileInView={{ clipPath: "inset(0% 0% 0% 0%)" }}
            transition={{ duration: 0.5, delay: delay }}
            viewport={{ once: true }}
            className={cn("w-full", className)}
         >
            {children}
         </motion.div>
      </div>
   );
}
