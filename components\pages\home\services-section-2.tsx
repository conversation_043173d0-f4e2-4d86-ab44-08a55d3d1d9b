"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { motion } from "framer-motion";
import {
   ArrowRight,
   Baby,
   Camera,
   CheckCircle,
   Users,
   Video,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";

export default function ServicesSection2() {
   const [activeService, setActiveService] = useState(0);

   const services = [
      {
         title: "Wedding Photography",
         shortDesc: "Capture your special day",
         fullDesc:
            "Capture your special day with stunning, timeless photographs that tell your unique love story. Our wedding photography service includes comprehensive coverage from preparation to reception, ensuring every precious moment is preserved forever.",
         image: "/images/wedding-shoots/wedding-shoot-5.JPG",
         icon: Camera,
         href: "/services/wedding",
         features: [
            "Full Day Coverage",
            "Engagement Session",
            "Online Gallery",
            "Print Release",
            "Second Photographer",
         ],
         price: "From £800",
      },
      {
         title: "Pre-Wedding Shoots",
         shortDesc: "Beautiful engagement sessions",
         fullDesc:
            "Beautiful engagement and pre-wedding photography sessions in stunning locations. We work with you to create romantic, natural images that capture your connection and excitement for your upcoming wedding day.",
         image: "/images/pre-wedding-shoots/pre-wedding-shoot-1.JP<PERSON>",
         icon: Users,
         href: "/services/pre-wedding",
         features: [
            "Location Scouting",
            "Outfit Changes",
            "Edited Photos",
            "Same Day Preview",
            "Consultation",
         ],
         price: "From £300",
      },
      {
         title: "Pregnancy Photography",
         shortDesc: "Elegant maternity portraits",
         fullDesc:
            "Celebrate this magical time with elegant maternity portraits that capture the beauty of pregnancy. Our sessions can be conducted in our studio or at outdoor locations, creating timeless images of this special journey.",
         image: "/images/pregnancy-shoots/pregnancy-shoot-1.jpg",
         icon: Baby,
         href: "/services/pregnancy",
         features: [
            "Studio or Outdoor",
            "Partner Included",
            "Wardrobe Assistance",
            "Digital Gallery",
            "Styling Guide",
         ],
         price: "From £250",
      },
      {
         title: "Child Dedication",
         shortDesc: "Precious ceremony moments",
         fullDesc:
            "Capture precious moments of your child's dedication ceremony with beautiful, heartfelt photography. We document the ceremony and create family portraits that celebrate this important milestone in your child's life.",
         image: "/images/child-dedication/child-dedication-1.JPG",
         icon: Video,
         href: "/services/child-dedication",
         features: [
            "Ceremony Coverage",
            "Family Portraits",
            "Candid Moments",
            "Digital Delivery",
            "Quick Turnaround",
         ],
         price: "From £200",
      },
   ];

   const currentService = services[activeService];
   const IconComponent = currentService.icon;

   return (
      <section className="py-20 bg-astral-grey">
         <div className="container max-w-[1400px] mx-auto px-4">
            <motion.div
               className="text-center mb-16"
               initial={{ opacity: 0, y: 20 }}
               whileInView={{ opacity: 1, y: 0 }}
               transition={{ duration: 0.6 }}
               viewport={{ once: true }}
            >
               <h2 className="text-4xl md:text-5xl font-playfair font-bold text-foreground mb-6">
                  Explore Our{" "}
                  <span className="bg-gradient-accent bg-clip-text text-transparent">
                     Services
                  </span>
               </h2>
               <p className="text-lg text-muted-foreground font-montserrat max-w-2xl mx-auto">
                  Click on each service to learn more about what we offer
               </p>
            </motion.div>

            {/* Service Tabs */}
            <div className="flex flex-wrap justify-center gap-4 mb-12">
               {services.map((service, index) => {
                  const ServiceIcon = service.icon;
                  return (
                     <motion.button
                        key={service.title}
                        onClick={() => setActiveService(index)}
                        className={`flex items-center gap-3 px-6 py-3 rounded-full transition-all duration-300 ${
                           activeService === index
                              ? "bg-gradient-accent text-white shadow-glow"
                              : "bg-card text-muted-foreground hover:bg-card/80 hover:text-foreground"
                        }`}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                     >
                        <ServiceIcon className="h-5 w-5" />
                        <span className="font-medium hidden sm:inline">
                           {service.title}
                        </span>
                        <span className="font-medium sm:hidden">
                           {service.title.split(" ")[0]}
                        </span>
                     </motion.button>
                  );
               })}
            </div>

            {/* Active Service Display */}
            <motion.div
               key={activeService}
               initial={{ opacity: 0, y: 20 }}
               animate={{ opacity: 1, y: 0 }}
               transition={{ duration: 0.5 }}
            >
               <Card className="overflow-hidden bg-card/50 backdrop-blur-sm border-astral-grey-light">
                  <div className="grid grid-cols-1 lg:grid-cols-2">
                     {/* Image */}
                     <div className="relative aspect-[4/3] lg:aspect-auto">
                        <Image
                           src={currentService.image}
                           alt={currentService.title}
                           fill
                           className="object-cover"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />

                        {/* Price Badge */}
                        <div className="absolute top-4 right-4 bg-gradient-accent text-white px-4 py-2 rounded-full font-semibold">
                           {currentService.price}
                        </div>
                     </div>

                     {/* Content */}
                     <CardContent className="p-8 lg:p-12 flex flex-col justify-center">
                        <div className="flex items-center gap-4 mb-6">
                           <div className="w-16 h-16 bg-gradient-accent rounded-full flex items-center justify-center">
                              <IconComponent className="h-8 w-8 text-white" />
                           </div>
                           <div>
                              <h3 className="text-3xl font-bold text-foreground mb-2">
                                 {currentService.title}
                              </h3>
                              <p className="text-primary font-medium">
                                 {currentService.shortDesc}
                              </p>
                           </div>
                        </div>

                        <p className="text-muted-foreground mb-8 leading-relaxed">
                           {currentService.fullDesc}
                        </p>

                        {/* Features */}
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-8">
                           {currentService.features.map((feature, idx) => (
                              <div
                                 key={idx}
                                 className="flex items-center gap-3"
                              >
                                 <CheckCircle className="h-5 w-5 text-primary flex-shrink-0" />
                                 <span className="text-sm text-muted-foreground">
                                    {feature}
                                 </span>
                              </div>
                           ))}
                        </div>

                        <div className="flex flex-col sm:flex-row gap-4">
                           <Button asChild className="flex-1">
                              <Link href={currentService.href}>
                                 Learn More
                                 <ArrowRight className="h-4 w-4 ml-2" />
                              </Link>
                           </Button>
                           <Button variant="outline" asChild className="flex-1">
                              <Link href="/contact">Get Quote</Link>
                           </Button>
                        </div>
                     </CardContent>
                  </div>
               </Card>
            </motion.div>
         </div>
      </section>
   );
}
