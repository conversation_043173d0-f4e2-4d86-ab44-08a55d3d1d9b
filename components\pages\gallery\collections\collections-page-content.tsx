"use client";

import {
   GalleryBreadcrumb,
   GalleryGrid,
   GallerySearch,
   LoadMoreButton,
} from "@/components/pages/gallery";
import { Button } from "@/components/ui/button";
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import {
   usePublicCollections,
   useSearchCollections,
} from "@/lib/hooks/use-collections";
import { motion } from "framer-motion";
import { SortAsc, SortDesc, Tag } from "lucide-react";
import { useEffect, useState } from "react";
import CollectionCard from "./collection-card";

const ITEMS_PER_PAGE = 12;

export default function CollectionsPageContent() {
   const [searchQuery, setSearchQuery] = useState("");
   const [currentPage, setCurrentPage] = useState(1);
   const [sortBy, setSortBy] = useState("createdAt");
   const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
   const [filters, setFilters] = useState<Record<string, string>>({});

   // Reset page when search or filters change
   useEffect(() => {
      setCurrentPage(1);
   }, [searchQuery, filters, sortBy, sortOrder]);

   // Fetch data based on search or normal listing
   const isSearching = searchQuery.trim().length > 0;

   const { data: collectionsData, isLoading: collectionsLoading } =
      usePublicCollections({
         page: currentPage,
         limit: ITEMS_PER_PAGE,
         sortBy,
         sortOrder,
      });

   const { data: searchData, isLoading: searchLoading } = useSearchCollections(
      searchQuery,
      {
         page: currentPage,
         limit: ITEMS_PER_PAGE,
         sortBy,
         sortOrder,
      }
   );

   // Determine which data to use
   const currentData = isSearching ? searchData : collectionsData;
   const isLoading = isSearching ? searchLoading : collectionsLoading;
   const collections = currentData?.data || [];
   const pagination = currentData?.pagination;

   // Breadcrumb items
   const breadcrumbItems = [
      { label: "Gallery", href: "/gallery" },
      { label: "Collections", isActive: true },
   ];

   // Filter options for collections
   const categoryOptions = [
      { id: "all", label: "All Themes", value: "all" },
      { id: "wedding", label: "Wedding", value: "wedding" },
      { id: "portrait", label: "Portrait", value: "portrait" },
      { id: "lifestyle", label: "Lifestyle", value: "lifestyle" },
      { id: "artistic", label: "Artistic", value: "artistic" },
      { id: "black-white", label: "Black & White", value: "black-white" },
      { id: "vintage", label: "Vintage", value: "vintage" },
   ];

   const colorOptions = [
      { id: "all", label: "All Colors", value: "all" },
      { id: "warm", label: "Warm Tones", value: "warm" },
      { id: "cool", label: "Cool Tones", value: "cool" },
      { id: "neutral", label: "Neutral", value: "neutral" },
      { id: "vibrant", label: "Vibrant", value: "vibrant" },
   ];

   const handleLoadMore = () => {
      if (pagination?.hasNext) {
         setCurrentPage((prev) => prev + 1);
      }
   };

   const handleSortChange = (value: string) => {
      const [newSortBy, newSortOrder] = value.split("-");
      setSortBy(newSortBy);
      setSortOrder(newSortOrder as "asc" | "desc");
   };

   return (
      <div className="py-8 mt-20">
         <div className="container mx-auto px-4">
            {/* Breadcrumb */}
            <GalleryBreadcrumb items={breadcrumbItems} />

            {/* Header */}
            <div className="mb-12">
               <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  className="text-center mb-8"
               >
                  <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-4 flex items-center justify-center gap-3">
                     Collections
                  </h1>
                  <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
                     Explore our curated collections, each capturing the essence
                     and memorable moments of special events through our
                     creative lens.
                  </p>
               </motion.div>

               {/* Search */}
               <GallerySearch
                  onSearch={setSearchQuery}
                  placeholder="Search collections by name or theme..."
                  className="mb-8"
               />

               {/* Controls */}
               <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
                  {/* Sort */}
                  <div className="flex items-center gap-2">
                     <span className="text-sm text-muted-foreground">
                        Sort by:
                     </span>
                     <Select
                        value={`${sortBy}-${sortOrder}`}
                        onValueChange={handleSortChange}
                     >
                        <SelectTrigger className="w-48">
                           <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                           <SelectItem value="createdAt-desc">
                              <div className="flex items-center gap-2">
                                 <SortDesc className="w-4 h-4" />
                                 Newest First
                              </div>
                           </SelectItem>
                           <SelectItem value="createdAt-asc">
                              <div className="flex items-center gap-2">
                                 <SortAsc className="w-4 h-4" />
                                 Oldest First
                              </div>
                           </SelectItem>
                           <SelectItem value="name-asc">
                              <div className="flex items-center gap-2">
                                 <SortAsc className="w-4 h-4" />
                                 Name A-Z
                              </div>
                           </SelectItem>
                           <SelectItem value="name-desc">
                              <div className="flex items-center gap-2">
                                 <SortDesc className="w-4 h-4" />
                                 Name Z-A
                              </div>
                           </SelectItem>
                        </SelectContent>
                     </Select>
                  </div>
               </div>

               {/* Active Filters */}
               {Object.keys(filters).some((key) => filters[key]) && (
                  <div className="flex flex-wrap gap-2 mt-4">
                     {Object.entries(filters).map(([key, value]) => {
                        if (!value) return null;

                        const label =
                           key === "theme"
                              ? categoryOptions.find(
                                   (opt) => opt.value === value
                                )?.label || value
                              : colorOptions.find((opt) => opt.value === value)
                                   ?.label || value;

                        return (
                           <Button
                              key={key}
                              variant="secondary"
                              size="sm"
                              onClick={() =>
                                 setFilters((prev) => ({ ...prev, [key]: "" }))
                              }
                              className="h-8 px-3 text-xs"
                           >
                              {label} ×
                           </Button>
                        );
                     })}
                     <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setFilters({})}
                        className="h-8 px-3 text-xs text-muted-foreground hover:text-foreground"
                     >
                        Clear All
                     </Button>
                  </div>
               )}
            </div>

            {/* Results Info */}
            {isSearching && (
               <div className="mb-8">
                  {searchLoading ? (
                     <p className="text-muted-foreground">
                        Searching collections...
                     </p>
                  ) : (
                     <p className="text-muted-foreground">
                        {collections.length > 0 ? (
                           <>
                              Found {pagination?.total || collections.length}{" "}
                              collections for &quot;
                              <span className="text-foreground font-medium">
                                 {searchQuery}
                              </span>
                              &quot;
                           </>
                        ) : (
                           <>
                              No collections found for &quot;
                              <span className="text-foreground font-medium">
                                 {searchQuery}
                              </span>
                              &quot;
                           </>
                        )}
                     </p>
                  )}
               </div>
            )}

            {/* Collections Grid */}
            {isLoading ? (
               <GalleryGrid columns={3}>
                  {Array.from({ length: 3 }).map((_, i) => (
                     <div key={i} className="space-y-4">
                        <Skeleton className="h-48 w-full rounded-3xl" />
                     </div>
                  ))}
               </GalleryGrid>
            ) : collections.length > 0 ? (
               <>
                  <GalleryGrid columns={3} className="mb-12">
                     {collections.map((collection) => (
                        <CollectionCard
                           key={collection._id as string}
                           collection={collection}
                           showStats={true}
                           showDescription={true}
                        />
                     ))}
                  </GalleryGrid>

                  {/* Pagination */}
                  {pagination && (
                     <div className="flex flex-col items-center gap-4">
                        {/* Load More Button */}
                        {pagination.hasNext && (
                           <LoadMoreButton
                              onClick={handleLoadMore}
                              isLoading={isLoading}
                              hasMore={pagination.hasNext}
                              buttonText="Load More Collections"
                              loadingText="Loading more collections..."
                           />
                        )}

                        {/* Pagination Info */}
                        <div className="text-sm text-muted-foreground text-center">
                           Showing {collections.length} of {pagination.total}{" "}
                           collections
                           {pagination.totalPages > 1 && (
                              <span className="ml-2">
                                 (Page {pagination.page} of{" "}
                                 {pagination.totalPages})
                              </span>
                           )}
                        </div>
                     </div>
                  )}
               </>
            ) : (
               <div className="text-center py-20">
                  <Tag className="w-20 h-20 mx-auto mb-6 text-muted-foreground/50" />
                  <h3 className="text-2xl font-semibold text-foreground mb-4">
                     {isSearching
                        ? "No Collections Found"
                        : "No Collections Available"}
                  </h3>
                  <p className="text-muted-foreground text-lg mb-8 max-w-md mx-auto">
                     {isSearching
                        ? "Try adjusting your search terms or filters to find what you're looking for."
                        : "We're working on creating new collections. Check back soon!"}
                  </p>
                  {(isSearching ||
                     Object.keys(filters).some((key) => filters[key])) && (
                     <Button
                        onClick={() => {
                           setSearchQuery("");
                           setFilters({});
                        }}
                        variant="outline"
                     >
                        Clear Search & Filters
                     </Button>
                  )}
               </div>
            )}
         </div>
      </div>
   );
}
