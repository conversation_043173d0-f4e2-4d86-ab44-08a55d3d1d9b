"use client";

import Image from "next/image";
import { useState } from "react";

interface PortfolioItem {
   id: string;
   src: string;
   alt: string;
   category: string;
   title?: string;
}

interface PortfolioGridProps {
   items: PortfolioItem[];
   showFilter?: boolean;
   columns?: 2 | 3 | 4;
}

const categories = [
   { id: "all", name: "All" },
   { id: "wedding", name: "Wedding" },
   { id: "pre-wedding", name: "Pre-wedding" },
   { id: "pregnancy", name: "Pregnancy" },
   { id: "child-dedication", name: "Child Dedication" },
];

export default function PortfolioGrid({
   items,
   showFilter = true,
   columns = 3,
}: PortfolioGridProps) {
   const [activeFilter, setActiveFilter] = useState("all");
   const [selectedImage, setSelectedImage] = useState<PortfolioItem | null>(
      null
   );

   const filteredItems =
      activeFilter === "all"
         ? items
         : items.filter((item) => item.category === activeFilter);

   const gridCols = {
      2: "grid-cols-1 md:grid-cols-2",
      3: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
      4: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",
   };

   return (
      <div>
         {/* Filter Buttons */}
         {showFilter && (
            <div className="flex flex-wrap justify-center gap-4 mb-8">
               {categories.map((category) => (
                  <button
                     key={category.id}
                     onClick={() => setActiveFilter(category.id)}
                     className={`px-6 py-2 rounded-full font-medium transition-all duration-200 ${
                        activeFilter === category.id
                           ? "bg-primary text-white shadow-lg"
                           : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                     }`}
                  >
                     {category.name}
                  </button>
               ))}
            </div>
         )}

         {/* Grid */}
         <div className={`grid ${gridCols[columns]} gap-6`}>
            {filteredItems.map((item) => (
               <div
                  key={item.id}
                  className="group relative overflow-hidden rounded-lg cursor-pointer transform transition-all duration-300 hover:scale-105 hover:shadow-xl"
                  onClick={() => setSelectedImage(item)}
               >
                  <div className="relative aspect-square">
                     <Image
                        src={item.src}
                        alt={item.alt}
                        fill
                        className="object-cover transition-transform duration-300 group-hover:scale-110"
                     />
                     <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-300 flex items-center justify-center">
                        <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                           <svg
                              className="w-12 h-12 text-white"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                           >
                              <path
                                 strokeLinecap="round"
                                 strokeLinejoin="round"
                                 strokeWidth={2}
                                 d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"
                              />
                           </svg>
                        </div>
                     </div>
                  </div>
                  {item.title && (
                     <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
                        <h3 className="text-white font-medium">{item.title}</h3>
                     </div>
                  )}
               </div>
            ))}
         </div>

         {/* Lightbox Modal */}
         {selectedImage && (
            <div
               className="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4"
               onClick={() => setSelectedImage(null)}
            >
               <div className="relative max-w-4xl max-h-full">
                  <button
                     onClick={() => setSelectedImage(null)}
                     className="absolute -top-12 right-0 text-white hover:text-gray-300 transition-colors duration-200"
                  >
                     <svg
                        className="w-8 h-8"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                     >
                        <path
                           strokeLinecap="round"
                           strokeLinejoin="round"
                           strokeWidth={2}
                           d="M6 18L18 6M6 6l12 12"
                        />
                     </svg>
                  </button>
                  <div className="relative">
                     <Image
                        src={selectedImage.src}
                        alt={selectedImage.alt}
                        width={800}
                        height={600}
                        className="max-w-full max-h-[80vh] object-contain"
                     />
                     {selectedImage.title && (
                        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6">
                           <h3 className="text-white text-xl font-medium">
                              {selectedImage.title}
                           </h3>
                        </div>
                     )}
                  </div>
               </div>
            </div>
         )}

         {/* No results message */}
         {filteredItems.length === 0 && (
            <div className="text-center py-12">
               <p className="text-gray-500 text-lg">
                  No images found for this category.
               </p>
            </div>
         )}
      </div>
   );
}
