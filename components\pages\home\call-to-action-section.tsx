import ElementReveal from "@/components/animations/element-reveal";
import TextReveal from "@/components/animations/text-reveal";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import Link from "next/link";

export default function CallToActionSection() {
   return (
      <>
         <section className="py-20 bg-background">
            <div className="container mx-auto px-6 text-center">
               <div className="max-w-3xl mx-auto">
                  <TextReveal>
                     <h2 className="text-4xl md:text-5xl font-playfair font-bold text-foreground mb-6">
                        Ready to{" "}
                        <span className="bg-gradient-accent bg-clip-text text-transparent">
                           Capture Your Story?
                        </span>
                     </h2>
                  </TextReveal>
                  <TextReveal className="mb-8">
                     <p className="text-lg text-muted-foreground font-montserrat leading-relaxed">
                        Let&apos;s create something magical together. Contact us
                        today to discuss your photography and videography needs,
                        and let us help you preserve your most precious moments.
                     </p>
                  </TextReveal>
                  <ElementReveal className="flex flex-col items-center sm:flex-row gap-4 justify-center">
                     <Button
                        asChild
                        size="lg"
                        className="bg-gradient-accent hover:opacity-90 font-montserrat font-semibold text-lg !px-8"
                     >
                        <Link href="/contact">
                           Contact Us Today{" "}
                           <ArrowRight className="ml-2 h-5 w-5" />
                        </Link>
                     </Button>
                     <Button
                        asChild
                        variant="outline"
                        size="lg"
                        className="border-primary/50 hover:bg-primary/5 font-montserrat font-semibold text-lg px-8"
                     >
                        <Link href="/services">View Our Services</Link>
                     </Button>
                  </ElementReveal>
               </div>
            </div>
         </section>
      </>
   );
}
