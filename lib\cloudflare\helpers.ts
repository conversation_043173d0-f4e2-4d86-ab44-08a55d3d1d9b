/**
 * Generate a unique key for file storage
 */
export function generateUniqueKey(originalName: string): string {
   const timestamp = Date.now();
   const randomString = Math.random().toString(36).substring(2, 15);
   const extension = originalName.split(".").pop() || "";
   const nameWithoutExtension = originalName.replace(/\.[^/.]+$/, "");

   // Sanitize filename
   const sanitizedName = nameWithoutExtension
      .replace(/[^a-zA-Z0-9-_]/g, "-")
      .replace(/-+/g, "-")
      .replace(/^-|-$/g, "");

   return `gallery/${sanitizedName}-${timestamp}-${randomString}.${extension}`;
}

/**
 * Extract file key from R2 URL
 * @param url - The full R2 URL (e.g., https://domain.com/gallery/filename-timestamp-random.ext)
 * @returns The file key (e.g., gallery/filename-timestamp-random.ext)
 */
export function extractFileKeyFromUrl(url: string): string {
   try {
      const urlObj = new URL(url);
      const fileKey = urlObj.pathname.substring(1); // Remove leading slash

      if (!fileKey) {
         throw new Error("No file key found in URL");
      }

      return fileKey;
   } catch (error) {
      throw new Error(
         `Invalid URL format: ${
            error instanceof Error ? error.message : "Unknown error"
         }`
      );
   }
}
