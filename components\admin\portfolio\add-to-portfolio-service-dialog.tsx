"use client";

import CreatePortfolioServiceDialog from "@/components/admin/portfolio/create-portfolio-service-dialog";
import { But<PERSON> } from "@/components/ui/button";
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogFooter,
   DialogHeader,
   DialogTitle,
} from "@/components/ui/dialog";
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from "@/components/ui/select";
import {
   useAddExistingImagesToPortfolioService,
   usePortfolioServices,
} from "@/lib/hooks/use-portfolio";
import { Briefcase, Plus } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

interface AddToPortfolioServiceDialogProps {
   open: boolean;
   onOpenChange: (open: boolean) => void;
   imageIds: string[];
   onSuccess?: () => void;
}

export default function AddToPortfolioServiceDialog({
   open,
   onOpenChange,
   imageIds,
   onSuccess,
}: AddToPortfolioServiceDialogProps) {
   const [selectedServiceId, setSelectedServiceId] = useState<string>("");
   const [showCreateService, setShowCreateService] = useState(false);

   const { data: servicesData, isLoading: servicesLoading } =
      usePortfolioServices({ limit: 100 });
   const addToServiceMutation = useAddExistingImagesToPortfolioService();

   const services = servicesData || [];

   const handleSave = async () => {
      if (!selectedServiceId) {
         toast.error("Please select a portfolio service");
         return;
      }

      try {
         const formData = new FormData();
         formData.append("serviceId", selectedServiceId);
         formData.append("imageIds", JSON.stringify(imageIds));

         await addToServiceMutation.mutateAsync(formData);
         onOpenChange(false);
         onSuccess?.();
      } catch (error) {
         console.error("Error adding images to portfolio service:", error);
         // Error handling is done in the hook
      }
   };

   const handleCreateService = () => {
      setShowCreateService(true);
   };

   const handleCreateServiceSuccess = (serviceId: string) => {
      setShowCreateService(false);
      setSelectedServiceId(serviceId);
      toast.success("Portfolio service created successfully");
   };

   const imageText = imageIds.length === 1 ? "image" : "images";

   return (
      <Dialog open={open} onOpenChange={onOpenChange}>
         <DialogContent className="sm:max-w-md">
            <DialogHeader>
               <DialogTitle>Add to Portfolio Service</DialogTitle>
               <DialogDescription>
                  Select which portfolio service these {imageIds.length}{" "}
                  {imageText} should be added to.
               </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
               {servicesLoading ? (
                  <div className="flex items-center justify-center h-32">
                     <div className="text-sm text-muted-foreground">
                        Loading portfolio services...
                     </div>
                  </div>
               ) : services.length === 0 ? (
                  <div className="flex flex-col items-center justify-center h-64 text-muted-foreground">
                     <div className="text-center">
                        <Briefcase className="w-16 h-16 mx-auto mb-4 opacity-50" />
                        <h3 className="text-lg font-medium mb-2 text-foreground">
                           No Portfolio Services Available
                        </h3>
                        <p className="text-sm mb-4 max-w-sm">
                           Create your first portfolio service to organize your
                           images by service type (weddings, pre-weddings,
                           etc.).
                        </p>
                        <Button
                           onClick={handleCreateService}
                           className="bg-gradient-accent hover:opacity-90"
                        >
                           Create Portfolio Service
                        </Button>
                     </div>
                  </div>
               ) : (
                  <div className="space-y-3">
                     <div className="text-sm font-medium">
                        Select Portfolio Service:
                     </div>
                     <Select
                        value={selectedServiceId}
                        onValueChange={setSelectedServiceId}
                     >
                        <SelectTrigger>
                           <SelectValue placeholder="Choose a portfolio service..." />
                        </SelectTrigger>
                        <SelectContent>
                           {services.map((service) => (
                              <SelectItem
                                 key={service._id?.toString() || ""}
                                 value={service._id?.toString() || ""}
                              >
                                 <div className="flex items-center space-x-2">
                                    <Briefcase className="w-4 h-4 text-muted-foreground" />
                                    <span>{service.name}</span>
                                    <span className="text-xs text-muted-foreground">
                                       ({service.imageCount} images)
                                    </span>
                                 </div>
                              </SelectItem>
                           ))}
                        </SelectContent>
                     </Select>

                     <Button
                        variant="outline"
                        onClick={handleCreateService}
                        disabled={addToServiceMutation.isPending}
                        className="w-full"
                     >
                        <Plus className="w-4 h-4" />
                        Create New Portfolio Service
                     </Button>
                  </div>
               )}
            </div>

            {services.length > 0 && (
               <DialogFooter>
                  <Button variant="outline" onClick={() => onOpenChange(false)}>
                     Cancel
                  </Button>
                  <Button
                     onClick={handleSave}
                     disabled={
                        addToServiceMutation.isPending ||
                        servicesLoading ||
                        !selectedServiceId
                     }
                     className="bg-gradient-accent hover:opacity-90"
                  >
                     {addToServiceMutation.isPending
                        ? "Adding..."
                        : `Add ${imageIds.length} ${imageText}`}
                  </Button>
               </DialogFooter>
            )}

            <CreatePortfolioServiceDialog
               open={showCreateService}
               onOpenChange={setShowCreateService}
               onSuccess={handleCreateServiceSuccess}
            />
         </DialogContent>
      </Dialog>
   );
}
