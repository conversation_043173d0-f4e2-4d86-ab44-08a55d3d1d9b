"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
   ArchiveBoxArrowDownIcon,
   TagIcon,
   TrashIcon,
   XMarkIcon,
} from "@heroicons/react/24/solid";

interface FloatingActionBarProps {
   selectedCount: number;
   onClearSelection: () => void;
   onAddToAlbum: () => void;
   onAddToCollection: () => void;
   onDelete: () => void;
}

export default function FloatingActionBar({
   selectedCount,
   onClearSelection,
   onAddToAlbum,
   onAddToCollection,
   onDelete,
}: FloatingActionBarProps) {
   if (selectedCount === 0) return null;

   return (
      <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50">
         <div className="bg-background/95 backdrop-blur-sm border border-border/50 rounded-lg shadow-lg px-4 py-3">
            <div className="flex items-center space-x-2">
               <span className="text-sm font-medium text-foreground whitespace-nowrap pr-2">
                  {selectedCount} image{selectedCount !== 1 ? "s" : ""} selected
               </span>
               <Button
                  variant="outline"
                  onClick={onClearSelection}
                  className="h-8 px-2"
               >
                  <XMarkIcon className="w-4 h-4" />
                  <span className="hidden md:inline">Clear</span>
               </Button>

               <Button
                  variant="outline"
                  onClick={onAddToAlbum}
                  className="h-8 px-2 sm:px-3"
               >
                  <ArchiveBoxArrowDownIcon className="w-4 h-4" />
                  <span className="hidden md:inline">Add to Album</span>
               </Button>
               <Button
                  variant="outline"
                  onClick={onAddToCollection}
                  className="h-8 px-2 sm:px-3"
               >
                  <TagIcon className="w-4 h-4" />
                  <span className="hidden md:inline">Add to Collection</span>
               </Button>
               <Button
                  variant="destructive"
                  onClick={onDelete}
                  className="h-8 px-2 sm:px-3"
               >
                  <TrashIcon className="w-4 h-4" />
                  <span className="hidden md:inline">Delete</span>
               </Button>
            </div>
         </div>
      </div>
   );
}
