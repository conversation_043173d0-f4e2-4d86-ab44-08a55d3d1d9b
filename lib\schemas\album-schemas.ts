import { z } from "zod";

/**
 * Schema for creating a new album
 */
export const createAlbumSchema = z
   .object({
      name: z
         .string()
         .min(1, "Name is required")
         .max(100, "Name must be less than 100 characters")
         .trim(),
      description: z
         .string()
         .max(500, "Description must be less than 500 characters")
         .optional()
         .or(z.literal("")),
      hasPassword: z.boolean().default(false),
      password: z.string().optional(),
      coverImageUrl: z.string().optional().nullable(),
   })
   .superRefine((data, ctx) => {
      if (data.hasPassword) {
         if (!data.password || data.password.trim().length === 0) {
            ctx.addIssue({
               code: z.ZodIssueCode.custom,
               message: "Password is required if 'Add password' is enabled.",
               path: ["password"],
            });
         }
      } else {
         if (data.password) {
            ctx.addIssue({
               code: z.ZodIssueCode.custom,
               message:
                  "Password should not be set if 'Add password' is disabled.",
               path: ["password"],
            });
         }
      }
   });

/**
 * Schema for updating an album
 */
export const updateAlbumSchema = z
   .object({
      name: z
         .string()
         .min(1, "Name is required")
         .max(100, "Name must be less than 100 characters")
         .trim()
         .optional(),
      description: z
         .string()
         .max(500, "Description must be less than 500 characters")
         .optional()
         .or(z.literal("")),
      hasPassword: z.boolean().optional(),
      password: z.string().optional(),
      coverImageUrl: z.string().optional().nullable(),
   })
   .superRefine((data, ctx) => {
      if (data.hasPassword) {
         if (!data.password || data.password.trim().length === 0) {
            ctx.addIssue({
               code: z.ZodIssueCode.custom,
               message: "Password is required if 'Add password' is enabled.",
               path: ["password"],
            });
         }
      } else {
         if (data.password) {
            ctx.addIssue({
               code: z.ZodIssueCode.custom,
               message:
                  "Password should not be set if 'Add password' is disabled.",
               path: ["password"],
            });
         }
      }
   });

/**
 * Type inference for create album form
 */
export type CreateAlbumFormData = z.infer<typeof createAlbumSchema>;

/**
 * Type inference for update album form
 */
export type UpdateAlbumFormData = z.infer<typeof updateAlbumSchema>;
