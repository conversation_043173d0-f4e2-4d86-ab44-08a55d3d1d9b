"use client";

import {
   deleteImageAction,
   uploadImage,
   uploadMultipleImages,
} from "@/lib/actions/upload-actions";
import { PaginationOptions, UpdateImageInput } from "@/lib/models";
import { setAlbumCover } from "@/lib/services/album-service";
import {
   bulkDeleteImagesAndFiles,
   bulkMoveImagesToAlbum,
   bulkUpdateImageCollections,
   deleteImageAndFile,
   getImageById,
   getImages,
   getRecentImages,
   getUngroupedImages,
   moveImageToAlbum,
   searchImages,
   updateImage,
   updateImageCollections,
} from "@/lib/services/image-service";
import {
   useInfiniteQuery,
   useMutation,
   useQuery,
   useQueryClient,
} from "@tanstack/react-query";
import { toast } from "sonner";
import { invalidationKeys, queryKeys } from "./query-keys";

/**
 * Hook to fetch images with pagination and filtering
 */
export function useImages(
   options: PaginationOptions & {
      albumId?: string | null;
      collectionId?: string | null;
   } = {}
) {
   return useQuery({
      queryKey: queryKeys.images.list(options),
      queryFn: () => getImages(options),
      staleTime: 5 * 60 * 1000, // 5 minutes
   });
}

/**
 * Hook to fetch images with infinite scroll pagination
 */
export function useInfiniteImages(
   options: PaginationOptions & {
      albumId?: string | null;
      collectionId?: string | null;
   } = {}
) {
   return useInfiniteQuery({
      queryKey: queryKeys.images.infiniteList(options),
      queryFn: ({ pageParam = 1 }) =>
         getImages({ ...options, page: pageParam }),
      getNextPageParam: (lastPage) => {
         if (lastPage.pagination.hasNext) {
            return lastPage.pagination.page + 1;
         }
         return undefined;
      },
      initialPageParam: 1,
      staleTime: 5 * 60 * 1000, // 5 minutes
   });
}

/**
 * Hook to fetch ungrouped images
 */
export function useUngroupedImages(options: PaginationOptions = {}) {
   return useQuery({
      queryKey: queryKeys.images.ungrouped(options),
      queryFn: () => getUngroupedImages(options),
      staleTime: 5 * 60 * 1000, // 5 minutes
   });
}

/**
 * Hook to fetch ungrouped images with infinite scroll pagination
 */
export function useInfiniteUngroupedImages(options: PaginationOptions = {}) {
   return useInfiniteQuery({
      queryKey: queryKeys.images.infiniteUngrouped(options),
      queryFn: ({ pageParam = 1 }) =>
         getUngroupedImages({ ...options, page: pageParam }),
      getNextPageParam: (lastPage) => {
         if (lastPage.pagination.hasNext) {
            return lastPage.pagination.page + 1;
         }
         return undefined;
      },
      initialPageParam: 1,
      staleTime: 5 * 60 * 1000, // 5 minutes
   });
}

/**
 * Hook to fetch a single image by ID
 */
export function useImage(id: string) {
   return useQuery({
      queryKey: queryKeys.images.detail(id),
      queryFn: () => getImageById(id),
      enabled: !!id,
      staleTime: 10 * 60 * 1000, // 10 minutes
   });
}

/**
 * Hook to search images
 */
export function useSearchImages(
   query: string,
   options: PaginationOptions = {}
) {
   return useQuery({
      queryKey: queryKeys.images.search(query, options),
      queryFn: () => searchImages(query, options),
      enabled: !!query && query.length > 0,
      staleTime: 2 * 60 * 1000, // 2 minutes
   });
}

/**
 * Hook to fetch recent images with album and collection information
 */
export function useRecentImages(limit: number = 6) {
   return useQuery({
      queryKey: queryKeys.images.recent(limit),
      queryFn: () => getRecentImages(limit),
      staleTime: 2 * 60 * 1000, // 2 minutes
   });
}

/**
 * Hook to upload a single image
 */
export function useUploadImage() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: uploadImage,
      onSuccess: (data) => {
         if (data.success) {
            // Invalidate relevant queries
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.allImages(),
            });
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.recentImages(),
            });
         } else {
            toast.error(data.error || "Failed to upload image");
         }
      },
      onError: (error) => {
         toast.error(
            error instanceof Error ? error.message : "Failed to upload image"
         );
      },
   });
}

/**
 * Hook to upload multiple images
 */
export function useUploadMultipleImages() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: uploadMultipleImages,
      onSuccess: (data) => {
         if (data.success) {
            // Invalidate relevant queries
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.allImages(),
            });
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.recentImages(),
            });
         } else {
            toast.error(data.error || "Failed to upload images");
         }
      },
      onError: (error) => {
         toast.error(
            error instanceof Error ? error.message : "Failed to upload images"
         );
      },
   });
}

/**
 * Hook to update image metadata
 */
export function useUpdateImage() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: ({ id, input }: { id: string; input: UpdateImageInput }) =>
         updateImage(id, input),
      onSuccess: (data, variables) => {
         if (data) {
            // Update the specific image in cache
            queryClient.setQueryData(
               queryKeys.images.detail(variables.id),
               data
            );

            // Invalidate lists to ensure consistency
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.imagesList(),
            });
         }
      },
      onError: (error) => {
         toast.error(
            error instanceof Error ? error.message : "Failed to update image"
         );
      },
   });
}

/**
 * Hook to delete an image
 */
export function useDeleteImage() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: deleteImageAction,
      onSuccess: (data) => {
         if (data.success) {
            // Invalidate all image queries
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.allImages(),
            });
         } else {
            toast.error(data.error || "Failed to delete image");
         }
      },
      onError: (error) => {
         toast.error(
            error instanceof Error ? error.message : "Failed to delete image"
         );
      },
   });
}

/**
 * Hook to delete an image completely (from DB and storage)
 */
export function useDeleteImageCompletely() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: deleteImageAndFile,
      onSuccess: (success) => {
         if (success) {
            // Invalidate all image queries
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.allImages(),
            });
            // Also invalidate album queries since image counts might change
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.albumsList(),
            });
            // And collection queries
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.collectionsList(),
            });
         } else {
            toast.error("Failed to delete image");
         }
      },
      onError: (error) => {
         toast.error(
            error instanceof Error ? error.message : "Failed to delete image"
         );
      },
   });
}

/**
 * Hook to move image to album
 */
export function useMoveImageToAlbum() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: ({
         imageId,
         albumId,
      }: {
         imageId: string;
         albumId: string;
      }) => moveImageToAlbum(imageId, albumId),
      onSuccess: (data, variables) => {
         if (data) {
            // Update the specific image in cache
            queryClient.setQueryData(
               queryKeys.images.detail(variables.imageId),
               data
            );

            // Invalidate all image lists and album data
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.allImages(),
            });
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.albumsList(),
            });
         }
      },
      onError: (error) => {
         toast.error(
            error instanceof Error ? error.message : "Failed to move image"
         );
      },
   });
}

/**
 * Hook to update image collections
 */
export function useUpdateImageCollections() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: ({
         imageId,
         collectionIds,
      }: {
         imageId: string;
         collectionIds: string[];
      }) => updateImageCollections(imageId, collectionIds),
      onSuccess: (data, variables) => {
         if (data) {
            // Update the specific image in cache
            queryClient.setQueryData(
               queryKeys.images.detail(variables.imageId),
               data
            );

            // Invalidate all image lists and collection data
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.allImages(),
            });
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.collectionsList(),
            });
         }
      },
      onError: (error) => {
         toast.error(
            error instanceof Error
               ? error.message
               : "Failed to update collections"
         );
      },
   });
}

/**
 * Hook to set album cover
 */
export function useSetAlbumCover() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: ({
         albumId,
         imageUrl,
      }: {
         albumId: string;
         imageUrl: string;
      }) => setAlbumCover(albumId, imageUrl),
      onSuccess: (data, variables) => {
         if (data) {
            // Update the specific album in cache
            queryClient.setQueryData(
               queryKeys.albums.detail(variables.albumId),
               data
            );

            // Invalidate album lists to ensure consistency
            queryClient.invalidateQueries({
               queryKey: queryKeys.albums.lists(),
            });
         }
      },
      onError: (error) => {
         toast.error(
            error instanceof Error ? error.message : "Failed to set album cover"
         );
      },
   });
}

/**
 * Hook to bulk move images to album
 */
export function useBulkMoveImagesToAlbum() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: ({
         imageIds,
         albumId,
      }: {
         imageIds: string[];
         albumId: string;
      }) => bulkMoveImagesToAlbum(imageIds, albumId),
      onSuccess: (data, variables) => {
         if (data) {
            // Invalidate all image lists and album data
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.allImages(),
            });
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.albumsList(),
            });

            toast.success(
               `${variables.imageIds.length} images moved successfully`
            );
         }
      },
      onError: (error) => {
         toast.error(
            error instanceof Error ? error.message : "Failed to move images"
         );
      },
   });
}

/**
 * Hook to bulk update image collections
 */
export function useBulkUpdateImageCollections() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: ({
         imageIds,
         collectionIds,
      }: {
         imageIds: string[];
         collectionIds: string[];
      }) => bulkUpdateImageCollections(imageIds, collectionIds),
      onSuccess: (data, variables) => {
         if (data) {
            // Invalidate all image lists and collection data
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.allImages(),
            });
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.collectionsList(),
            });

            toast.success(
               `Collections updated for ${variables.imageIds.length} images`
            );
         }
      },
      onError: (error) => {
         toast.error(
            error instanceof Error
               ? error.message
               : "Failed to update collections"
         );
      },
   });
}

/**
 * Hook to bulk delete images
 */
export function useBulkDeleteImages() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: (imageIds: string[]) => bulkDeleteImagesAndFiles(imageIds),
      onSuccess: (data, variables) => {
         if (data) {
            // Invalidate all image queries
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.allImages(),
            });
            // Also invalidate album queries since image counts might change
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.albumsList(),
            });
            // And collection queries
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.collectionsList(),
            });
            toast.success(`${variables.length} images deleted successfully`);
         } else {
            toast.error("Failed to delete images");
         }
      },
      onError: (error) => {
         toast.error(
            error instanceof Error ? error.message : "Failed to delete images"
         );
      },
   });
}
