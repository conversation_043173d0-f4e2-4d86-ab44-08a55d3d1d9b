import { ReactNode } from "react";
import { cn } from "@/lib/utils";

interface ResponsiveContainerProps {
  children: ReactNode;
  className?: string;
  size?: "sm" | "md" | "lg" | "xl" | "full";
}

const sizeClasses = {
  sm: "max-w-2xl",
  md: "max-w-4xl", 
  lg: "max-w-6xl",
  xl: "max-w-7xl",
  full: "max-w-none"
};

export function ResponsiveContainer({ 
  children, 
  className = "", 
  size = "xl" 
}: ResponsiveContainerProps) {
  return (
    <div className={cn(
      "container mx-auto px-4 sm:px-6 lg:px-8",
      sizeClasses[size],
      className
    )}>
      {children}
    </div>
  );
}

interface ResponsiveGridProps {
  children: ReactNode;
  className?: string;
  cols?: {
    default?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  gap?: number;
}

export function ResponsiveGrid({ 
  children, 
  className = "",
  cols = { default: 1, md: 2, lg: 3 },
  gap = 6
}: ResponsiveGridProps) {
  const gridClasses = [
    `grid`,
    `gap-${gap}`,
    cols.default && `grid-cols-${cols.default}`,
    cols.sm && `sm:grid-cols-${cols.sm}`,
    cols.md && `md:grid-cols-${cols.md}`,
    cols.lg && `lg:grid-cols-${cols.lg}`,
    cols.xl && `xl:grid-cols-${cols.xl}`,
  ].filter(Boolean).join(" ");

  return (
    <div className={cn(gridClasses, className)}>
      {children}
    </div>
  );
}

interface ResponsiveTextProps {
  children: ReactNode;
  className?: string;
  size?: "sm" | "base" | "lg" | "xl" | "2xl" | "3xl" | "4xl";
  weight?: "normal" | "medium" | "semibold" | "bold";
  align?: "left" | "center" | "right";
}

const textSizeClasses = {
  sm: "text-sm md:text-base",
  base: "text-base md:text-lg",
  lg: "text-lg md:text-xl",
  xl: "text-xl md:text-2xl",
  "2xl": "text-2xl md:text-3xl",
  "3xl": "text-3xl md:text-4xl lg:text-5xl",
  "4xl": "text-4xl md:text-5xl lg:text-6xl"
};

const textWeightClasses = {
  normal: "font-normal",
  medium: "font-medium",
  semibold: "font-semibold",
  bold: "font-bold"
};

const textAlignClasses = {
  left: "text-left",
  center: "text-center",
  right: "text-right"
};

export function ResponsiveText({ 
  children, 
  className = "",
  size = "base",
  weight = "normal",
  align = "left"
}: ResponsiveTextProps) {
  return (
    <div className={cn(
      textSizeClasses[size],
      textWeightClasses[weight],
      textAlignClasses[align],
      className
    )}>
      {children}
    </div>
  );
}

interface ResponsiveSpacingProps {
  children: ReactNode;
  className?: string;
  py?: {
    default?: number;
    sm?: number;
    md?: number;
    lg?: number;
  };
  px?: {
    default?: number;
    sm?: number;
    md?: number;
    lg?: number;
  };
}

export function ResponsiveSpacing({ 
  children, 
  className = "",
  py = { default: 8, lg: 16 },
  px = { default: 4, sm: 6, lg: 8 }
}: ResponsiveSpacingProps) {
  const spacingClasses = [
    py.default && `py-${py.default}`,
    py.sm && `sm:py-${py.sm}`,
    py.md && `md:py-${py.md}`,
    py.lg && `lg:py-${py.lg}`,
    px.default && `px-${px.default}`,
    px.sm && `sm:px-${px.sm}`,
    px.md && `md:px-${px.md}`,
    px.lg && `lg:px-${px.lg}`,
  ].filter(Boolean).join(" ");

  return (
    <div className={cn(spacingClasses, className)}>
      {children}
    </div>
  );
}
