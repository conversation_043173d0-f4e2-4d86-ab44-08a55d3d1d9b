import CollectionPageContent from "@/components/pages/gallery/collections/collection-page-content";
import { getCollectionById } from "@/lib/services/collection-service";
import { Metadata } from "next";
import { notFound } from "next/navigation";
import { Suspense } from "react";

interface CollectionPageProps {
   params: Promise<{
      id: string;
   }>;
}

export async function generateMetadata({
   params,
}: CollectionPageProps): Promise<Metadata> {
   try {
      const { id } = await params;
      const collection = await getCollectionById(id);

      if (!collection) {
         return {
            title: "Collection Not Found - Astral Studios",
            description: "The requested collection could not be found.",
         };
      }

      return {
         title: `${collection.name} - Collections - Astral Studios`,
         description:
            collection.description ||
            `Explore the ${collection.name} collection featuring ${collection.imageCount} curated photographs by Astral Studios.`,
         keywords: [
            "collection",
            "photography",
            collection.name,
            "curated",
            "astral studios",
         ],
         openGraph: {
            title: `${collection.name} - Astral Studios`,
            description:
               collection.description ||
               `Explore the ${collection.name} collection featuring ${collection.imageCount} curated photographs by Astral Studios.`,
            type: "website",
            images: [
               {
                  url: "/images/collection-default.jpg", // You might want to add a cover image field to collections
                  width: 1200,
                  height: 630,
                  alt: collection.name,
               },
            ],
         },
         twitter: {
            card: "summary_large_image",
            title: `${collection.name} - Astral Studios`,
            description:
               collection.description ||
               `Explore the ${collection.name} collection featuring ${collection.imageCount} curated photographs by Astral Studios.`,
            images: ["/images/collection-default.jpg"],
         },
      };
   } catch (error) {
      console.error("Error generating metadata:", error);
      return {
         title: "Collection - Astral Studios",
         description:
            "View collection from Astral Studios photography gallery.",
      };
   }
}

export default async function CollectionPage({ params }: CollectionPageProps) {
   const { id } = await params;
   const collection = await getCollectionById(id);

   if (!collection) {
      notFound();
   }

   return (
      <Suspense fallback={<div>Loading collection...</div>}>
         <CollectionPageContent collection={collection} />
      </Suspense>
   );
}
