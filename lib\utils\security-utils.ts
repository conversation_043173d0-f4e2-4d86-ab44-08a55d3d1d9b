import { getSession } from "@/lib/auth/session";
import { AdminSession } from "@/lib/models";

/**
 * Input sanitization utilities for XSS prevention
 */
export class InputSanitizer {
   /**
    * Sanitize HTML content by removing potentially dangerous tags and attributes
    */
   static sanitizeHtml(input: string): string {
      if (!input || typeof input !== "string") {
         return "";
      }

      return (
         input
            // Remove script tags and their content
            .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, "")
            // Remove on* event handlers
            .replace(/\s*on\w+\s*=\s*["'][^"']*["']/gi, "")
            // Remove javascript: protocol
            .replace(/javascript:/gi, "")
            // Remove data: protocol (except for images)
            .replace(/data:(?!image\/)/gi, "")
            // Remove style attributes that could contain expressions
            .replace(/\s*style\s*=\s*["'][^"']*expression[^"']*["']/gi, "")
            // Remove potentially dangerous HTML tags
            .replace(
               /<(iframe|object|embed|form|input|textarea|select|button|link|meta|base)[^>]*>/gi,
               ""
            )
            .replace(
               /<\/(iframe|object|embed|form|input|textarea|select|button|link|meta|base)>/gi,
               ""
            )
      );
   }

   /**
    * Sanitize text input by removing HTML tags and encoding special characters
    */
   static sanitizeText(input: string): string {
      if (!input || typeof input !== "string") {
         return "";
      }

      return (
         input
            // Remove all HTML tags
            .replace(/<[^>]*>/g, "")
            // Encode special characters
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#x27;")
            .replace(/\//g, "&#x2F;")
            // Trim whitespace
            .trim()
      );
   }

   /**
    * Sanitize filename by removing dangerous characters
    */
   static sanitizeFilename(filename: string): string {
      if (!filename || typeof filename !== "string") {
         return "";
      }

      return (
         filename
            // Remove path traversal attempts
            .replace(/\.\./g, "")
            .replace(/[\/\\]/g, "")
            // Remove null bytes and control characters
            .replace(/[\x00-\x1f\x80-\x9f]/g, "")
            // Remove dangerous characters
            .replace(/[<>:"|?*]/g, "")
            // Limit to safe characters
            .replace(/[^a-zA-Z0-9._-]/g, "_")
            // Remove multiple underscores
            .replace(/_+/g, "_")
            // Remove leading/trailing underscores and dots
            .replace(/^[._]+|[._]+$/g, "")
            // Limit length
            .substring(0, 255)
      );
   }

   /**
    * Sanitize URL to prevent XSS and ensure it's safe
    */
   static sanitizeUrl(url: string): string {
      if (!url || typeof url !== "string") {
         return "";
      }

      // Remove dangerous protocols
      const dangerousProtocols = /^(javascript|data|vbscript|file|ftp):/i;
      if (dangerousProtocols.test(url)) {
         return "";
      }

      // Ensure URL starts with http/https or is relative
      if (!/^(https?:\/\/|\/)/i.test(url)) {
         return "";
      }

      return url.trim();
   }

   /**
    * Validate and sanitize portfolio service input
    */
   static sanitizePortfolioServiceInput(input: {
      name?: string;
      description?: string;
      coverImageUrl?: string;
   }): {
      name?: string;
      description?: string;
      coverImageUrl?: string;
   } {
      const sanitized: typeof input = {};

      if (input.name) {
         sanitized.name = this.sanitizeText(input.name);
      }

      if (input.description) {
         sanitized.description = this.sanitizeHtml(input.description);
      }

      if (input.coverImageUrl) {
         sanitized.coverImageUrl = this.sanitizeUrl(input.coverImageUrl);
      }

      return sanitized;
   }

   /**
    * Validate and sanitize portfolio image input
    */
   static sanitizePortfolioImageInput(input: {
      name?: string;
      altText?: string;
      url?: string;
   }): {
      name?: string;
      altText?: string;
      url?: string;
   } {
      const sanitized: typeof input = {};

      if (input.name) {
         sanitized.name = this.sanitizeFilename(input.name);
      }

      if (input.altText) {
         sanitized.altText = this.sanitizeText(input.altText);
      }

      if (input.url) {
         sanitized.url = this.sanitizeUrl(input.url);
      }

      return sanitized;
   }
}

/**
 * Rate limiting utilities
 */
export class RateLimiter {
   private static uploadAttempts = new Map<
      string,
      { count: number; resetTime: number }
   >();
   private static actionAttempts = new Map<
      string,
      { count: number; resetTime: number }
   >();

   /**
    * Check if upload rate limit is exceeded
    */
   static checkUploadRateLimit(
      identifier: string,
      maxAttempts: number = 10,
      windowMs: number = 60000
   ): boolean {
      const now = Date.now();
      const key = `upload_${identifier}`;
      const attempts = this.uploadAttempts.get(key);

      if (!attempts || now > attempts.resetTime) {
         // Reset or initialize counter
         this.uploadAttempts.set(key, { count: 1, resetTime: now + windowMs });
         return false; // Not rate limited
      }

      if (attempts.count >= maxAttempts) {
         return true; // Rate limited
      }

      // Increment counter
      attempts.count++;
      return false; // Not rate limited
   }

   /**
    * Check if action rate limit is exceeded
    */
   static checkActionRateLimit(
      identifier: string,
      action: string,
      maxAttempts: number = 30,
      windowMs: number = 60000
   ): boolean {
      const now = Date.now();
      const key = `${action}_${identifier}`;
      const attempts = this.actionAttempts.get(key);

      if (!attempts || now > attempts.resetTime) {
         // Reset or initialize counter
         this.actionAttempts.set(key, { count: 1, resetTime: now + windowMs });
         return false; // Not rate limited
      }

      if (attempts.count >= maxAttempts) {
         return true; // Rate limited
      }

      // Increment counter
      attempts.count++;
      return false; // Not rate limited
   }

   /**
    * Clean up expired rate limit entries
    */
   static cleanup(): void {
      const now = Date.now();

      // Clean upload attempts
      for (const [key, attempts] of this.uploadAttempts.entries()) {
         if (now > attempts.resetTime) {
            this.uploadAttempts.delete(key);
         }
      }

      // Clean action attempts
      for (const [key, attempts] of this.actionAttempts.entries()) {
         if (now > attempts.resetTime) {
            this.actionAttempts.delete(key);
         }
      }
   }
}

/**
 * Authentication error messages
 */
export const AUTH_ERRORS = {
   AUTHENTICATION_REQUIRED:
      "Authentication required. Please log in to access portfolio management.",
   PERMISSION_DENIED:
      "Permission denied. You don't have access to this resource.",
} as const;

/**
 * Security validation utilities
 */
export class SecurityValidator {
   /**
    * Validate file upload security
    */
   static validateFileUpload(
      filename: string,
      contentType: string,
      fileSize: number
   ): {
      isValid: boolean;
      errors: string[];
   } {
      const errors: string[] = [];

      // Validate filename
      if (!filename || filename.trim().length === 0) {
         errors.push("Filename is required");
      } else if (filename.length > 255) {
         errors.push("Filename is too long (max 255 characters)");
      }

      // Check for dangerous file extensions
      const dangerousExtensions = [
         ".exe",
         ".bat",
         ".cmd",
         ".com",
         ".pif",
         ".scr",
         ".vbs",
         ".js",
         ".jar",
         ".php",
         ".asp",
         ".aspx",
         ".jsp",
         ".pl",
         ".py",
         ".rb",
         ".sh",
      ];

      const fileExtension = filename
         .toLowerCase()
         .substring(filename.lastIndexOf("."));
      if (dangerousExtensions.includes(fileExtension)) {
         errors.push("File type not allowed for security reasons");
      }

      // Validate content type
      const allowedTypes = [
         "image/jpeg",
         "image/jpg",
         "image/png",
         "image/webp",
         "image/gif",
      ];

      if (!allowedTypes.includes(contentType)) {
         errors.push(
            "Invalid file type. Only JPEG, PNG, WebP, and GIF images are allowed"
         );
      }

      // Validate file size (50MB limit)
      const maxSize = 50 * 1024 * 1024; // 50MB
      if (fileSize > maxSize) {
         errors.push("File size exceeds maximum limit of 50MB");
      }

      if (fileSize <= 0) {
         errors.push("File size must be greater than 0");
      }

      return {
         isValid: errors.length === 0,
         errors,
      };
   }

   /**
    * Validate MongoDB ObjectId
    */
   static validateObjectId(id: string): boolean {
      return /^[0-9a-fA-F]{24}$/.test(id);
   }

   /**
    * Validate array of ObjectIds
    */
   static validateObjectIds(ids: string[]): {
      isValid: boolean;
      errors: string[];
   } {
      const errors: string[] = [];

      if (!Array.isArray(ids)) {
         errors.push("IDs must be an array");
         return { isValid: false, errors };
      }

      if (ids.length === 0) {
         errors.push("At least one ID is required");
         return { isValid: false, errors };
      }

      if (ids.length > 100) {
         errors.push("Too many IDs (maximum 100 allowed)");
      }

      for (const id of ids) {
         if (!this.validateObjectId(id)) {
            errors.push(`Invalid ID format: ${id}`);
         }
      }

      return {
         isValid: errors.length === 0,
         errors,
      };
   }
}

/**
 * Portfolio-specific authentication utilities
 */
export class PortfolioAuth {
   /**
    * Check if user has permission to access portfolio management
    */
   static async checkPortfolioPermission(): Promise<AdminSession> {
      const session = await getSession();

      if (!session) {
         throw new Error(AUTH_ERRORS.AUTHENTICATION_REQUIRED);
      }

      // In this implementation, any authenticated admin user has portfolio access
      // You could extend this to check for specific roles or permissions
      return session;
   }

   /**
    * Check if user has permission for specific portfolio action
    */
   static async checkPortfolioActionPermission(
      action: string,
      resourceId?: string
   ): Promise<AdminSession> {
      const session = await this.checkPortfolioPermission();

      // Log the access attempt for audit purposes
      console.log(`Portfolio action attempted: ${action}`, {
         userId: session.id,
         userEmail: session.email,
         action,
         resourceId,
         timestamp: new Date().toISOString(),
      });

      return session;
   }

   /**
    * Validate session and check for suspicious activity
    */
   static async validateSessionSecurity(
      session: AdminSession
   ): Promise<boolean> {
      // Basic session validation
      if (!session.id || !session.email) {
         throw new Error("Invalid session data");
      }

      // Additional security checks could be added here:
      // - Check for concurrent sessions
      // - Validate IP address consistency
      // - Check for unusual activity patterns
      // - Session expiration would be handled by JWT verification in middleware

      return true;
   }
}

// Cleanup rate limiter every 5 minutes
if (typeof setInterval !== "undefined") {
   setInterval(() => {
      RateLimiter.cleanup();
   }, 5 * 60 * 1000);
}
