"use client";

import BulkAddToAlbumDialog from "@/components/admin/album/bulk-add-to-album-dialog";
import MoveToAlbumDialog from "@/components/admin/album/move-to-album-dialog";
import AddToCollectionDialog from "@/components/admin/collection/add-to-collection-dialog";
import BulkAddToCollectionDialog from "@/components/admin/collection/bulk-add-to-collection-dialog";
import BulkDeleteImagesDialog from "@/components/ui/bulk-delete-images-dialog";
import { Button } from "@/components/ui/button";
import {
   Card,
   CardContent,
   CardDescription,
   CardHeader,
   CardTitle,
} from "@/components/ui/card";
import DeleteImageDialog from "@/components/ui/delete-image-dialog";
import FloatingActionBar from "@/components/ui/floating-action-bar";
import MasonryGallery from "@/components/ui/masonry-gallery";
import { Skeleton } from "@/components/ui/skeleton";
import UploadImagesDialog from "@/components/ui/upload-images-dialog";
import {
   useBulkDeleteImages,
   useBulkMoveImagesToAlbum,
   useBulkUpdateImageCollections,
   useDeleteImageCompletely,
   useInfiniteUngroupedImages,
   useMoveImageToAlbum,
   useUpdateImageCollections,
} from "@/lib/hooks/use-images";
import { ImageIcon, Upload } from "lucide-react";
import { useMemo, useState } from "react";

export default function UngroupedPage() {
   const {
      data: imagesData,
      isLoading,
      error,
      hasNextPage,
      isFetchingNextPage,
      fetchNextPage,
   } = useInfiniteUngroupedImages({ limit: 20 });
   const [selectedImages, setSelectedImages] = useState<Set<string>>(new Set());

   // Individual image dialogs
   const [moveToAlbumDialog, setMoveToAlbumDialog] = useState<{
      open: boolean;
      imageId: string;
   }>({ open: false, imageId: "" });

   const [addToCollectionDialog, setAddToCollectionDialog] = useState<{
      open: boolean;
      imageId: string;
      currentCollectionIds: string[];
   }>({ open: false, imageId: "", currentCollectionIds: [] });

   const [deleteDialog, setDeleteDialog] = useState<{
      open: boolean;
      imageId: string;
      imageName: string;
   }>({ open: false, imageId: "", imageName: "" });

   // Bulk action dialogs
   const [bulkAddToAlbumDialog, setBulkAddToAlbumDialog] = useState<{
      open: boolean;
   }>({ open: false });

   const [bulkAddToCollectionDialog, setBulkAddToCollectionDialog] = useState<{
      open: boolean;
   }>({ open: false });

   const [bulkDeleteDialog, setBulkDeleteDialog] = useState<{
      open: boolean;
   }>({ open: false });

   const [uploadDialog, setUploadDialog] = useState<{
      open: boolean;
   }>({ open: false });

   // Mutations
   const deleteImageMutation = useDeleteImageCompletely();
   const moveImageMutation = useMoveImageToAlbum();
   const updateCollectionsMutation = useUpdateImageCollections();
   const bulkMoveImagesMutation = useBulkMoveImagesToAlbum();
   const bulkUpdateCollectionsMutation = useBulkUpdateImageCollections();
   const bulkDeleteImagesMutation = useBulkDeleteImages();

   // Flatten all images from all pages
   const images = useMemo(() => {
      if (!imagesData?.pages) return [];
      return imagesData.pages.flatMap((page) => page.data);
   }, [imagesData?.pages]);

   const totalImages = useMemo(() => {
      if (!imagesData?.pages || imagesData.pages.length === 0) return 0;
      return imagesData.pages[0].pagination.total;
   }, [imagesData?.pages]);

   if (error) {
      return (
         <div className="p-8">
            <div className="text-center text-destructive">
               <p>Failed to load images. Please try again.</p>
            </div>
         </div>
      );
   }

   const hasImages = images.length > 0;

   const handleImageSelect = (imageId: string, selected: boolean) => {
      const newSelected = new Set(selectedImages);
      if (selected) {
         newSelected.add(imageId);
      } else {
         newSelected.delete(imageId);
      }
      setSelectedImages(newSelected);
   };

   const handleClearSelection = () => {
      setSelectedImages(new Set());
   };

   const handleBulkAddToAlbum = () => {
      setBulkAddToAlbumDialog({ open: true });
   };

   const handleBulkAddToCollection = () => {
      setBulkAddToCollectionDialog({ open: true });
   };

   const handleBulkDelete = () => {
      setBulkDeleteDialog({ open: true });
   };

   // Bulk action handlers
   const handleBulkMoveToAlbum = async (
      imageIds: string[],
      albumId: string
   ) => {
      await bulkMoveImagesMutation.mutateAsync({ imageIds, albumId });
      setSelectedImages(new Set());
   };

   const handleBulkAddToCollections = async (
      imageIds: string[],
      collectionIds: string[]
   ) => {
      await bulkUpdateCollectionsMutation.mutateAsync({
         imageIds,
         collectionIds,
      });
      setSelectedImages(new Set());
   };

   const handleBulkDeleteImages = async (): Promise<void> => {
      const imageIds = Array.from(selectedImages);
      await bulkDeleteImagesMutation.mutateAsync(imageIds);
      setSelectedImages(new Set());
   };

   const handleImageAction = (imageId: string, action: string) => {
      const image = images.find((img) => img._id === imageId);
      if (!image) return;

      switch (action) {
         case "move-to-album":
            setMoveToAlbumDialog({
               open: true,
               imageId,
            });
            break;
         case "add-to-collection":
            setAddToCollectionDialog({
               open: true,
               imageId,
               currentCollectionIds: image.collectionIds || [],
            });
            break;
         case "delete":
            setDeleteDialog({
               open: true,
               imageId,
               imageName: image.name,
            });
            break;
         default:
            console.log("Unknown action:", action);
      }
   };

   // Handle move to album
   const handleMoveToAlbum = async (
      imageId: string,
      targetAlbumId: string
   ): Promise<void> => {
      await moveImageMutation.mutateAsync({ imageId, albumId: targetAlbumId });
   };

   // Handle add to collections
   const handleAddToCollections = async (
      imageId: string,
      collectionIds: string[]
   ): Promise<void> => {
      await updateCollectionsMutation.mutateAsync({ imageId, collectionIds });
   };

   // Handle delete image
   const handleDeleteImage = async (): Promise<void> => {
      if (deleteDialog.imageId) {
         await deleteImageMutation.mutateAsync(deleteDialog.imageId);
      }
   };

   const handleUploadComplete = () => {
      // The useInfiniteUngroupedImages hook will automatically refetch
      // when the upload mutation invalidates the queries
      console.log("Upload completed, images should refresh automatically");
   };

   const handleSelectAll = () => {
      if (selectedImages.size === images.length) {
         setSelectedImages(new Set());
      } else {
         setSelectedImages(new Set(images.map((img) => img._id!)));
      }
   };

   return (
      <div className="p-6 sm:p-8 space-y-8">
         {/* Header */}
         <div className="flex flex-col sm:flex-row sm:items-center justify-between">
            <div className="flex items-center space-x-4">
               <div>
                  <h1 className="text-2xl sm:text-3xl font-bold text-foreground">
                     Ungrouped Images
                  </h1>
                  <p className="text-muted-foreground">
                     Images not assigned to any album
                  </p>
               </div>
            </div>

            <div className="flex items-center space-x-3 mt-4 sm:mt-0">
               <Button
                  className="bg-gradient-accent hover:opacity-90"
                  onClick={() => setUploadDialog({ open: true })}
               >
                  <Upload className="w-4 h-4 " />
                  Upload Images
               </Button>
            </div>
         </div>

         {/* Main Content */}
         <Card className="border-border/50">
            <CardHeader>
               <div className="flex items-center justify-between">
                  <div>
                     <CardTitle className="text-foreground">
                        Image Gallery
                     </CardTitle>
                     <CardDescription className="hidden sm:block">
                        {hasImages
                           ? `${images.length} of ${totalImages} ungrouped images`
                           : "No ungrouped images"}
                     </CardDescription>
                  </div>

                  {hasImages && (
                     <Button variant="outline" onClick={handleSelectAll}>
                        {selectedImages.size === images.length
                           ? "Deselect All"
                           : "Select All"}
                     </Button>
                  )}
               </div>
            </CardHeader>
            <CardContent>
               {isLoading ? (
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                     {Array.from({ length: 12 }).map((_, i) => (
                        <Skeleton
                           key={i}
                           className="aspect-square rounded-lg"
                        />
                     ))}
                  </div>
               ) : hasImages ? (
                  <MasonryGallery
                     images={images}
                     selectedImages={selectedImages}
                     onImageSelect={handleImageSelect}
                     onImageAction={handleImageAction}
                     showSetCover={false}
                     showMoveToAlbum={true}
                     showAddToCollection={true}
                     showCheckboxes={true}
                     hasNextPage={hasNextPage}
                     isFetchingNextPage={isFetchingNextPage}
                     fetchNextPage={fetchNextPage}
                     isLoading={isLoading}
                  />
               ) : (
                  <div className="flex flex-col items-center justify-center h-64 text-muted-foreground">
                     <ImageIcon className="w-16 h-16 mb-4 opacity-50" />
                     <h3 className="text-lg font-medium mb-2">
                        No ungrouped images
                     </h3>
                     <p className="text-sm mb-4 text-center">
                        All your images are organized in albums, or upload new
                        images
                     </p>
                  </div>
               )}
            </CardContent>
         </Card>

         {/* Floating Action Bar */}
         <FloatingActionBar
            selectedCount={selectedImages.size}
            onClearSelection={handleClearSelection}
            onAddToAlbum={handleBulkAddToAlbum}
            onAddToCollection={handleBulkAddToCollection}
            onDelete={handleBulkDelete}
         />

         {/* Dialogs */}
         <UploadImagesDialog
            open={uploadDialog.open}
            onOpenChange={(open) => setUploadDialog({ open })}
            onUploadComplete={handleUploadComplete}
            title="Upload Images"
            subtitle="Add new images to your gallery without assigning them to albums or collections"
         />

         <MoveToAlbumDialog
            open={moveToAlbumDialog.open}
            onOpenChange={(open) => setMoveToAlbumDialog({ open, imageId: "" })}
            imageId={moveToAlbumDialog.imageId}
            currentAlbumId={null}
            onMove={handleMoveToAlbum}
         />

         <AddToCollectionDialog
            open={addToCollectionDialog.open}
            onOpenChange={(open) =>
               setAddToCollectionDialog({
                  open,
                  imageId: "",
                  currentCollectionIds: [],
               })
            }
            imageId={addToCollectionDialog.imageId}
            currentCollectionIds={addToCollectionDialog.currentCollectionIds}
            onAddToCollections={handleAddToCollections}
         />

         <DeleteImageDialog
            open={deleteDialog.open}
            onOpenChange={(open) =>
               setDeleteDialog({ open, imageId: "", imageName: "" })
            }
            imageName={deleteDialog.imageName}
            onConfirm={handleDeleteImage}
            isDeleting={deleteImageMutation.isPending}
         />

         {/* Bulk Action Dialogs */}
         <BulkAddToAlbumDialog
            open={bulkAddToAlbumDialog.open}
            onOpenChange={(open) => setBulkAddToAlbumDialog({ open })}
            imageIds={Array.from(selectedImages)}
            onMove={handleBulkMoveToAlbum}
         />

         <BulkAddToCollectionDialog
            open={bulkAddToCollectionDialog.open}
            onOpenChange={(open) => setBulkAddToCollectionDialog({ open })}
            imageIds={Array.from(selectedImages)}
            onAddToCollections={handleBulkAddToCollections}
         />

         <BulkDeleteImagesDialog
            open={bulkDeleteDialog.open}
            onOpenChange={(open) => setBulkDeleteDialog({ open })}
            imageIds={Array.from(selectedImages)}
            onConfirm={handleBulkDeleteImages}
            isDeleting={bulkDeleteImagesMutation.isPending}
         />
      </div>
   );
}
