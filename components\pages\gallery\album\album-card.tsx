"use client";

import { BorderTrail } from "@/components/ui/border-trail";
import { Tilt } from "@/components/ui/tilt";
import { AlbumWithStats } from "@/lib/models";
import { cn } from "@/lib/utils";
import {
   CalendarDaysIcon,
   LockClosedIcon,
   PhotoIcon,
} from "@heroicons/react/24/solid";
import { motion } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";

interface AlbumCardProps {
   album: AlbumWithStats;
   className?: string;
   variant?: "default" | "compact" | "featured";
   showStats?: boolean;
   showDescription?: boolean;
}

export default function AlbumCard({
   album,
   className,
   variant = "default",
   showStats = true,
}: AlbumCardProps) {
   const [isHovered, setIsHovered] = useState(false);
   const [imageLoaded, setImageLoaded] = useState(false);

   const formatDate = (date: Date | string) => {
      return new Date(date).toLocaleDateString("en-US", {
         year: "numeric",
         month: "short",
         day: "numeric",
      });
   };

   const cardVariants = {
      // default: "aspect-[4/3]",
      default: "aspect-[16/10]",
      compact: "aspect-square",
      featured: "aspect-[16/9]",
   };

   return (
      <Link href={`/gallery/albums/${album._id}`} className="block">
         <Tilt rotationFactor={8} isRevese>
            <motion.div
               initial={{ opacity: 0, y: 20 }}
               animate={{ opacity: 1, y: 0 }}
               transition={{ duration: 0.5 }}
               onHoverStart={() => setIsHovered(true)}
               onHoverEnd={() => setIsHovered(false)}
               className={cn(
                  "group relative overflow-hidden rounded-xl bg-card border border-border/30 cursor-pointer",
                  className
               )}
            >
               <BorderTrail
                  className="opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  style={{
                     boxShadow:
                        "0px 0px 60px 30px rgb(255 255 255 / 50%), 0 0 100px 60px rgb(0 0 0 / 50%), 0 0 140px 90px rgb(0 0 0 / 50%)",
                  }}
                  size={100}
               />
               {/* Image Container */}
               <div
                  className={cn(
                     "relative overflow-hidden",
                     cardVariants[variant]
                  )}
               >
                  {/* Cover Image */}
                  {album.coverImageUrl ? (
                     <Image
                        src={album.coverImageUrl}
                        alt={album.name}
                        fill
                        className={cn(
                           "object-cover transition-all duration-700",
                           // "group-hover:scale-105",
                           imageLoaded ? "opacity-100" : "opacity-0"
                        )}
                        onLoad={() => setImageLoaded(true)}
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                     />
                  ) : (
                     <div className="w-full h-full bg-gradient-to-br from-primary/20 to-accent/20 flex items-center justify-center">
                        <PhotoIcon className="size-16 text-muted-foreground/50" />
                     </div>
                  )}
                  {/* Loading Skeleton */}
                  {!imageLoaded && album.coverImageUrl && (
                     <div className="absolute inset-0 bg-muted animate-pulse" />
                  )}
                  {/* Overlay Gradient */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300" />
                  {/* Password Protection Badge */}
                  {album.hasPassword && (
                     <div className="absolute top-3 right-3">
                        <LockClosedIcon className="size-5 text-astral-black mr-1" />
                     </div>
                  )}
               </div>
               {/* Content */}
               <div className="p-5 pt-4 bg-astral-grey/20">
                  {/* Title */}
                  <h3 className="font-bold text-foreground mb-2 line-clamp-1">
                     {album.name}
                  </h3>
                  {/* Description */}
                  {/* {showDescription && album.description ? (
                     <p className="text-muted-foreground text-sm line-clamp-1 mb-2">
                        {album.description}
                     </p>
                  ) : (
                     <p className="text-muted-foreground text-sm line-clamp-1 mb-2">
                        No description available
                     </p>
                  )} */}
                  {/* Metadata */}
                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                     <div className="flex items-center gap-1 font-semibold">
                        <CalendarDaysIcon className="size-4" />
                        <span>{formatDate(album.createdAt)}</span>
                     </div>
                     {showStats && (
                        <div className="flex items-center gap-4">
                           <span className="flex items-center gap-1 font-semibold">
                              <PhotoIcon className="size-4" />
                              {album.imageCount} photos
                           </span>
                        </div>
                     )}
                  </div>
                  {/* Progress Bar (if applicable) */}
                  {variant === "featured" && (
                     <div className="mt-4">
                        <div className="w-full bg-muted rounded-full h-1">
                           <motion.div
                              initial={{ width: 0 }}
                              animate={{ width: isHovered ? "100%" : "0%" }}
                              transition={{ duration: 0.8, ease: "easeInOut" }}
                              className="h-1 bg-gradient-accent rounded-full"
                           />
                        </div>
                     </div>
                  )}
               </div>
               {/* Decorative Elements */}
               <div className="absolute -top-20 -right-20 w-40 h-40 bg-primary/5 rounded-full blur-3xl group-hover:bg-primary/10 transition-colors duration-500" />
               <div className="absolute -bottom-10 -left-10 w-20 h-20 bg-accent/5 rounded-full blur-2xl group-hover:bg-accent/10 transition-colors duration-500" />
            </motion.div>
         </Tilt>
      </Link>
   );
}
