"use server";

import {
   CreatePortfolioImageInput,
   CreatePortfolioServiceInput,
   DEFAULT_PAGINATION,
   PaginatedResponse,
   PaginationOptions,
   PortfolioImage,
   PortfolioService,
   PortfolioServiceWithStats,
   UpdatePortfolioImageInput,
   UpdatePortfolioServiceInput,
   createPaginationMetadata,
   createPortfolioImageMetadata,
   createPortfolioServiceMetadata,
   validatePortfolioImageInput,
   validatePortfolioServiceInput,
} from "@/lib/models";
import { getCollection } from "@/lib/mongodb/db";
import { ObjectId } from "mongodb";

/**
 * Get all portfolio services with pagination and image counts
 */
export async function getPortfolioServices(
   options: PaginationOptions = {}
): Promise<PaginatedResponse<PortfolioServiceWithStats>> {
   try {
      const {
         page = DEFAULT_PAGINATION.page,
         limit = DEFAULT_PAGINATION.limit,
         sortBy = DEFAULT_PAGINATION.sortBy,
         sortOrder = DEFAULT_PAGINATION.sortOrder,
      } = options;

      const serviceCollection = await getCollection<PortfolioService>(
         "portfolio-services"
      );

      // Build sort query
      const sort: Record<string, 1 | -1> = {};
      sort[sortBy] = sortOrder === "asc" ? 1 : -1;

      // Calculate skip value
      const skip = (page - 1) * limit;

      // Get services with image counts
      const pipeline = [
         {
            $lookup: {
               from: "portfolio-images",
               let: { serviceId: { $toString: "$_id" } },
               pipeline: [
                  {
                     $match: {
                        $expr: {
                           $eq: ["$serviceId", "$$serviceId"],
                        },
                     },
                  },
               ],
               as: "images",
            },
         },
         {
            $addFields: {
               imageCount: { $size: "$images" },
            },
         },
         {
            $project: {
               images: 0, // Remove the images array, keep only the count
            },
         },
         { $sort: sort },
         { $skip: skip },
         { $limit: limit },
      ];

      const [services, total] = await Promise.all([
         serviceCollection.aggregate(pipeline).toArray(),
         serviceCollection.countDocuments(),
      ]);

      const pagination = createPaginationMetadata(page, limit, total);

      // Convert ObjectIds to strings for client component serialization
      const serializedServices = services.map((service) => ({
         ...service,
         _id: service._id?.toString(),
      }));

      return {
         data: serializedServices as PortfolioServiceWithStats[],
         pagination,
      };
   } catch (error) {
      console.error("Error fetching portfolio services:", error);
      throw new Error("Failed to fetch portfolio services");
   }
}

/**
 * Get portfolio service by ID with image count
 */
export async function getPortfolioServiceById(
   id: string
): Promise<PortfolioServiceWithStats | null> {
   try {
      const serviceCollection = await getCollection<PortfolioService>(
         "portfolio-services"
      );

      const pipeline = [
         { $match: { _id: new ObjectId(id) } },
         {
            $lookup: {
               from: "portfolio-images",
               let: { serviceId: { $toString: "$_id" } },
               pipeline: [
                  {
                     $match: {
                        $expr: {
                           $eq: ["$serviceId", "$$serviceId"],
                        },
                     },
                  },
               ],
               as: "images",
            },
         },
         {
            $addFields: {
               imageCount: { $size: "$images" },
            },
         },
         {
            $project: {
               images: 0,
            },
         },
      ];

      const result = await serviceCollection.aggregate(pipeline).toArray();
      const service = result[0] as PortfolioServiceWithStats;

      if (!service) return null;

      // Convert ObjectId to string for client component serialization
      return {
         ...service,
         _id: service._id?.toString(),
      };
   } catch (error) {
      console.error("Error fetching portfolio service by ID:", error);
      throw new Error("Failed to fetch portfolio service");
   }
}

/**
 * Create a new portfolio service
 */
export async function createPortfolioService(
   input: CreatePortfolioServiceInput
): Promise<PortfolioService> {
   try {
      // Validate input
      const errors = validatePortfolioServiceInput(input);
      if (errors.length > 0) {
         throw new Error(`Validation failed: ${errors.join(", ")}`);
      }

      const collection = await getCollection<PortfolioService>(
         "portfolio-services"
      );
      const serviceData = createPortfolioServiceMetadata(input);

      const result = await collection.insertOne(serviceData);

      if (!result.insertedId) {
         throw new Error("Failed to create portfolio service");
      }

      const createdService = await collection.findOne({
         _id: result.insertedId,
      });

      if (!createdService) {
         throw new Error("Failed to retrieve created portfolio service");
      }

      // Convert ObjectId to string for client component serialization
      return {
         ...createdService,
         _id: createdService._id?.toString(),
      };
   } catch (error) {
      console.error("Error creating portfolio service:", error);
      throw error;
   }
}

/**
 * Update a portfolio service
 */
export async function updatePortfolioService(
   id: string,
   input: UpdatePortfolioServiceInput
): Promise<PortfolioService | null> {
   try {
      const collection = await getCollection<PortfolioService>(
         "portfolio-services"
      );

      const updateData: Record<string, unknown> = {
         ...input,
         updatedAt: new Date(),
      };

      // If name is being updated, regenerate slug
      if (input.name) {
         const { generateSlug } = await import("@/lib/models/portfolio");
         updateData.slug = generateSlug(input.name);
      }

      const result = await collection.findOneAndUpdate(
         { _id: new ObjectId(id) },
         { $set: updateData },
         { returnDocument: "after" }
      );

      if (!result) return null;

      // Convert ObjectId to string for client component serialization
      return {
         ...result,
         _id: result._id?.toString(),
      };
   } catch (error) {
      console.error("Error updating portfolio service:", error);
      throw new Error("Failed to update portfolio service");
   }
}

/**
 * Delete a portfolio service and handle associated images
 */
export async function deletePortfolioService(id: string): Promise<boolean> {
   try {
      const serviceCollection = await getCollection<PortfolioService>(
         "portfolio-services"
      );
      const imageCollection = await getCollection<PortfolioImage>(
         "portfolio-images"
      );

      // Delete the service
      const result = await serviceCollection.deleteOne({
         _id: new ObjectId(id),
      });

      if (result.deletedCount > 0) {
         // Remove all images associated with this service
         await imageCollection.deleteMany({ serviceId: id });
         return true;
      }

      return false;
   } catch (error) {
      console.error("Error deleting portfolio service:", error);
      throw new Error("Failed to delete portfolio service");
   }
}

/**
 * Get active portfolio services only (for public display)
 */
export async function getActivePortfolioServices(
   options: PaginationOptions = {}
): Promise<PaginatedResponse<PortfolioServiceWithStats>> {
   try {
      const {
         page = DEFAULT_PAGINATION.page,
         limit = DEFAULT_PAGINATION.limit,
         sortBy = "displayOrder",
         sortOrder = "asc", // Always sort by displayOrder in ascending order for proper category ordering
      } = options;

      const serviceCollection = await getCollection<PortfolioService>(
         "portfolio-services"
      );

      // Build sort query
      const sort: Record<string, 1 | -1> = {};
      sort[sortBy] = sortOrder === "asc" ? 1 : -1;

      // Calculate skip value
      const skip = (page - 1) * limit;

      // Get active services with image counts
      const pipeline = [
         { $match: { isActive: true } },
         {
            $lookup: {
               from: "portfolio-images",
               let: { serviceId: { $toString: "$_id" } },
               pipeline: [
                  {
                     $match: {
                        $expr: {
                           $eq: ["$serviceId", "$$serviceId"],
                        },
                     },
                  },
               ],
               as: "images",
            },
         },
         {
            $addFields: {
               imageCount: { $size: "$images" },
            },
         },
         {
            $project: {
               images: 0,
            },
         },
         { $sort: sort },
         { $skip: skip },
         { $limit: limit },
      ];

      const [services, total] = await Promise.all([
         serviceCollection.aggregate(pipeline).toArray(),
         serviceCollection.countDocuments({ isActive: true }),
      ]);

      const pagination = createPaginationMetadata(page, limit, total);

      // Convert ObjectIds to strings for client component serialization
      const serializedServices = services.map((service) => ({
         ...service,
         _id: service._id?.toString(),
      }));

      return {
         data: serializedServices as PortfolioServiceWithStats[],
         pagination,
      };
   } catch (error) {
      console.error("Error fetching active portfolio services:", error);
      throw new Error("Failed to fetch active portfolio services");
   }
}

/**
 * Get all active portfolio services without pagination (for public display)
 */
export async function getAllActivePortfolioServices(): Promise<
   PortfolioServiceWithStats[]
> {
   try {
      const serviceCollection = await getCollection<PortfolioService>(
         "portfolio-services"
      );

      // Build sort query - always sort by displayOrder ascending for proper category ordering
      const sort: Record<string, 1 | -1> = { displayOrder: 1 };

      // Get all active services with image counts (no pagination)
      const pipeline = [
         { $match: { isActive: true } },
         {
            $lookup: {
               from: "portfolio-images",
               let: { serviceId: { $toString: "$_id" } },
               pipeline: [
                  {
                     $match: {
                        $expr: {
                           $eq: ["$serviceId", "$$serviceId"],
                        },
                     },
                  },
               ],
               as: "images",
            },
         },
         {
            $addFields: {
               imageCount: { $size: "$images" },
            },
         },
         {
            $project: {
               images: 0,
            },
         },
         { $sort: sort },
      ];

      const services = await serviceCollection.aggregate(pipeline).toArray();

      // Convert ObjectIds to strings for client component serialization
      const serializedServices = services.map((service) => ({
         ...service,
         _id: service._id?.toString(),
      }));

      return serializedServices as PortfolioServiceWithStats[];
   } catch (error) {
      console.error("Error fetching all active portfolio services:", error);
      throw new Error("Failed to fetch all active portfolio services");
   }
}

/**
 * Search portfolio services by name
 */
export async function searchPortfolioServices(
   query: string,
   options: PaginationOptions = {}
): Promise<PaginatedResponse<PortfolioServiceWithStats>> {
   try {
      const {
         page = DEFAULT_PAGINATION.page,
         limit = DEFAULT_PAGINATION.limit,
         sortBy = DEFAULT_PAGINATION.sortBy,
         sortOrder = DEFAULT_PAGINATION.sortOrder,
      } = options;

      const serviceCollection = await getCollection<PortfolioService>(
         "portfolio-services"
      );

      // Build sort query
      const sort: Record<string, 1 | -1> = {};
      sort[sortBy] = sortOrder === "asc" ? 1 : -1;

      // Calculate skip value
      const skip = (page - 1) * limit;

      // Build search filter
      const searchFilter = {
         name: { $regex: query, $options: "i" },
      };

      // Get services with image counts
      const pipeline = [
         { $match: searchFilter },
         {
            $lookup: {
               from: "portfolio-images",
               let: { serviceId: { $toString: "$_id" } },
               pipeline: [
                  {
                     $match: {
                        $expr: {
                           $eq: ["$serviceId", "$$serviceId"],
                        },
                     },
                  },
               ],
               as: "images",
            },
         },
         {
            $addFields: {
               imageCount: { $size: "$images" },
            },
         },
         {
            $project: {
               images: 0,
            },
         },
         { $sort: sort },
         { $skip: skip },
         { $limit: limit },
      ];

      const [services, total] = await Promise.all([
         serviceCollection.aggregate(pipeline).toArray(),
         serviceCollection.countDocuments(searchFilter),
      ]);

      const pagination = createPaginationMetadata(page, limit, total);

      // Convert ObjectIds to strings for client component serialization
      const serializedServices = services.map((service) => ({
         ...service,
         _id: service._id?.toString(),
      }));

      return {
         data: serializedServices as PortfolioServiceWithStats[],
         pagination,
      };
   } catch (error) {
      console.error("Error searching portfolio services:", error);
      throw new Error("Failed to search portfolio services");
   }
}

// ===== Portfolio Image Management Functions =====

/**
 * Get portfolio images with pagination and filtering
 */
export async function getPortfolioImages(
   options: PaginationOptions & {
      serviceId?: string;
   } = {}
): Promise<PaginatedResponse<PortfolioImage>> {
   try {
      const {
         page = DEFAULT_PAGINATION.page,
         limit = 1000,
         // limit = DEFAULT_PAGINATION.limit,
         sortBy = "displayOrder", // Default to displayOrder for portfolio images
         sortOrder = DEFAULT_PAGINATION.sortOrder,
         serviceId,
      } = options;

      const collection = await getCollection<PortfolioImage>(
         "portfolio-images"
      );

      // Build filter query
      const filter: Record<string, unknown> = {};
      if (serviceId) {
         filter.serviceId = serviceId;
      }

      // Build sort query - always include displayOrder as secondary sort
      const sort: Record<string, 1 | -1> = {};
      sort[sortBy] = sortOrder === "asc" ? 1 : -1;
      if (sortBy !== "displayOrder") {
         sort.displayOrder = 1; // Secondary sort by display order
      }

      // Calculate skip value
      const skip = (page - 1) * limit;

      // Execute queries
      const [images, total] = await Promise.all([
         collection.find(filter).sort(sort).skip(skip).limit(limit).toArray(),
         collection.countDocuments(filter),
      ]);

      // Convert ObjectIds to strings for client serialization
      const serializedImages = images.map((image) => ({
         ...image,
         _id: image._id?.toString(),
      }));

      const pagination = createPaginationMetadata(page, limit, total);

      return {
         data: serializedImages,
         pagination,
      };
   } catch (error) {
      console.error("Error fetching portfolio images:", error);
      throw new Error("Failed to fetch portfolio images");
   }
}

/**
 * Get all portfolio images without pagination (for public display)
 */
export async function getAllPortfolioImages(
   options: {
      serviceId?: string;
      sortBy?: string;
      sortOrder?: "asc" | "desc";
   } = {}
): Promise<PortfolioImage[]> {
   try {
      const {
         sortBy = "displayOrder", // Default to displayOrder for portfolio images
         sortOrder = "asc", // Default to ascending for proper display order
         serviceId,
      } = options;

      const collection = await getCollection<PortfolioImage>(
         "portfolio-images"
      );

      // Build filter query
      const filter: Record<string, unknown> = {};
      if (serviceId) {
         filter.serviceId = serviceId;
      }

      // Build sort query - always include displayOrder as secondary sort
      const sort: Record<string, 1 | -1> = {};
      sort[sortBy] = sortOrder === "asc" ? 1 : -1;
      if (sortBy !== "displayOrder") {
         sort.displayOrder = 1; // Secondary sort by display order
      }

      // Execute query without pagination
      const images = await collection.find(filter).sort(sort).toArray();

      // Convert ObjectIds to strings for client serialization
      const serializedImages = images.map((image) => ({
         ...image,
         _id: image._id?.toString(),
      }));

      return serializedImages;
   } catch (error) {
      console.error("Error fetching all portfolio images:", error);
      throw new Error("Failed to fetch all portfolio images");
   }
}

/**
 * Get portfolio images by service ID
 */
export async function getPortfolioImagesByServiceId(
   serviceId: string,
   options: PaginationOptions = {}
): Promise<PaginatedResponse<PortfolioImage>> {
   return getPortfolioImages({ ...options, serviceId });
}

/**
 * Get portfolio image by ID
 */
export async function getPortfolioImageById(
   id: string
): Promise<PortfolioImage | null> {
   try {
      const collection = await getCollection<PortfolioImage>(
         "portfolio-images"
      );
      const image = await collection.findOne({ _id: new ObjectId(id) });

      if (!image) return null;

      // Convert ObjectId to string for client serialization
      return {
         ...image,
         _id: image._id?.toString(),
      };
   } catch (error) {
      console.error("Error fetching portfolio image by ID:", error);
      throw new Error("Failed to fetch portfolio image");
   }
}

/**
 * Create a new portfolio image
 */
export async function createPortfolioImage(
   input: CreatePortfolioImageInput
): Promise<PortfolioImage> {
   try {
      // Validate input
      const errors = validatePortfolioImageInput(input);
      if (errors.length > 0) {
         throw new Error(`Validation failed: ${errors.join(", ")}`);
      }

      // Get service name for alt text generation
      const service = await getPortfolioServiceById(input.serviceId);
      if (!service) {
         throw new Error("Portfolio service not found");
      }

      // Get next display order if not provided
      const displayOrder =
         input.displayOrder ?? (await getNextDisplayOrder(input.serviceId));

      const collection = await getCollection<PortfolioImage>(
         "portfolio-images"
      );
      const imageData = createPortfolioImageMetadata(
         { ...input, displayOrder },
         service.name
      );

      const result = await collection.insertOne(imageData);

      if (!result.insertedId) {
         throw new Error("Failed to create portfolio image");
      }

      const createdImage = await collection.findOne({ _id: result.insertedId });

      if (!createdImage) {
         throw new Error("Failed to retrieve created portfolio image");
      }

      // Convert ObjectId to string for client serialization
      return {
         ...createdImage,
         _id: createdImage._id?.toString(),
      };
   } catch (error) {
      console.error("Error creating portfolio image:", error);
      throw error;
   }
}

/**
 * Update a portfolio image
 */
export async function updatePortfolioImage(
   id: string,
   input: UpdatePortfolioImageInput
): Promise<PortfolioImage | null> {
   try {
      const collection = await getCollection<PortfolioImage>(
         "portfolio-images"
      );

      const updateData: Record<string, unknown> = {
         ...input,
      };

      // If serviceId is being updated, regenerate alt text
      if (input.serviceId) {
         const service = await getPortfolioServiceById(input.serviceId);
         if (!service) {
            throw new Error("Portfolio service not found");
         }

         // Get current image to preserve name for alt text generation
         const currentImage = await collection.findOne({
            _id: new ObjectId(id),
         });
         if (currentImage) {
            const { generatePortfolioAltText } = await import(
               "@/lib/models/portfolio"
            );
            updateData.altText = generatePortfolioAltText(
               service.name,
               currentImage.name
            );
         }
      }

      const result = await collection.findOneAndUpdate(
         { _id: new ObjectId(id) },
         { $set: updateData },
         { returnDocument: "after" }
      );

      if (!result) return null;

      // Convert ObjectId to string for client serialization
      return {
         ...result,
         _id: result._id?.toString(),
      };
   } catch (error) {
      console.error("Error updating portfolio image:", error);
      throw new Error("Failed to update portfolio image");
   }
}

/**
 * Delete a portfolio image
 */
export async function deletePortfolioImage(id: string): Promise<boolean> {
   try {
      const collection = await getCollection<PortfolioImage>(
         "portfolio-images"
      );
      const result = await collection.deleteOne({ _id: new ObjectId(id) });
      return result.deletedCount > 0;
   } catch (error) {
      console.error("Error deleting portfolio image:", error);
      throw new Error("Failed to delete portfolio image");
   }
}

/**
 * Move portfolio image to a different service
 */
export async function movePortfolioImageToService(
   imageId: string,
   serviceId: string
): Promise<PortfolioImage | null> {
   try {
      // Get service name for alt text regeneration
      const service = await getPortfolioServiceById(serviceId);
      if (!service) {
         throw new Error("Portfolio service not found");
      }

      const collection = await getCollection<PortfolioImage>(
         "portfolio-images"
      );

      // Get current image to preserve name for alt text generation
      const currentImage = await collection.findOne({
         _id: new ObjectId(imageId),
      });
      if (!currentImage) {
         return null;
      }

      const { generatePortfolioAltText } = await import(
         "@/lib/models/portfolio"
      );
      const newAltText = generatePortfolioAltText(
         service.name,
         currentImage.name
      );

      const result = await collection.findOneAndUpdate(
         { _id: new ObjectId(imageId) },
         {
            $set: {
               serviceId: serviceId,
               altText: newAltText,
            },
         },
         { returnDocument: "after" }
      );

      if (!result) return null;

      // Convert ObjectId to string for client serialization
      return {
         ...result,
         _id: result._id?.toString(),
      };
   } catch (error) {
      console.error("Error moving portfolio image to service:", error);
      throw new Error("Failed to move portfolio image to service");
   }
}

/**
 * Bulk move portfolio images to a service
 */
export async function bulkMovePortfolioImagesToService(
   imageIds: string[],
   serviceId: string
): Promise<boolean> {
   try {
      // Get service name for alt text regeneration
      const service = await getPortfolioServiceById(serviceId);
      if (!service) {
         throw new Error("Portfolio service not found");
      }

      const collection = await getCollection<PortfolioImage>(
         "portfolio-images"
      );
      const objectIds = imageIds.map((id) => new ObjectId(id));

      // Get current images to preserve names for alt text generation
      const currentImages = await collection
         .find({ _id: { $in: objectIds } })
         .toArray();

      const { generatePortfolioAltText } = await import(
         "@/lib/models/portfolio"
      );

      // Update each image with new service and regenerated alt text
      const updatePromises = currentImages.map((image) => {
         const newAltText = generatePortfolioAltText(service.name, image.name);
         return collection.updateOne(
            { _id: image._id },
            {
               $set: {
                  serviceId: serviceId,
                  altText: newAltText,
               },
            }
         );
      });

      const results = await Promise.all(updatePromises);
      return results.some((result) => result.modifiedCount > 0);
   } catch (error) {
      console.error("Error bulk moving portfolio images to service:", error);
      throw new Error("Failed to move portfolio images to service");
   }
}

/**
 * Bulk delete portfolio images
 */
export async function bulkDeletePortfolioImages(
   imageIds: string[]
): Promise<boolean> {
   try {
      const collection = await getCollection<PortfolioImage>(
         "portfolio-images"
      );
      const objectIds = imageIds.map((id) => new ObjectId(id));

      const result = await collection.deleteMany({
         _id: { $in: objectIds },
      });

      return result.deletedCount > 0;
   } catch (error) {
      console.error("Error bulk deleting portfolio images:", error);
      throw new Error("Failed to delete portfolio images");
   }
}

/**
 * Get portfolio service statistics
 */
export async function getPortfolioServiceStats(serviceId: string): Promise<{
   imageCount: number;
   totalFileSize: number;
}> {
   try {
      const collection = await getCollection<PortfolioImage>(
         "portfolio-images"
      );

      const pipeline = [
         { $match: { serviceId: serviceId } },
         {
            $group: {
               _id: null,
               imageCount: { $sum: 1 },
               totalFileSize: { $sum: "$fileSize" },
            },
         },
      ];

      const result = await collection.aggregate(pipeline).toArray();
      const stats = result[0];

      return {
         imageCount: stats?.imageCount || 0,
         totalFileSize: stats?.totalFileSize || 0,
      };
   } catch (error) {
      console.error("Error fetching portfolio service stats:", error);
      throw new Error("Failed to fetch portfolio service stats");
   }
}

/**
 * Search portfolio images by name
 */
export async function searchPortfolioImages(
   query: string,
   options: PaginationOptions & { serviceId?: string } = {}
): Promise<PaginatedResponse<PortfolioImage>> {
   try {
      const {
         page = DEFAULT_PAGINATION.page,
         limit = DEFAULT_PAGINATION.limit,
         sortBy = DEFAULT_PAGINATION.sortBy,
         sortOrder = DEFAULT_PAGINATION.sortOrder,
         serviceId,
      } = options;

      const collection = await getCollection<PortfolioImage>(
         "portfolio-images"
      );

      // Build search filter
      const filter: Record<string, unknown> = {
         name: { $regex: query, $options: "i" },
      };

      if (serviceId) {
         filter.serviceId = serviceId;
      }

      // Build sort query
      const sort: Record<string, 1 | -1> = {};
      sort[sortBy] = sortOrder === "asc" ? 1 : -1;

      // Calculate skip value
      const skip = (page - 1) * limit;

      // Execute queries
      const [images, total] = await Promise.all([
         collection.find(filter).sort(sort).skip(skip).limit(limit).toArray(),
         collection.countDocuments(filter),
      ]);

      // Convert ObjectIds to strings for client serialization
      const serializedImages = images.map((image) => ({
         ...image,
         _id: image._id?.toString(),
      }));

      const pagination = createPaginationMetadata(page, limit, total);

      return {
         data: serializedImages,
         pagination,
      };
   } catch (error) {
      console.error("Error searching portfolio images:", error);
      throw new Error("Failed to search portfolio images");
   }
}

/**
 * Reorder portfolio images within a service
 */
export async function reorderPortfolioImages(
   serviceId: string,
   imageOrder: Array<{ imageId: string; displayOrder: number }>
): Promise<boolean> {
   try {
      const collection = await getCollection<PortfolioImage>(
         "portfolio-images"
      );

      // Update each image's display order
      const updatePromises = imageOrder.map(({ imageId, displayOrder }) =>
         collection.updateOne(
            { _id: new ObjectId(imageId), serviceId: serviceId },
            { $set: { displayOrder } }
         )
      );

      const results = await Promise.all(updatePromises);
      return results.some((result) => result.modifiedCount > 0);
   } catch (error) {
      console.error("Error reordering portfolio images:", error);
      throw new Error("Failed to reorder portfolio images");
   }
}

/**
 * Reorder portfolio services
 */
export async function reorderPortfolioServices(
   serviceOrder: Array<{ serviceId: string; displayOrder: number }>
): Promise<boolean> {
   try {
      const collection = await getCollection<PortfolioService>(
         "portfolio-services"
      );

      // Update each service's display order
      const updatePromises = serviceOrder.map(({ serviceId, displayOrder }) =>
         collection.updateOne(
            { _id: new ObjectId(serviceId) },
            { $set: { displayOrder, updatedAt: new Date() } }
         )
      );

      const results = await Promise.all(updatePromises);
      return results.some((result) => result.modifiedCount > 0);
   } catch (error) {
      console.error("Error reordering portfolio services:", error);
      throw new Error("Failed to reorder portfolio services");
   }
}

/**
 * Get next display order for a service
 */
export async function getNextDisplayOrder(serviceId: string): Promise<number> {
   try {
      const collection = await getCollection<PortfolioImage>(
         "portfolio-images"
      );

      const result = await collection.findOne(
         { serviceId },
         { sort: { displayOrder: -1 }, projection: { displayOrder: 1 } }
      );

      return (result?.displayOrder ?? -1) + 1;
   } catch (error) {
      console.error("Error getting next display order:", error);
      return 0;
   }
}
