"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { motion } from "framer-motion";
import { ArrowRight, Baby, Camera, Users, Video } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

export default function FeaturedServiceSection2() {
   const services = [
      {
         title: "Wedding Photography",
         description:
            "Capture your special day with stunning, timeless photographs that tell your unique love story. From intimate moments to grand celebrations.",
         image: "/images/wedding-shoots/wedding-shoot-5.JPG",
         icon: Camera,
         href: "/services/wedding",
      },
      {
         title: "Pre-Wedding Shoots",
         description:
            "Beautiful engagement sessions in stunning locations that capture your connection.",
         image: "/images/pre-wedding-shoots/pre-wedding-shoot-1.JPG",
         icon: Users,
         href: "/services/pre-wedding",
      },
      {
         title: "Pregnancy Photography",
         description:
            "Celebrate this magical time with elegant maternity portraits that capture the beauty of pregnancy and the anticipation of new life.",
         image: "/images/pregnancy-shoots/pregnancy-shoot-1.jpg",
         icon: Baby,
         href: "/services/pregnancy",
      },
      {
         title: "Child Dedication",
         description:
            "Capture precious moments of your child's dedication ceremony with heartfelt photography.",
         image: "/images/child-dedication/child-dedication-1.JP<PERSON>",
         icon: Video,
         href: "/services/child-dedication",
      },
   ];

   return (
      <section className="py-20 bg-background relative">
         {/* Animated background pattern */}
         <div className="absolute inset-0 opacity-5">
            <div
               className="absolute inset-0"
               style={{
                  backgroundImage: `radial-gradient(circle at 25% 25%, hsl(0 84% 60% / 0.1) 0%, transparent 50%),
                                radial-gradient(circle at 75% 75%, hsl(0 84% 60% / 0.1) 0%, transparent 50%)`,
               }}
            />
         </div>

         <div className="container max-w-[1400px] mx-auto px-4 relative z-10">
            <motion.div
               className="text-center mb-16"
               initial={{ opacity: 0, y: 30 }}
               whileInView={{ opacity: 1, y: 0 }}
               transition={{ duration: 0.8 }}
               viewport={{ once: true }}
            >
               <h2 className="text-4xl md:text-5xl font-playfair font-bold text-foreground mb-6">
                  Our{" "}
                  <span className="bg-gradient-accent bg-clip-text text-transparent">
                     Signature
                  </span>{" "}
                  Services
               </h2>
               <p className="text-xl text-muted-foreground font-montserrat max-w-3xl mx-auto leading-relaxed">
                  Discover our comprehensive range of photography and
                  videography services, each crafted with passion and precision
               </p>
            </motion.div>

            {/* Masonry Grid */}
            <div className="columns-1 md:columns-4 gap-6 space-y-6">
               {services.map((service, index) => {
                  const IconComponent = service.icon;
                  return (
                     <motion.div
                        key={service.title}
                        initial={{ opacity: 0, y: 50 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{
                           duration: 0.6,
                           delay: index * 0.1,
                           type: "spring",
                           stiffness: 100,
                        }}
                        viewport={{ once: true }}
                        className="break-inside-avoid mb-6"
                     >
                        <Card
                           className={`group overflow-hidden bg-card/80 backdrop-blur-sm border-astral-grey-light hover:border-primary/30 transition-all duration-500 hover:shadow-elegant p-0 h-[500px]`}
                        >
                           <div className="relative h-full">
                              {/* Background Image */}
                              <div className="absolute inset-0">
                                 <Image
                                    src={service.image}
                                    alt={service.title}
                                    fill
                                    className="object-cover transition-transform duration-700 group-hover:scale-110"
                                 />
                                 <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent" />
                              </div>

                              {/* Content */}
                              <CardContent className="relative h-full p-6 flex flex-col justify-end text-white">
                                 {/* Icon and Stats */}
                                 <div className="flex items-center justify-between mb-4">
                                    <div className="w-12 h-12 bg-gradient-accent rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                                       <IconComponent className="h-6 w-6 text-white" />
                                    </div>
                                 </div>

                                 <h3 className="text-lg lg:text-xl font-bold mb-3">
                                    {service.title}
                                 </h3>

                                 <p className="text-gray-200 text-sm leading-relaxed mb-6 line-clamp-3">
                                    {service.description}
                                 </p>

                                 <Button
                                    asChild
                                    size="sm"
                                    className="w-full bg-white/10 backdrop-blur-sm border border-white/20 text-white hover:bg-white hover:text-black transition-all duration-300"
                                 >
                                    <Link
                                       href={service.href}
                                       className="flex items-center justify-center gap-2"
                                    >
                                       Discover More
                                       <ArrowRight className="h-4 w-4 transition-transform group-hover:translate-x-1" />
                                    </Link>
                                 </Button>
                              </CardContent>
                           </div>
                        </Card>
                     </motion.div>
                  );
               })}
            </div>

            {/* Call to Action */}
            <motion.div
               className="text-center mt-16"
               initial={{ opacity: 0, y: 20 }}
               whileInView={{ opacity: 1, y: 0 }}
               transition={{ duration: 0.6, delay: 0.4 }}
               viewport={{ once: true }}
            >
               <Button
                  asChild
                  size="lg"
                  className="shadow-glow py-4 h-auto !px-6"
               >
                  <Link href="/services">
                     View All Services
                     <ArrowRight className="h-5 w-5 ml-2" />
                  </Link>
               </Button>
            </motion.div>
         </div>
      </section>
   );
}
