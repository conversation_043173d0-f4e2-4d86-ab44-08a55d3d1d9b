/**
 * Retry operations utilities for portfolio functionality
 */

import {
   calculateRetryDelay,
   categorizeError,
   DEFAULT_RETRY_CONFIG,
   PortfolioError,
   PortfolioErrorType,
   RetryConfig,
   shouldRetryError,
   sleep,
   UPLOAD_RETRY_CONFIG,
} from "./portfolio-error-handling";

export interface RetryResult<T> {
   success: boolean;
   data?: T;
   error?: PortfolioError;
   attempts: number;
}

/**
 * Generic retry wrapper for any async operation
 */
export async function withRetry<T>(
   operation: () => Promise<T>,
   config: RetryConfig = DEFAULT_RETRY_CONFIG,
   operationName: string = "operation"
): Promise<RetryResult<T>> {
   let lastError: PortfolioError | null = null;
   let attempts = 0;

   while (attempts < config.maxAttempts) {
      attempts++;

      try {
         console.log(
            `Attempting ${operationName} (attempt ${attempts}/${config.maxAttempts})`
         );
         const result = await operation();

         return {
            success: true,
            data: result,
            attempts,
         };
      } catch (error) {
         const portfolioError = categorizeError(error);
         lastError = portfolioError;

         console.warn(
            `${operationName} failed on attempt ${attempts}:`,
            portfolioError.message
         );

         // Check if we should retry this error
         if (!shouldRetryError(portfolioError, attempts, config)) {
            console.error(
               `${operationName} failed permanently:`,
               portfolioError.message
            );
            break;
         }

         // If this is not the last attempt, wait before retrying
         if (attempts < config.maxAttempts) {
            const delay = calculateRetryDelay(attempts, config);
            console.log(`Waiting ${delay}ms before retry...`);
            await sleep(delay);
         }
      }
   }

   return {
      success: false,
      error:
         lastError ||
         categorizeError(
            new Error(`${operationName} failed after ${attempts} attempts`)
         ),
      attempts,
   };
}

/**
 * Retry wrapper specifically for database operations
 */
export async function withDatabaseRetry<T>(
   operation: () => Promise<T>,
   operationName: string = "database operation"
): Promise<RetryResult<T>> {
   return withRetry(operation, DEFAULT_RETRY_CONFIG, operationName);
}

/**
 * Retry wrapper specifically for upload operations
 */
export async function withUploadRetry<T>(
   operation: () => Promise<T>,
   operationName: string = "upload operation"
): Promise<RetryResult<T>> {
   return withRetry(operation, UPLOAD_RETRY_CONFIG, operationName);
}

/**
 * Retry wrapper for network requests with exponential backoff
 */
export async function withNetworkRetry<T>(
   operation: () => Promise<T>,
   operationName: string = "network operation"
): Promise<RetryResult<T>> {
   const networkConfig: RetryConfig = {
      maxAttempts: 3,
      baseDelay: 1000,
      maxDelay: 10000,
      backoffMultiplier: 2,
      retryableErrors: [
         PortfolioErrorType.NETWORK_ERROR,
         PortfolioErrorType.UNKNOWN_ERROR,
      ],
   };

   return withRetry(operation, networkConfig, operationName);
}

/**
 * Batch operation with retry - processes items in batches with retry for each batch
 */
export async function withBatchRetry<T, R>(
   items: T[],
   batchSize: number,
   batchOperation: (batch: T[]) => Promise<R[]>,
   config: RetryConfig = DEFAULT_RETRY_CONFIG,
   operationName: string = "batch operation"
): Promise<{
   success: boolean;
   results: R[];
   errors: PortfolioError[];
   totalAttempts: number;
}> {
   const results: R[] = [];
   const errors: PortfolioError[] = [];
   let totalAttempts = 0;

   // Process items in batches
   for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);
      const batchIndex = Math.floor(i / batchSize) + 1;
      const totalBatches = Math.ceil(items.length / batchSize);

      console.log(
         `Processing batch ${batchIndex}/${totalBatches} (${batch.length} items)`
      );

      const batchResult = await withRetry(
         () => batchOperation(batch),
         config,
         `${operationName} batch ${batchIndex}`
      );

      totalAttempts += batchResult.attempts;

      if (batchResult.success && batchResult.data) {
         results.push(...batchResult.data);
      } else if (batchResult.error) {
         errors.push(batchResult.error);
         console.error(
            `Batch ${batchIndex} failed permanently:`,
            batchResult.error.message
         );
      }
   }

   return {
      success: errors.length === 0,
      results,
      errors,
      totalAttempts,
   };
}

/**
 * Circuit breaker pattern for operations that might fail frequently
 */
export class CircuitBreaker {
   private failures = 0;
   private lastFailureTime = 0;
   private state: "CLOSED" | "OPEN" | "HALF_OPEN" = "CLOSED";

   constructor(
      private failureThreshold: number = 5,
      private recoveryTimeout: number = 60000, // 1 minute
      private operationName: string = "operation"
   ) {}

   async execute<T>(operation: () => Promise<T>): Promise<T> {
      if (this.state === "OPEN") {
         if (Date.now() - this.lastFailureTime > this.recoveryTimeout) {
            this.state = "HALF_OPEN";
            console.log(
               `Circuit breaker for ${this.operationName} entering HALF_OPEN state`
            );
         } else {
            throw categorizeError(
               new Error(
                  `Circuit breaker is OPEN for ${this.operationName}. Too many recent failures.`
               )
            );
         }
      }

      try {
         const result = await operation();

         // Success - reset circuit breaker
         if (this.state === "HALF_OPEN") {
            this.state = "CLOSED";
            this.failures = 0;
            console.log(
               `Circuit breaker for ${this.operationName} reset to CLOSED state`
            );
         }

         return result;
      } catch (error) {
         this.failures++;
         this.lastFailureTime = Date.now();

         if (this.failures >= this.failureThreshold) {
            this.state = "OPEN";
            console.error(
               `Circuit breaker for ${this.operationName} opened due to ${this.failures} failures`
            );
         }

         throw error;
      }
   }

   getState() {
      return {
         state: this.state,
         failures: this.failures,
         lastFailureTime: this.lastFailureTime,
      };
   }

   reset() {
      this.state = "CLOSED";
      this.failures = 0;
      this.lastFailureTime = 0;
      console.log(`Circuit breaker for ${this.operationName} manually reset`);
   }
}

/**
 * Global circuit breakers for common operations
 */
export const portfolioCircuitBreakers = {
   imageUpload: new CircuitBreaker(3, 30000, "image upload"),
   databaseQuery: new CircuitBreaker(5, 60000, "database query"),
   migration: new CircuitBreaker(2, 300000, "migration"), // 5 minutes
};

/**
 * Utility to check if an operation should be attempted based on circuit breaker state
 */
export function canAttemptOperation(
   operationType: keyof typeof portfolioCircuitBreakers
): boolean {
   const circuitBreaker = portfolioCircuitBreakers[operationType];
   return circuitBreaker.getState().state !== "OPEN";
}

/**
 * Enhanced retry with circuit breaker protection
 */
export async function withCircuitBreakerRetry<T>(
   operation: () => Promise<T>,
   operationType: keyof typeof portfolioCircuitBreakers,
   config: RetryConfig = DEFAULT_RETRY_CONFIG,
   operationName?: string
): Promise<RetryResult<T>> {
   const circuitBreaker = portfolioCircuitBreakers[operationType];
   const finalOperationName = operationName || operationType;

   try {
      const result = await circuitBreaker.execute(async () => {
         const retryResult = await withRetry(
            operation,
            config,
            finalOperationName
         );

         if (!retryResult.success) {
            throw (
               retryResult.error || new Error(`${finalOperationName} failed`)
            );
         }

         return retryResult.data!;
      });

      return {
         success: true,
         data: result,
         attempts: 1, // Circuit breaker handles its own retry logic
      };
   } catch (error) {
      const portfolioError = categorizeError(error);
      return {
         success: false,
         error: portfolioError,
         attempts: 1,
      };
   }
}
