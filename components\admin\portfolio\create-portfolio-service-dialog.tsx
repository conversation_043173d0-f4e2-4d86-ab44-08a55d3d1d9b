"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
   <PERSON><PERSON>,
   DialogContent,
   DialogDescription,
   DialogFooter,
   DialogHeader,
   <PERSON>alogTitle,
   DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { useCreatePortfolioService } from "@/lib/hooks/use-portfolio";

import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2, Plus } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

interface CreatePortfolioServiceDialogProps {
   trigger?: React.ReactNode;
   onSuccess?: (serviceId: string) => void;
   open?: boolean;
   onOpenChange?: (open: boolean) => void;
   showTrigger?: boolean;
}

const portfolioServiceFormSchema = z.object({
   name: z
      .string()
      .min(1, "Service name is required")
      .max(100, "Service name must be less than 100 characters")
      .trim(),
   description: z
      .string()
      .max(500, "Description must be less than 500 characters"),
   coverImageUrl: z
      .string()
      .optional()
      .or(z.literal(""))
      .refine((val) => {
         if (!val) return true; // Empty string is valid
         // Allow full URLs (http/https) or relative paths starting with /
         return /^(https?:\/\/|\/)/i.test(val);
      }, "Cover image must be a valid URL or relative path starting with /")
      .refine(
         (val) => !val || !/^(javascript|data|vbscript):/i.test(val),
         "Invalid URL protocol"
      ),
   isActive: z.boolean(),
   displayOrder: z
      .number()
      .int("Display order must be a whole number")
      .min(0, "Display order cannot be negative"),
});

type PortfolioServiceFormData = z.infer<typeof portfolioServiceFormSchema>;

export default function CreatePortfolioServiceDialog({
   trigger,
   onSuccess,
   open: controlledOpen,
   onOpenChange: controlledOnOpenChange,
   showTrigger = false,
}: CreatePortfolioServiceDialogProps) {
   const [uncontrolledOpen, setUncontrolledOpen] = useState(false);
   const open =
      controlledOpen !== undefined ? controlledOpen : uncontrolledOpen;
   const setOpen = controlledOnOpenChange || setUncontrolledOpen;
   const router = useRouter();
   const createServiceMutation = useCreatePortfolioService();

   const form = useForm<PortfolioServiceFormData>({
      resolver: zodResolver(portfolioServiceFormSchema),
      defaultValues: {
         name: "",
         description: "",
         coverImageUrl: "",
         isActive: true,
         displayOrder: 0,
      },
      mode: "onChange",
   });

   const {
      register,
      handleSubmit,
      formState: { errors, isSubmitting },
      reset,
      watch,
      setValue,
   } = form;

   const isActive = watch("isActive");

   const onSubmit = async (data: PortfolioServiceFormData) => {
      try {
         const formData = new FormData();
         formData.append("name", data.name);
         if (data.description) {
            formData.append("description", data.description);
         }
         if (data.coverImageUrl) {
            formData.append("coverImageUrl", data.coverImageUrl);
         }
         formData.append("isActive", data.isActive.toString());
         formData.append("displayOrder", data.displayOrder.toString());

         const result = await createServiceMutation.mutateAsync(formData);

         if (result.success && result.data) {
            setOpen(false);
            reset();

            if (onSuccess) {
               onSuccess(result.data._id?.toString() || "");
            } else {
               // Default behavior: redirect to service detail page
               router.push(`/admin/portfolio/${result.data._id}`);
            }
         }
      } catch (error) {
         // Error is handled by the mutation's onError callback
         console.error("Failed to create portfolio service:", error);
      }
   };

   const handleOpenChange = (newOpen: boolean) => {
      setOpen(newOpen);
      if (!newOpen) {
         reset();
      }
   };

   return (
      <Dialog open={open} onOpenChange={handleOpenChange}>
         <DialogTrigger asChild>
            {trigger ||
               (showTrigger ? (
                  <Button className="bg-gradient-accent hover:opacity-90">
                     <Plus className="w-4 h-4" />
                     Create Service
                  </Button>
               ) : null)}
         </DialogTrigger>
         <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
               <DialogTitle>Create New Portfolio Service</DialogTitle>
               <DialogDescription>
                  Create a new portfolio service to organize your work.
               </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-5">
               <div className="space-y-3">
                  <Label htmlFor="name">Service Name *</Label>
                  <Input
                     id="name"
                     placeholder="e.g., Wedding Photography, Pre-Wedding Shoots"
                     {...register("name")}
                     className={errors.name ? "border-destructive" : ""}
                  />
                  {errors.name && (
                     <p className="text-sm text-destructive">
                        {errors.name.message}
                     </p>
                  )}
               </div>

               <div className="space-y-3">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                     id="description"
                     placeholder="Enter service description (optional)"
                     rows={3}
                     {...register("description")}
                     className={errors.description ? "border-destructive" : ""}
                  />
                  {errors.description && (
                     <p className="text-sm text-destructive">
                        {errors.description.message}
                     </p>
                  )}
               </div>

               <div className="flex items-center space-x-2">
                  <Switch
                     id="isActive"
                     checked={isActive}
                     onCheckedChange={(checked) =>
                        setValue("isActive", checked as boolean)
                     }
                  />
                  <Label htmlFor="isActive" className="text-sm font-normal">
                     Active service
                  </Label>
               </div>

               <DialogFooter>
                  <Button
                     type="button"
                     variant="outline"
                     onClick={() => setOpen(false)}
                     disabled={isSubmitting}
                  >
                     Cancel
                  </Button>
                  <Button
                     type="submit"
                     disabled={isSubmitting}
                     className="bg-gradient-accent hover:opacity-90"
                  >
                     {isSubmitting ? (
                        <>
                           <Loader2 className="w-4 h-4 animate-spin" />
                           Creating...
                        </>
                     ) : (
                        "Create Service"
                     )}
                  </Button>
               </DialogFooter>
            </form>
         </DialogContent>
      </Dialog>
   );
}
