import ElementReveal from "@/components/animations/element-reveal";
import StaggerReveal from "@/components/animations/stagger-reveal";
import TextReveal from "@/components/animations/text-reveal";
import { ImageGallery } from "@/components/image-gallery";
import { Button } from "@/components/ui/button";
import {
   Card,
   CardContent,
   CardDescription,
   CardHeader,
   CardTitle,
} from "@/components/ui/card";
import { ArrowRight, Camera, Clock, Gift, Heart } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

const galleryImages = [
   {
      src: "/images/bridal-shower/bridal-shower-1.JPG",
      alt: "Bridal shower celebration",
   },
   {
      src: "/images/bridal-shower/bridal-shower-2.JPG",
      alt: "Bridal shower decorations and setup",
   },
];

const features = [
   {
      icon: Heart,
      title: "Elegant Storytelling",
      description:
         "We capture the love and excitement of this special pre-wedding celebration with artistic vision.",
   },
   {
      icon: Camera,
      title: "Professional Coverage",
      description:
         "High-quality photography that documents every special moment, from decorations to guest interactions.",
   },
   {
      icon: Clock,
      title: "Flexible Coverage",
      description:
         "We adapt to your timeline, whether it's a morning brunch, afternoon tea, or evening celebration.",
   },
   {
      icon: Gift,
      title: "Detail Focused",
      description:
         "We capture all the beautiful details - decorations, gifts, food, and the special moments shared.",
   },
];

const eventTypes = [
   {
      title: "Traditional Showers",
      description:
         "Classic bridal shower photography that captures the elegance and tradition of this special celebration.",
   },
   {
      title: "Modern Celebrations",
      description:
         "Contemporary photography for modern bridal showers with unique themes and creative elements.",
   },
   {
      title: "Intimate Gatherings",
      description:
         "Warm, personal photography for small, meaningful celebrations with close friends and family.",
   },
   {
      title: "Themed Parties",
      description:
         "Creative photography that enhances and showcases your bridal shower theme and decorations.",
   },
   {
      title: "Outdoor Celebrations",
      description:
         "Beautiful outdoor photography that captures the natural beauty and relaxed atmosphere of garden parties.",
   },
   {
      title: "Luxury Events",
      description:
         "Sophisticated photography for high-end bridal showers with premium venues and elegant styling.",
   },
];

export default function BridalShowerPage() {
   return (
      <div className="min-h-screen">
         {/* Hero Section */}
         <section className="relative !pt-38 pb-16">
            <div className="absolute inset-0 z-0">
               <Image
                  src="/images/bridal-shower/bridal-shower-2.JPG"
                  alt="Bridal shower photography hero"
                  fill
                  className="object-cover object-top"
                  priority
                  sizes="100vw"
               />
               <div className="absolute inset-0 bg-black/50" />
            </div>

            <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
               <TextReveal>
                  <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
                     Bridal Shower Photography
                  </h1>
               </TextReveal>
               <TextReveal className="mb-8">
                  <p className="text-lg max-w-3xl mx-auto leading-relaxed">
                     Capture the joy and excitement of your bridal shower with
                     professional photography that preserves every special
                     moment
                  </p>
               </TextReveal>
               <ElementReveal>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                     <Button asChild size="lg">
                        <Link href="/contact">Book Your Bridal Shower</Link>
                     </Button>
                     <Button
                        asChild
                        variant="outline"
                        size="lg"
                        className="text-white hover:bg-white hover:text-black"
                     >
                        <Link href="/portfolio">
                           View Bridal Shower Gallery
                        </Link>
                     </Button>
                  </div>
               </ElementReveal>
            </div>
         </section>

         {/* Features Section */}
         <section className="py-20">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
               <div className="text-center mb-16">
                  <TextReveal>
                     <h2 className="text-3xl md:text-4xl font-bold mb-4">
                        Why Choose Us for Your Bridal Shower
                     </h2>
                  </TextReveal>
                  <TextReveal>
                     <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                        We understand the significance of this pre-wedding
                        celebration and capture it with the same care and
                        attention as your wedding day.
                     </p>
                  </TextReveal>
               </div>

               <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                  {features.map((feature, index) => (
                     <StaggerReveal key={feature.title} index={index}>
                        <Card className="text-center h-full">
                           <CardHeader>
                              <div className="flex justify-center mb-4">
                                 <feature.icon className="h-12 w-12 text-primary" />
                              </div>
                              <CardTitle>{feature.title}</CardTitle>
                           </CardHeader>
                           <CardContent>
                              <CardDescription className="text-center">
                                 {feature.description}
                              </CardDescription>
                           </CardContent>
                        </Card>
                     </StaggerReveal>
                  ))}
               </div>
            </div>
         </section>

         {/* Event Types Section */}
         <section className="py-20 bg-astral-grey">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
               <div className="text-center mb-16">
                  <TextReveal>
                     <h2 className="text-3xl md:text-4xl font-bold mb-4">
                        Perfect for Every Bridal Shower
                     </h2>
                  </TextReveal>
                  <TextReveal>
                     <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                        From intimate gatherings to grand celebrations, we
                        capture the essence of your bridal shower in every style
                        and setting.
                     </p>
                  </TextReveal>
               </div>

               <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {eventTypes.map((event, index) => (
                     <StaggerReveal key={event.title} index={index}>
                        <Card className="h-full">
                           <CardHeader>
                              <CardTitle>{event.title}</CardTitle>
                           </CardHeader>
                           <CardContent>
                              <CardDescription>
                                 {event.description}
                              </CardDescription>
                           </CardContent>
                        </Card>
                     </StaggerReveal>
                  ))}
               </div>
            </div>
         </section>

         {/* Gallery Section */}
         <section className="py-20 bg-background">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
               <div className="text-center mb-16">
                  <TextReveal>
                     <h2 className="text-3xl md:text-4xl font-bold mb-4">
                        Recent Bridal Shower Photography
                     </h2>
                  </TextReveal>
                  <TextReveal>
                     <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                        Take a look at some of our recent bridal shower
                        photography work that showcases our style and attention
                        to detail.
                     </p>
                  </TextReveal>
               </div>

               <ImageGallery images={galleryImages} />
            </div>
         </section>

         {/* CTA Section */}
         <section className="py-20 bg-astral-grey">
            <div className="container mx-auto px-4 text-center">
               <div className="max-w-4xl mx-auto">
                  <TextReveal>
                     <h2 className="text-4xl font-playfair font-bold text-foreground mb-6">
                        Ready to Capture Your{" "}
                        <span className="bg-gradient-accent bg-clip-text text-transparent">
                           Bridal Shower Memories?
                        </span>
                     </h2>
                  </TextReveal>
                  <TextReveal className="mb-8">
                     <p className="text-lg text-muted-foreground font-montserrat leading-relaxed">
                        Let&apos;s create beautiful memories of this special
                        celebration that you&apos;ll treasure alongside your
                        wedding photos.
                     </p>
                  </TextReveal>
                  <ElementReveal>
                     <div className="flex flex-col sm:flex-row gap-4 items-center justify-center">
                        <Button
                           asChild
                           size="lg"
                           className="bg-gradient-accent hover:opacity-90 font-montserrat font-semibold text-lg px-8"
                        >
                           <Link href="/contact">
                              Contact Now{" "}
                              <ArrowRight className="ml-2 h-5 w-5" />
                           </Link>
                        </Button>
                        <Button
                           asChild
                           variant="outline"
                           size="lg"
                           className="border-primary/20 hover:bg-primary/5 font-montserrat font-semibold text-lg px-8"
                        >
                           <Link href="/portfolio">View Portfolio</Link>
                        </Button>
                     </div>
                  </ElementReveal>
               </div>
            </div>
         </section>
      </div>
   );
}
