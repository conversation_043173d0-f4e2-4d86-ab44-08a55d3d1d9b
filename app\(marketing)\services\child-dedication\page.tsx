import ElementReveal from "@/components/animations/element-reveal";
import StaggerReveal from "@/components/animations/stagger-reveal";
import TextReveal from "@/components/animations/text-reveal";
import { ImageGallery } from "@/components/image-gallery";
import { Button } from "@/components/ui/button";
import {
   Card,
   CardContent,
   CardDescription,
   CardHeader,
   CardTitle,
} from "@/components/ui/card";
import { ArrowRight, Camera, Clock, Heart, Users } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

const galleryImages = [
   {
      src: "/images/child-dedication/child-dedication-1.PNG",
      alt: "Child dedication ceremony",
   },
];

export default function ChildDedicationPage() {
   return (
      <div className="min-h-screen">
         {/* Hero Section */}
         <section className="relative !pt-38 pb-16">
            <div className="absolute inset-0 z-0">
               <Image
                  src="/images/child-dedication/child-dedication-1.PNG"
                  alt="Child dedication photography hero"
                  fill
                  className="object-cover"
                  priority
                  sizes="100vw"
               />
               <div className="absolute inset-0 bg-black/50" />
            </div>

            <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
               <TextReveal>
                  <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
                     Child Dedication Photography
                  </h1>
               </TextReveal>
               <TextReveal className="mb-8">
                  <p className="text-lg max-w-3xl mx-auto leading-relaxed">
                     Document these precious ceremonies with sensitivity and
                     artistic vision, capturing the joy and significance of this
                     special milestone
                  </p>
               </TextReveal>
               <ElementReveal>
                  <div className="flex flex-col sm:flex-row gap-4 items-center justify-center">
                     <Button asChild size="lg">
                        <Link href="/contact">Book Your Session</Link>
                     </Button>
                     <Button
                        asChild
                        variant="outline"
                        size="lg"
                        className="text-white hover:bg-white hover:text-black"
                     >
                        <Link href="/portfolio">View Gallery</Link>
                     </Button>
                  </div>
               </ElementReveal>
            </div>
         </section>

         {/* About Section */}
         <section className="pt-20">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
               <div className="max-w-4xl bg-astral-grey rounded-sm p-8 mx-auto text-center">
                  <TextReveal>
                     <h2 className="text-3xl font-bold mb-4 italic">
                        Capturing Sacred Moments
                     </h2>
                  </TextReveal>
                  <TextReveal className="prose prose-lg italic mx-auto text-muted-foreground">
                     <p>
                        Child dedication ceremonies are deeply meaningful events
                        that celebrate the commitment of parents and community
                        to nurture and guide a child&apos;s spiritual journey.
                        We understand the sacred nature of these ceremonies and
                        approach each event with respect, sensitivity, and
                        artistic excellence. <br /> <br />
                        Our photography captures not just the formal ceremony,
                        but also the intimate family moments, the joy of parents
                        and grandparents, and the love that surrounds your child
                        on this special day. We work discreetly to document
                        these precious moments without disrupting the solemnity
                        of the occasion.
                     </p>
                  </TextReveal>
               </div>
            </div>
         </section>

         {/* Features */}
         <section className="py-20">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
               <div className="text-center mb-16">
                  <TextReveal>
                     <h2 className="text-3xl md:text-4xl font-bold mb-4">
                        Our Approach
                     </h2>
                  </TextReveal>
                  <TextReveal>
                     <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                        We combine respectful documentation with artistic
                        photography to create lasting memories of this important
                        milestone.
                     </p>
                  </TextReveal>
               </div>

               <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                  {[
                     {
                        icon: Heart,
                        title: "Respectful Documentation",
                        description:
                           "We work quietly and respectfully, capturing moments without disrupting the ceremony's sacred atmosphere.",
                     },
                     {
                        icon: Camera,
                        title: "Professional Quality",
                        description:
                           "High-quality photography that captures both the formal ceremony and candid family moments.",
                     },
                     {
                        icon: Users,
                        title: "Family Focus",
                        description:
                           "We ensure all family members are included, from grandparents to siblings, capturing the complete family story.",
                     },
                     {
                        icon: Clock,
                        title: "Flexible Coverage",
                        description:
                           "Coverage options from ceremony-only to extended family celebration photography.",
                     },
                  ].map((feature, index) => (
                     <StaggerReveal key={feature.title} index={index}>
                        <Card className="text-center h-full">
                           <CardHeader>
                              <div className="flex justify-center mb-4">
                                 <feature.icon className="h-12 w-12 text-primary" />
                              </div>
                              <CardTitle>{feature.title}</CardTitle>
                           </CardHeader>
                           <CardContent>
                              <CardDescription className="text-center">
                                 {feature.description}
                              </CardDescription>
                           </CardContent>
                        </Card>
                     </StaggerReveal>
                  ))}
               </div>
            </div>
         </section>

         {/* Gallery Section */}
         <section className="py-20 bg-astral-grey">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
               <div className="text-center mb-16">
                  <TextReveal>
                     <h2 className="text-3xl md:text-4xl font-bold mb-4">
                        Recent Child Dedication Photography
                     </h2>
                  </TextReveal>
                  <TextReveal>
                     <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                        Browse through our recent child dedication photography
                        to see how we capture these meaningful ceremonies.
                     </p>
                  </TextReveal>
               </div>

               <ImageGallery images={galleryImages} />
            </div>
         </section>

         {/* CTA Section */}
         <section className="py-20 bg-background">
            <div className="container mx-auto px-4 text-center">
               <div className="max-w-3xl mx-auto">
                  <TextReveal>
                     <h2 className="text-4xl font-playfair font-bold text-foreground mb-6">
                        Ready to Document This{" "}
                        <span className="bg-gradient-accent bg-clip-text text-transparent">
                           Special Day?{" "}
                        </span>
                     </h2>
                  </TextReveal>
                  <TextReveal className="mb-8">
                     <p className="text-lg text-muted-foreground font-montserrat leading-relaxed">
                        Let us help you preserve the memories of your
                        child&apos;s dedication ceremony with beautiful,
                        respectful photography.
                     </p>
                  </TextReveal>
                  <ElementReveal>
                     <div className="flex flex-col sm:flex-row gap-4 items-center justify-center">
                        <Button
                           asChild
                           size="lg"
                           className="bg-gradient-accent hover:opacity-90 font-montserrat font-semibold text-lg px-8"
                        >
                           <Link href="/contact">
                              Contact Now{" "}
                              <ArrowRight className="ml-2 h-5 w-5" />
                           </Link>
                        </Button>
                     </div>
                  </ElementReveal>
               </div>
            </div>
         </section>
      </div>
   );
}
