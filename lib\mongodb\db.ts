import { Collection, Document, MongoClient, ServerApiVersion } from "mongodb";

if (!process.env.MONGODB_URI) {
   throw new Error("MONGODB_URI is not defined");
}

const client = new MongoClient(process.env.MONGODB_URI, {
   serverApi: {
      version: ServerApiVersion.v1,
      strict: true,
      deprecationErrors: true,
   },
   // Add timeout configurations
   connectTimeoutMS: 10000, // 10 seconds
   serverSelectionTimeoutMS: 10000, // 10 seconds
   socketTimeoutMS: 45000, // 45 seconds
   maxPoolSize: 10, // Maximum 10 connections in pool
});

let isConnected = false;

export async function connectToDatabase() {
   if (!isConnected) {
      try {
         await client.connect();
         console.log("Connected to MongoDB");
         isConnected = true;
      } catch (error) {
         console.error("Error connecting to MongoDB:", error);
         throw error;
      }
   }
   return client;
}

export async function getDB(dbName: string = "gallery") {
   await connectToDatabase();
   return client.db(dbName);
}

export async function getCollection<T extends Document = Document>(
   collectionName: string
): Promise<Collection<T>> {
   const db = await getDB("gallery");
   return db.collection<T>(collectionName);
}

// Graceful shutdown
process.on("SIGINT", async () => {
   if (isConnected) {
      await client.close();
      console.log("MongoDB connection closed.");
      process.exit(0);
   }
});
