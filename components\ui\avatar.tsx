import { cn } from "@/lib/utils";

// Helper function to generate user initials
function getUserInitials(name: string): string {
   return name
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase())
      .slice(0, 2)
      .join("");
}

// Avatar component for consistent styling
interface AvatarProps {
   name: string;
   size?: "sm" | "md" | "lg";
   variant?: "primary" | "secondary";
}

export default function Avatar({
   name,
   size = "md",
   variant = "primary",
}: AvatarProps) {
   const initials = getUserInitials(name);

   const sizeClasses = {
      sm: "h-6 w-6 text-xs",
      md: "h-8 w-8 text-sm",
      lg: "h-10 w-10 text-base",
   };

   const variantClasses = {
      primary: "bg-primary/10 text-primary border-primary/20",
      secondary: "bg-muted text-muted-foreground border-border/30",
   };

   return (
      <div
         className={cn(
            "flex items-center justify-center rounded-full font-medium border transition-colors",
            sizeClasses[size],
            variantClasses[variant]
         )}
      >
         {initials}
      </div>
   );
}
