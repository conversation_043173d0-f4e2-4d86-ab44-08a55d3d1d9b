/**
 * Utility functions for drag and drop operations
 */

export interface DragDropItem {
   id: string;
   displayOrder: number;
}

/**
 * Reorder items in an array based on drag and drop operation
 */
export function reorderItems<T extends DragDropItem>(
   items: T[],
   activeId: string,
   overId: string
): T[] {
   const activeIndex = items.findIndex((item) => item.id === activeId);
   const overIndex = items.findIndex((item) => item.id === overId);

   if (activeIndex === -1 || overIndex === -1) {
      return items;
   }

   const newItems = [...items];
   const [movedItem] = newItems.splice(activeIndex, 1);
   newItems.splice(overIndex, 0, movedItem);

   // Update display orders
   return newItems.map((item, index) => ({
      ...item,
      displayOrder: index,
   }));
}

/**
 * Move an item left in the array
 */
export function moveItemLeft<T extends DragDropItem>(
   items: T[],
   itemId: string
): T[] {
   const currentIndex = items.findIndex((item) => item.id === itemId);

   if (currentIndex <= 0) {
      return items;
   }

   const newItems = [...items];
   [newItems[currentIndex], newItems[currentIndex - 1]] = [
      newItems[currentIndex - 1],
      newItems[currentIndex],
   ];

   // Update display orders
   return newItems.map((item, index) => ({
      ...item,
      displayOrder: index,
   }));
}

/**
 * Move an item right in the array
 */
export function moveItemRight<T extends DragDropItem>(
   items: T[],
   itemId: string
): T[] {
   const currentIndex = items.findIndex((item) => item.id === itemId);

   if (currentIndex >= items.length - 1 || currentIndex === -1) {
      return items;
   }

   const newItems = [...items];
   [newItems[currentIndex], newItems[currentIndex + 1]] = [
      newItems[currentIndex + 1],
      newItems[currentIndex],
   ];

   // Update display orders
   return newItems.map((item, index) => ({
      ...item,
      displayOrder: index,
   }));
}

/**
 * Generate service order array for API submission
 */
export function generateServiceOrder(
   services: Array<{ _id?: string; displayOrder: number }>
): Array<{ serviceId: string; displayOrder: number }> {
   return services.map((service, index) => ({
      serviceId: service._id?.toString() || "",
      displayOrder: index,
   }));
}
