import { z } from "zod";

/**
 * Security validation helpers
 */
const sanitizeString = (str: string) => {
   return str
      .replace(/<[^>]*>/g, "") // Remove HTML tags
      .replace(/[<>&"']/g, (match) => {
         const entities: Record<string, string> = {
            "<": "&lt;",
            ">": "&gt;",
            "&": "&amp;",
            '"': "&quot;",
            "'": "&#x27;",
         };
         return entities[match] || match;
      })
      .trim();
};

const validateNoScriptTags = (str: string) => {
   return !/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi.test(str);
};

const validateNoJavaScript = (str: string) => {
   return !/javascript:/gi.test(str) && !/on\w+\s*=/gi.test(str);
};

/**
 * Schema for creating a new portfolio service
 */
export const createPortfolioServiceSchema = z.object({
   name: z
      .string()
      .min(1, "Name is required")
      .max(100, "Name must be less than 100 characters")
      .trim()
      .refine(validateNoScriptTags, "Name contains invalid content")
      .refine(
         validateNoJavaScript,
         "Name contains potentially dangerous content"
      )
      .transform(sanitizeString),
   description: z
      .string()
      .max(500, "Description must be less than 500 characters")
      .optional()
      .or(z.literal(""))
      .refine(
         (val) => !val || validateNoScriptTags(val),
         "Description contains invalid content"
      )
      .refine(
         (val) => !val || validateNoJavaScript(val),
         "Description contains potentially dangerous content"
      )
      .transform((val) => (val ? sanitizeString(val) : val)),
   coverImageUrl: z
      .string()
      .optional()
      .or(z.literal(""))
      .refine((val) => {
         if (!val) return true; // Empty string is valid
         // Allow full URLs (http/https) or relative paths starting with /
         return /^(https?:\/\/|\/)/i.test(val);
      }, "Cover image must be a valid URL or relative path starting with /")
      .refine(
         (val) => !val || !/^(javascript|data|vbscript):/i.test(val),
         "Invalid URL protocol"
      ),
   isActive: z.boolean().default(true),
   displayOrder: z
      .number()
      .int("Display order must be an integer")
      .min(0, "Display order must be non-negative")
      .max(9999, "Display order is too large")
      .default(0),
});

/**
 * Schema for updating a portfolio service
 */
export const updatePortfolioServiceSchema = z.object({
   name: z
      .string()
      .min(1, "Name is required")
      .max(100, "Name must be less than 100 characters")
      .trim()
      .refine(validateNoScriptTags, "Name contains invalid content")
      .refine(
         validateNoJavaScript,
         "Name contains potentially dangerous content"
      )
      .transform(sanitizeString)
      .optional(),
   description: z
      .string()
      .max(500, "Description must be less than 500 characters")
      .optional()
      .or(z.literal(""))
      .refine(
         (val) => !val || validateNoScriptTags(val),
         "Description contains invalid content"
      )
      .refine(
         (val) => !val || validateNoJavaScript(val),
         "Description contains potentially dangerous content"
      )
      .transform((val) => (val ? sanitizeString(val) : val)),
   coverImageUrl: z
      .string()
      .optional()
      .or(z.literal(""))
      .refine((val) => {
         if (!val) return true; // Empty string is valid
         // Allow full URLs (http/https) or relative paths starting with /
         return /^(https?:\/\/|\/)/i.test(val);
      }, "Cover image must be a valid URL or relative path starting with /")
      .refine(
         (val) => !val || !/^(javascript|data|vbscript):/i.test(val),
         "Invalid URL protocol"
      ),
   isActive: z.boolean().optional(),
   displayOrder: z
      .number()
      .int("Display order must be an integer")
      .min(0, "Display order must be non-negative")
      .max(9999, "Display order is too large")
      .optional(),
});

/**
 * Schema for creating a new portfolio image
 */
export const createPortfolioImageSchema = z.object({
   url: z
      .string()
      .url("URL must be a valid URL")
      .min(1, "URL is required")
      .refine(
         (val) => !/^(javascript|data|vbscript):/i.test(val),
         "Invalid URL protocol"
      )
      .refine(
         (val) => /^(https?:\/\/|\/)/i.test(val),
         "URL must use HTTP/HTTPS or be relative"
      ),
   name: z
      .string()
      .min(1, "Name is required")
      .max(255, "Name must be less than 255 characters")
      .trim()
      .refine(
         (val) => !/[<>:"|?*\\\/]/g.test(val),
         "Name contains invalid characters"
      )
      .refine(
         (val) => !/\.\./g.test(val),
         "Name cannot contain path traversal sequences"
      )
      .transform(sanitizeString),
   serviceId: z
      .string()
      .min(1, "Service ID is required")
      .regex(
         /^[0-9a-fA-F]{24}$/,
         "Service ID must be a valid MongoDB ObjectId"
      ),
   width: z
      .number()
      .int("Width must be an integer")
      .positive("Width must be positive")
      .max(50000, "Width is too large"),
   height: z
      .number()
      .int("Height must be an integer")
      .positive("Height must be positive")
      .max(50000, "Height is too large"),
   fileSize: z
      .number()
      .int("File size must be an integer")
      .positive("File size must be positive")
      .max(50 * 1024 * 1024, "File size must be less than 50MB"), // 50MB limit
   mimeType: z
      .string()
      .regex(/^image\//, "MIME type must be an image type")
      .refine(
         (type) =>
            [
               "image/jpeg",
               "image/jpg",
               "image/png",
               "image/webp",
               "image/gif",
            ].includes(type),
         "MIME type must be jpeg, jpg, png, webp, or gif"
      ),
   displayOrder: z
      .number()
      .int("Display order must be an integer")
      .min(0, "Display order must be non-negative")
      .max(9999, "Display order is too large")
      .default(0),
});

/**
 * Schema for updating a portfolio image
 */
export const updatePortfolioImageSchema = z.object({
   name: z
      .string()
      .min(1, "Name is required")
      .max(255, "Name must be less than 255 characters")
      .trim()
      .refine(
         (val) => !/[<>:"|?*\\\/]/g.test(val),
         "Name contains invalid characters"
      )
      .refine(
         (val) => !/\.\./g.test(val),
         "Name cannot contain path traversal sequences"
      )
      .transform(sanitizeString)
      .optional(),
   altText: z
      .string()
      .max(255, "Alt text must be less than 255 characters")
      .refine(validateNoScriptTags, "Alt text contains invalid content")
      .refine(
         validateNoJavaScript,
         "Alt text contains potentially dangerous content"
      )
      .transform(sanitizeString)
      .optional(),
   serviceId: z
      .string()
      .min(1, "Service ID is required")
      .regex(/^[0-9a-fA-F]{24}$/, "Service ID must be a valid MongoDB ObjectId")
      .optional(),
   displayOrder: z
      .number()
      .int("Display order must be an integer")
      .min(0, "Display order must be non-negative")
      .max(9999, "Display order is too large")
      .optional(),
});

/**
 * Schema for portfolio service form validation (client-side)
 */
export const portfolioServiceFormSchema = z.object({
   name: z
      .string()
      .min(1, "Service name is required")
      .max(100, "Service name must be less than 100 characters")
      .trim()
      .refine((name) => name.length > 0, "Service name cannot be empty"),
   description: z
      .string()
      .max(500, "Description must be less than 500 characters")
      .optional()
      .or(z.literal("")),
   coverImageUrl: z
      .string()
      .optional()
      .or(z.literal(""))
      .refine((val) => {
         if (!val) return true; // Empty string is valid
         // Allow full URLs (http/https) or relative paths starting with /
         return /^(https?:\/\/|\/)/i.test(val);
      }, "Cover image must be a valid URL or relative path starting with /")
      .refine(
         (val) => !val || !/^(javascript|data|vbscript):/i.test(val),
         "Invalid URL protocol"
      ),
   isActive: z.boolean().default(true),
   displayOrder: z
      .number()
      .int("Display order must be a whole number")
      .min(0, "Display order cannot be negative")
      .default(0),
});

/**
 * Schema for portfolio image upload form validation
 */
export const portfolioImageUploadSchema = z.object({
   serviceId: z.string().min(1, "Please select a portfolio service"),
   files: z
      .array(z.instanceof(File))
      .min(1, "Please select at least one image")
      .max(20, "Cannot upload more than 20 images at once")
      .refine(
         (files) => files.every((file) => file.size <= 50 * 1024 * 1024),
         "Each file must be less than 50MB"
      )
      .refine(
         (files) => files.every((file) => file.type.startsWith("image/")),
         "All files must be images"
      )
      .refine(
         (files) =>
            files.every((file) =>
               [
                  "image/jpeg",
                  "image/jpg",
                  "image/png",
                  "image/webp",
                  "image/gif",
               ].includes(file.type)
            ),
         "Images must be JPEG, PNG, WebP, or GIF format"
      ),
});

/**
 * Schema for adding existing images to portfolio service
 */
export const addImagesToServiceSchema = z.object({
   serviceId: z
      .string()
      .min(1, "Service ID is required")
      .regex(
         /^[0-9a-fA-F]{24}$/,
         "Service ID must be a valid MongoDB ObjectId"
      ),
   imageIds: z
      .array(
         z
            .string()
            .min(1, "Image ID cannot be empty")
            .regex(
               /^[0-9a-fA-F]{24}$/,
               "Image ID must be a valid MongoDB ObjectId"
            )
      )
      .min(1, "At least one image must be selected")
      .max(100, "Cannot add more than 100 images at once"),
});

/**
 * Schema for bulk portfolio image operations
 */
export const bulkPortfolioImageOperationSchema = z
   .object({
      imageIds: z
         .array(
            z
               .string()
               .min(1, "Image ID cannot be empty")
               .regex(
                  /^[0-9a-fA-F]{24}$/,
                  "Image ID must be a valid MongoDB ObjectId"
               )
         )
         .min(1, "At least one image must be selected")
         .max(100, "Cannot operate on more than 100 images at once"),
      operation: z.enum(["delete", "move", "reorder"]),
      targetServiceId: z
         .string()
         .min(1, "Target service ID is required")
         .regex(
            /^[0-9a-fA-F]{24}$/,
            "Target service ID must be a valid MongoDB ObjectId"
         )
         .optional(),
      newOrder: z
         .array(
            z
               .string()
               .regex(
                  /^[0-9a-fA-F]{24}$/,
                  "Image ID must be a valid MongoDB ObjectId"
               )
         )
         .optional(),
   })
   .superRefine((data, ctx) => {
      if (data.operation === "move" && !data.targetServiceId) {
         ctx.addIssue({
            code: "custom",
            message: "Target service ID is required for move operation",
            path: ["targetServiceId"],
         });
      }

      if (data.operation === "reorder" && !data.newOrder) {
         ctx.addIssue({
            code: "custom",
            message: "New order is required for reorder operation",
            path: ["newOrder"],
         });
      }

      if (
         data.operation === "reorder" &&
         data.newOrder &&
         data.newOrder.length !== data.imageIds.length
      ) {
         ctx.addIssue({
            code: "custom",
            message: "New order must contain all selected image IDs",
            path: ["newOrder"],
         });
      }
   });

/**
 * Schema for reordering portfolio images
 */
export const reorderPortfolioImagesSchema = z.object({
   serviceId: z
      .string()
      .min(1, "Service ID is required")
      .regex(
         /^[0-9a-fA-F]{24}$/,
         "Service ID must be a valid MongoDB ObjectId"
      ),
   imageOrder: z
      .array(
         z.object({
            imageId: z
               .string()
               .min(1, "Image ID is required")
               .regex(
                  /^[0-9a-fA-F]{24}$/,
                  "Image ID must be a valid MongoDB ObjectId"
               ),
            displayOrder: z
               .number()
               .int("Display order must be an integer")
               .min(0, "Display order must be non-negative")
               .max(9999, "Display order is too large"),
         })
      )
      .min(1, "At least one image order must be specified")
      .max(100, "Cannot reorder more than 100 images at once"),
});

/**
 * Schema for editing image metadata
 */
export const editImageMetadataSchema = z.object({
   name: z
      .string()
      .min(1, "Name is required")
      .max(255, "Name must be less than 255 characters")
      .trim(),
   altText: z
      .string()
      .max(255, "Alt text must be less than 255 characters")
      .optional()
      .or(z.literal("")),
   generateAltText: z.boolean().optional().default(false),
});

/**
 * Type inference for create portfolio service form
 */
export type CreatePortfolioServiceFormData = z.infer<
   typeof createPortfolioServiceSchema
>;

/**
 * Type inference for update portfolio service form
 */
export type UpdatePortfolioServiceFormData = z.infer<
   typeof updatePortfolioServiceSchema
>;

/**
 * Type inference for create portfolio image form
 */
export type CreatePortfolioImageFormData = z.infer<
   typeof createPortfolioImageSchema
>;

/**
 * Type inference for update portfolio image form
 */
export type UpdatePortfolioImageFormData = z.infer<
   typeof updatePortfolioImageSchema
>;

/**
 * Type inference for portfolio service form
 */
export type PortfolioServiceFormData = z.infer<
   typeof portfolioServiceFormSchema
>;

/**
 * Type inference for portfolio image upload form
 */
export type PortfolioImageUploadFormData = z.infer<
   typeof portfolioImageUploadSchema
>;

/**
 * Type inference for adding images to service form
 */
export type AddImagesToServiceFormData = z.infer<
   typeof addImagesToServiceSchema
>;

/**
 * Type inference for bulk portfolio image operations
 */
export type BulkPortfolioImageOperationFormData = z.infer<
   typeof bulkPortfolioImageOperationSchema
>;

/**
 * Type inference for reordering portfolio images
 */
export type ReorderPortfolioImagesFormData = z.infer<
   typeof reorderPortfolioImagesSchema
>;

/**
 * Type inference for editing image metadata
 */
export type EditImageMetadataFormData = z.infer<typeof editImageMetadataSchema>;
