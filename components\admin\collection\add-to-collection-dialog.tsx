"use client";

import CreateCollectionDialog from "@/components/admin/collection/create-collection-dialog";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogFooter,
   DialogHeader,
   DialogTitle,
} from "@/components/ui/dialog";
import {
   useCollections,
   useCreateCollection,
} from "@/lib/hooks/use-collections";
import { Plus, Tags } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

interface AddToCollectionDialogProps {
   open: boolean;
   onOpenChange: (open: boolean) => void;
   imageId: string;
   currentCollectionIds?: string[];
   onAddToCollections: (
      imageId: string,
      collectionIds: string[]
   ) => Promise<void>;
}

export default function AddToCollectionDialog({
   open,
   onOpenChange,
   imageId,
   currentCollectionIds = [],
   onAddToCollections,
}: AddToCollectionDialogProps) {
   const [selectedCollectionIds, setSelectedCollectionIds] =
      useState<string[]>(currentCollectionIds);
   const [isUpdating, setIsUpdating] = useState(false);
   const [showCreateCollection, setShowCreateCollection] = useState(false);

   const { data: collectionsData, isLoading: collectionsLoading } =
      useCollections({ limit: 100 });
   const createCollectionMutation = useCreateCollection();

   const collections = collectionsData?.data || [];

   const handleToggleCollection = (collectionId: string, checked: boolean) => {
      if (checked) {
         setSelectedCollectionIds((prev) => [...prev, collectionId]);
      } else {
         setSelectedCollectionIds((prev) =>
            prev.filter((id) => id !== collectionId)
         );
      }
   };

   const handleSave = async () => {
      try {
         setIsUpdating(true);
         await onAddToCollections(imageId, selectedCollectionIds);
         onOpenChange(false);
         toast.success("Collections updated successfully");
      } catch (error) {
         console.error("Error updating collections:", error);
         toast.error("Failed to update collections");
      } finally {
         setIsUpdating(false);
      }
   };

   const handleCreateCollection = () => {
      setShowCreateCollection(true);
   };

   const handleCreateCollectionSuccess = (collectionId: string) => {
      setShowCreateCollection(false);
      setSelectedCollectionIds((prev) => [...prev, collectionId]);
      toast.success("Collection created successfully");
   };

   return (
      <Dialog open={open} onOpenChange={onOpenChange}>
         <DialogContent className="sm:max-w-md">
            <DialogHeader>
               <DialogTitle>Add to Collections</DialogTitle>
               <DialogDescription>
                  Select which collections this image should belong to.
               </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
               {collectionsLoading ? (
                  <div className="flex items-center justify-center h-32">
                     <div className="text-sm text-muted-foreground">
                        Loading collections...
                     </div>
                  </div>
               ) : collections.length === 0 ? (
                  <div className="flex flex-col items-center justify-center h-64 text-muted-foreground">
                     <div className="text-center">
                        <Tags className="w-16 h-16 mx-auto mb-4 opacity-50" />
                        <h3 className="text-lg font-medium mb-2 text-foreground">
                           No Collections Available
                        </h3>
                        <p className="text-sm mb-4 max-w-sm">
                           Create your first collection to organize your images
                           by themes or categories.
                        </p>
                        <Button
                           onClick={handleCreateCollection}
                           className="bg-gradient-accent hover:opacity-90"
                        >
                           Create Collection
                        </Button>
                     </div>
                  </div>
               ) : (
                  <div className="space-y-3">
                     <div className="text-sm font-medium">
                        Select Collections:
                     </div>
                     <div className="max-h-64 overflow-y-auto space-y-2">
                        {collections.map((collection) => (
                           <div
                              key={collection._id?.toString() || ""}
                              className="flex items-center space-x-3 p-2 rounded-md hover:bg-accent/50"
                           >
                              <Checkbox
                                 id={`collection-${collection._id}`}
                                 checked={selectedCollectionIds.includes(
                                    collection._id?.toString() || ""
                                 )}
                                 onCheckedChange={(checked) =>
                                    handleToggleCollection(
                                       collection._id?.toString() || "",
                                       checked as boolean
                                    )
                                 }
                              />
                              <label
                                 htmlFor={`collection-${collection._id}`}
                                 className="flex-1 text-sm cursor-pointer"
                              >
                                 <div className="flex items-center space-x-2">
                                    <Tags className="w-4 h-4 text-muted-foreground" />
                                    <span>{collection.name}</span>
                                    <span className="text-xs text-muted-foreground">
                                       ({collection.imageCount} images)
                                    </span>
                                 </div>
                                 {collection.description && (
                                    <div className="text-xs text-muted-foreground mt-1">
                                       {collection.description}
                                    </div>
                                 )}
                              </label>
                           </div>
                        ))}
                     </div>

                     <Button
                        variant="outline"
                        onClick={handleCreateCollection}
                        disabled={createCollectionMutation.isPending}
                        className="w-full"
                     >
                        <Plus className="w-4 h-4" />
                        Create New Collection
                     </Button>
                  </div>
               )}
            </div>

            {collections.length > 0 && (
               <DialogFooter>
                  <Button variant="outline" onClick={() => onOpenChange(false)}>
                     Cancel
                  </Button>
                  <Button
                     onClick={handleSave}
                     disabled={isUpdating || collectionsLoading}
                     className="bg-gradient-accent hover:opacity-90"
                  >
                     {isUpdating ? "Updating..." : "Save Changes"}
                  </Button>
               </DialogFooter>
            )}

            <CreateCollectionDialog
               open={showCreateCollection}
               onOpenChange={setShowCreateCollection}
               onSuccess={handleCreateCollectionSuccess}
            />
         </DialogContent>
      </Dialog>
   );
}
