import { Button } from "@/components/ui/button";
import { CollectionWithStats } from "@/lib/models";
import { cn } from "@/lib/utils";
import { PhotoIcon } from "@heroicons/react/24/solid";
import { motion } from "framer-motion";
import { <PERSON><PERSON>ef<PERSON>, Share2, <PERSON>rk<PERSON>, <PERSON> } from "lucide-react";
import Link from "next/link";

interface CollectionHeroProps {
   collection: CollectionWithStats;
   isLiked?: boolean;
   handleLike?: () => void;
   handleShare: () => void;
}

export default function CollectionHero({
   collection,
   handleShare,
}: CollectionHeroProps) {
   // Format date

   // Generate gradient background
   const getGradient = () => {
      if (collection.color) {
         return `linear-gradient(135deg, ${collection.color}15, ${collection.color}05)`;
      }
      return "linear-gradient(135deg, hsl(var(--primary) / 0.1), hsl(var(--accent) / 0.05))";
   };

   return (
      <>
         <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="mb-12"
         >
            {/* Hero Section */}
            <div
               className="relative rounded-3xl p-8 md:p-12 mb-8 overflow-hidden"
               style={{ background: getGradient() }}
            >
               {/* Background Pattern */}
               <div className="absolute inset-0 opacity-5">
                  <div className="absolute top-4 right-4">
                     <PhotoIcon className="w-32 h-32 text-current rotate-12" />
                  </div>
                  <div className="absolute bottom-4 left-4">
                     <Sparkles className="w-24 h-24 text-current -rotate-12" />
                  </div>
               </div>

               <div className="relative z-10">
                  <div className="flex flex-col lg:flex-row gap-8 items-start">
                     {/* Collection Info */}
                     <div className="flex-1">
                        {/* Icon and Title */}
                        <div className="flex items-start gap-4 mb-6">
                           <motion.div
                              initial={{ scale: 0, rotate: -45 }}
                              animate={{ scale: 1, rotate: 0 }}
                              transition={{ duration: 0.6, delay: 0.2 }}
                              className={cn(
                                 "flex items-center justify-center w-16 h-16 rounded-2xl",
                                 "bg-gradient-to-br from-white/20 to-white/5 backdrop-blur-sm",
                                 "border border-white/20"
                              )}
                              style={{
                                 backgroundColor: collection.color
                                    ? `${collection.color}20`
                                    : undefined,
                                 borderColor: collection.color
                                    ? `${collection.color}40`
                                    : undefined,
                              }}
                           >
                              <Tag
                                 className="w-8 h-8"
                                 style={{
                                    color:
                                       collection.color ||
                                       "hsl(var(--primary))",
                                 }}
                              />
                           </motion.div>

                           <div className="flex-1">
                              <h1 className="text-2xl md:text-3xl font-bold text-foreground">
                                 {collection.name}
                              </h1>

                              {collection.description && (
                                 <p className="text-muted-foreground leading-relaxed">
                                    {collection.description}
                                 </p>
                              )}
                           </div>
                        </div>

                        {/* Metadata */}
                        <div className="flex flex-wrap gap-4 text-sm text-muted-foreground mb-6">
                           <div className="flex items-center gap-2">
                              <PhotoIcon className="w-4 h-4" />
                              <span>{collection.imageCount} images</span>
                           </div>
                        </div>

                        {/* Actions */}
                        <div className="flex flex-wrap gap-3">
                           <Button
                              onClick={handleShare}
                              variant="secondary"
                              className="flex items-center gap-2 bg-white/10 backdrop-blur-sm border-white/20"
                           >
                              <Share2 className="w-4 h-4" />
                              Share
                           </Button>

                           <Button
                              asChild
                              variant="ghost"
                              className="text-muted-foreground hover:text-foreground"
                           >
                              <Link
                                 href="/gallery/collections"
                                 className="flex items-center gap-2"
                              >
                                 <ArrowLeft className="w-4 h-4" />
                                 Back to Collections
                              </Link>
                           </Button>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </motion.div>
      </>
   );
}
