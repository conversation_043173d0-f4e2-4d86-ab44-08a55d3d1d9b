"use client";

import CreatePortfolioServiceDialog from "@/components/admin/portfolio/create-portfolio-service-dialog";
import { DraggableServiceCard } from "@/components/admin/portfolio/draggable-service-card";
import { PortfolioErrorBoundary } from "@/components/admin/portfolio/portfolio-error-boundary";
import {
   AlertDialog,
   AlertDialogCancel,
   AlertDialogContent,
   AlertDialogDescription,
   AlertDialogFooter,
   AlertDialogHeader,
   AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button, buttonVariants } from "@/components/ui/button";
import {
   Card,
   CardContent,
   CardDescription,
   CardHeader,
   CardTitle,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import {
   useDeletePortfolioService,
   usePortfolioServices,
   useReorderPortfolioServices,
   useUpdatePortfolioService,
} from "@/lib/hooks/use-portfolio";
import { PortfolioServiceWithStats } from "@/lib/models";
import { categorizeError } from "@/lib/utils/portfolio-error-handling";
import { showErrorToast } from "@/lib/utils/toast-notifications";
import {
   DndContext,
   DragEndEvent,
   DragOverlay,
   DragStartEvent,
   KeyboardSensor,
   PointerSensor,
   closestCenter,
   useSensor,
   useSensors,
} from "@dnd-kit/core";
import {
   SortableContext,
   arrayMove,
   rectSortingStrategy,
   sortableKeyboardCoordinates,
} from "@dnd-kit/sortable";
import { Briefcase, Loader2 } from "lucide-react";
import { useEffect, useState } from "react";

export default function PortfolioPage() {
   const { data: servicesData, isLoading, error } = usePortfolioServices();
   const deleteServiceMutation = useDeletePortfolioService();
   const reorderServicesMutation = useReorderPortfolioServices();
   const updateServiceMutation = useUpdatePortfolioService();

   const [deleteDialog, setDeleteDialog] = useState({
      open: false,
      serviceId: "",
      serviceName: "",
      imageCount: 0,
   });
   const [dropdownOpen, setDropdownOpen] = useState<{
      [serviceId: string]: boolean;
   }>({});
   const [activeId, setActiveId] = useState<string | null>(null);
   const [services, setServices] = useState<PortfolioServiceWithStats[]>([]);

   // Sensors for drag and drop
   const sensors = useSensors(
      useSensor(PointerSensor, {
         activationConstraint: {
            distance: 8,
         },
      }),
      useSensor(KeyboardSensor, {
         coordinateGetter: sortableKeyboardCoordinates,
      })
   );

   // Update local services state when data changes
   useEffect(() => {
      if (servicesData) {
         // Sort services by displayOrder and then by isActive (active first)
         const sortedServices = [...servicesData].sort((a, b) => {
            if (a.isActive !== b.isActive) {
               return a.isActive ? -1 : 1; // Active services first
            }
            return a.displayOrder - b.displayOrder;
         });
         setServices(sortedServices);
      }
   }, [servicesData]);

   // Close dialog only after successful deletion
   useEffect(() => {
      if (deleteServiceMutation.isSuccess && deleteDialog.open) {
         setDeleteDialog({
            open: false,
            serviceId: "",
            serviceName: "",
            imageCount: 0,
         });
         deleteServiceMutation.reset();
      }
   }, [
      deleteServiceMutation.isSuccess,
      deleteDialog.open,
      deleteServiceMutation,
   ]);

   const handleDeleteService = async () => {
      deleteServiceMutation.mutate(deleteDialog.serviceId);
   };

   const handleDragStart = (event: DragStartEvent) => {
      setActiveId(event.active.id as string);
   };

   const handleDragEnd = (event: DragEndEvent) => {
      const { active, over } = event;
      setActiveId(null);

      if (!over || active.id === over.id) {
         return;
      }

      const activeService = services.find(
         (s) => s._id?.toString() === active.id
      );
      const overService = services.find((s) => s._id?.toString() === over.id);

      if (!activeService || !overService) {
         return;
      }

      // Only allow reordering within the same active/inactive group
      if (activeService.isActive !== overService.isActive) {
         return;
      }

      const activeIndex = services.findIndex(
         (s) => s._id?.toString() === active.id
      );
      const overIndex = services.findIndex(
         (s) => s._id?.toString() === over.id
      );

      if (activeIndex !== overIndex) {
         const newServices = arrayMove(services, activeIndex, overIndex);
         setServices(newServices);

         // Update display orders for the affected group
         const isActiveGroup = activeService.isActive;
         const groupServices = newServices.filter(
            (s) => s.isActive === isActiveGroup
         );
         const serviceOrder = groupServices.map((service, index) => ({
            serviceId: service._id?.toString() || "",
            displayOrder: index,
         }));

         const formData = new FormData();
         formData.append("serviceOrder", JSON.stringify(serviceOrder));
         reorderServicesMutation.mutate(formData, {
            onError: () => {
               // Revert the optimistic update on error
               if (servicesData) {
                  const sortedServices = [...servicesData].sort((a, b) => {
                     if (a.isActive !== b.isActive) {
                        return a.isActive ? -1 : 1;
                     }
                     return a.displayOrder - b.displayOrder;
                  });
                  setServices(sortedServices);
               }
            },
         });
      }
   };

   const handleToggleActive = (service: PortfolioServiceWithStats) => {
      const formData = new FormData();
      formData.append("name", service.name);
      formData.append("description", service.description || "");
      formData.append("coverImageUrl", service.coverImageUrl || "");
      formData.append("isActive", (!service.isActive).toString());
      formData.append("displayOrder", service.displayOrder.toString());

      updateServiceMutation.mutate({
         id: service._id?.toString() || "",
         formData,
      });
   };

   const handleMoveLeft = (service: PortfolioServiceWithStats) => {
      const isActiveGroup = service.isActive;
      const groupServices = services.filter(
         (s) => s.isActive === isActiveGroup
      );
      const currentIndex = groupServices.findIndex(
         (s) => s._id?.toString() === service._id?.toString()
      );

      if (currentIndex > 0) {
         const newGroupServices = [...groupServices];
         [newGroupServices[currentIndex], newGroupServices[currentIndex - 1]] =
            [
               newGroupServices[currentIndex - 1],
               newGroupServices[currentIndex],
            ];

         const serviceOrder = newGroupServices.map((s, index) => ({
            serviceId: s._id?.toString() || "",
            displayOrder: index,
         }));

         const formData = new FormData();
         formData.append("serviceOrder", JSON.stringify(serviceOrder));
         reorderServicesMutation.mutate(formData);
      }
   };

   const handleMoveRight = (service: PortfolioServiceWithStats) => {
      const isActiveGroup = service.isActive;
      const groupServices = services.filter(
         (s) => s.isActive === isActiveGroup
      );
      const currentIndex = groupServices.findIndex(
         (s) => s._id?.toString() === service._id?.toString()
      );

      if (currentIndex < groupServices.length - 1) {
         const newGroupServices = [...groupServices];
         [newGroupServices[currentIndex], newGroupServices[currentIndex + 1]] =
            [
               newGroupServices[currentIndex + 1],
               newGroupServices[currentIndex],
            ];

         const serviceOrder = newGroupServices.map((s, index) => ({
            serviceId: s._id?.toString() || "",
            displayOrder: index,
         }));

         const formData = new FormData();
         formData.append("serviceOrder", JSON.stringify(serviceOrder));
         reorderServicesMutation.mutate(formData);
      }
   };

   if (error) {
      const portfolioError = categorizeError(error);
      return (
         <div className="p-8">
            <div className="text-center space-y-4">
               <div className="text-6xl">⚠️</div>
               <div>
                  <h2 className="text-xl font-semibold text-destructive mb-2">
                     Failed to Load Portfolio Services
                  </h2>
                  <p className="text-muted-foreground mb-4">
                     {portfolioError.message}
                  </p>
                  <div className="flex justify-center gap-2">
                     <Button
                        onClick={() => window.location.reload()}
                        variant="outline"
                     >
                        <Loader2 className="w-4 h-4" />
                        Retry
                     </Button>
                     <Button
                        onClick={() =>
                           showErrorToast(portfolioError, {
                              onRetry: () => window.location.reload(),
                              retryLabel: "Reload Page",
                           })
                        }
                        variant="ghost"
                     >
                        Show Details
                     </Button>
                  </div>
               </div>
            </div>
         </div>
      );
   }

   const hasServices = services.length > 0;

   return (
      <PortfolioErrorBoundary
         onError={(error, errorInfo) => {
            console.error("Portfolio Page Error:", error, errorInfo);
            showErrorToast(categorizeError(error), {
               duration: 8000,
               onRetry: () => window.location.reload(),
               retryLabel: "Reload Page",
            });
         }}
      >
         <div className="p-8 space-y-8">
            {/* Header */}
            <div className="flex flex-col sm:flex-row sm:items-center justify-between">
               <div className="flex items-center space-x-4">
                  <div>
                     <h1 className="text-2xl sm:text-3xl font-bold text-foreground">
                        Portfolio Services
                     </h1>
                     <p className="text-muted-foreground">
                        Manage your portfolio services and associated images
                     </p>
                  </div>
               </div>

               <div className="flex items-center space-x-3 mt-4 sm:mt-0">
                  <CreatePortfolioServiceDialog showTrigger={true} />
               </div>
            </div>

            {/* Main Content */}
            {isLoading ? (
               <div className="space-y-8">
                  <div>
                     <h2 className="text-xl font-semibold mb-4">
                        Active Services
                     </h2>
                     <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                        {Array.from({ length: 4 }).map((_, i) => (
                           <Card key={i} className="border-border/30 py-0">
                              <CardContent className="p-0">
                                 <Skeleton className="w-full h-48 rounded-t-lg" />
                                 <div className="p-4 space-y-2">
                                    <Skeleton className="h-5 w-3/4" />
                                    <Skeleton className="h-4 w-1/2" />
                                 </div>
                              </CardContent>
                           </Card>
                        ))}
                     </div>
                  </div>
                  <div>
                     <h2 className="text-xl font-semibold mb-4">
                        Inactive Services
                     </h2>
                     <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                        {Array.from({ length: 4 }).map((_, i) => (
                           <Card key={i + 4} className="border-border/30 py-0">
                              <CardContent className="p-0">
                                 <Skeleton className="w-full h-48 rounded-t-lg" />
                                 <div className="p-4 space-y-2">
                                    <Skeleton className="h-5 w-3/4" />
                                    <Skeleton className="h-4 w-1/2" />
                                 </div>
                              </CardContent>
                           </Card>
                        ))}
                     </div>
                  </div>
               </div>
            ) : hasServices ? (
               <DndContext
                  sensors={sensors}
                  collisionDetection={closestCenter}
                  onDragStart={handleDragStart}
                  onDragEnd={handleDragEnd}
               >
                  <div className="space-y-8">
                     {/* Active Services */}
                     <div>
                        <div className="flex items-center gap-2 mb-4">
                           <h2 className="text-xl font-semibold">Active</h2>
                           {reorderServicesMutation.isPending && (
                              <Loader2 className="w-4 h-4 animate-spin text-muted-foreground" />
                           )}
                        </div>
                        <SortableContext
                           items={services
                              .filter((s) => s.isActive)
                              .map((s) => s._id?.toString() || "")}
                           strategy={rectSortingStrategy}
                        >
                           <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                              {services
                                 .filter((service) => service.isActive)
                                 .map((service, index, activeServices) => (
                                    <DraggableServiceCard
                                       key={service._id?.toString()}
                                       service={service}
                                       isActive={true}
                                       onDelete={(service) => {
                                          setDeleteDialog({
                                             open: true,
                                             serviceId:
                                                service._id?.toString() || "",
                                             serviceName: service.name,
                                             imageCount:
                                                service.imageCount || 0,
                                          });
                                       }}
                                       onToggleActive={handleToggleActive}
                                       onMoveLeft={handleMoveLeft}
                                       onMoveRight={handleMoveRight}
                                       canMoveLeft={index > 0}
                                       canMoveRight={
                                          index < activeServices.length - 1
                                       }
                                       dropdownOpen={
                                          dropdownOpen[
                                             service._id?.toString() || ""
                                          ] || false
                                       }
                                       onDropdownOpenChange={(open) =>
                                          setDropdownOpen((prev) => ({
                                             ...prev,
                                             [service._id?.toString() || ""]:
                                                open,
                                          }))
                                       }
                                    />
                                 ))}
                           </div>
                        </SortableContext>
                        {services.filter((s) => s.isActive).length === 0 && (
                           <div className="text-center py-8 text-muted-foreground">
                              No active services yet. Move services from
                              inactive or create new ones.
                           </div>
                        )}
                     </div>

                     {/* Inactive Services */}
                     <div>
                        <div className="flex items-center gap-2 mb-4">
                           <h2 className="text-xl font-semibold text-gray-400">
                              Inactive
                           </h2>
                           {updateServiceMutation.isPending && (
                              <Loader2 className="w-4 h-4 animate-spin text-muted-foreground" />
                           )}
                        </div>
                        <SortableContext
                           items={services
                              .filter((s) => !s.isActive)
                              .map((s) => s._id?.toString() || "")}
                           strategy={rectSortingStrategy}
                        >
                           <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                              {services
                                 .filter((service) => !service.isActive)
                                 .map((service, index, inactiveServices) => (
                                    <DraggableServiceCard
                                       key={service._id?.toString()}
                                       service={service}
                                       isActive={false}
                                       onDelete={(service) => {
                                          setDeleteDialog({
                                             open: true,
                                             serviceId:
                                                service._id?.toString() || "",
                                             serviceName: service.name,
                                             imageCount:
                                                service.imageCount || 0,
                                          });
                                       }}
                                       onToggleActive={handleToggleActive}
                                       onMoveLeft={handleMoveLeft}
                                       onMoveRight={handleMoveRight}
                                       canMoveLeft={index > 0}
                                       canMoveRight={
                                          index < inactiveServices.length - 1
                                       }
                                       dropdownOpen={
                                          dropdownOpen[
                                             service._id?.toString() || ""
                                          ] || false
                                       }
                                       onDropdownOpenChange={(open) =>
                                          setDropdownOpen((prev) => ({
                                             ...prev,
                                             [service._id?.toString() || ""]:
                                                open,
                                          }))
                                       }
                                    />
                                 ))}
                           </div>
                        </SortableContext>
                        {services.filter((s) => !s.isActive).length === 0 && (
                           <div className="text-center py-8 text-muted-foreground">
                              No inactive services.
                           </div>
                        )}
                     </div>
                  </div>

                  <DragOverlay>
                     {activeId ? (
                        <div className="opacity-90">
                           <DraggableServiceCard
                              service={
                                 services.find(
                                    (s) => s._id?.toString() === activeId
                                 )!
                              }
                              isActive={
                                 services.find(
                                    (s) => s._id?.toString() === activeId
                                 )?.isActive || false
                              }
                              onDelete={() => {}}
                              onToggleActive={() => {}}
                              onMoveLeft={() => {}}
                              onMoveRight={() => {}}
                              canMoveLeft={false}
                              canMoveRight={false}
                              dropdownOpen={false}
                              onDropdownOpenChange={() => {}}
                           />
                        </div>
                     ) : null}
                  </DragOverlay>
               </DndContext>
            ) : (
               <Card className="border-border/30">
                  <CardHeader>
                     <CardTitle className="text-foreground">
                        Portfolio Management
                     </CardTitle>
                     <CardDescription>
                        Create and manage portfolio services to organize your
                        work
                     </CardDescription>
                  </CardHeader>
                  <CardContent>
                     <div className="flex items-center justify-center h-64 text-muted-foreground">
                        <div className="text-center">
                           <Briefcase className="w-16 h-16 mx-auto mb-4 opacity-50" />
                           <h3 className="text-lg font-medium mb-2">
                              No portfolio services yet
                           </h3>
                           <p className="text-sm mb-4">
                              Create your first portfolio service to start
                              organizing your work
                           </p>
                           <CreatePortfolioServiceDialog />
                        </div>
                     </div>
                  </CardContent>
               </Card>
            )}

            {/* Delete Service Dialog */}
            <AlertDialog
               open={deleteDialog.open}
               onOpenChange={(open) => {
                  if (!open) {
                     setDeleteDialog({
                        open: false,
                        serviceId: "",
                        serviceName: "",
                        imageCount: 0,
                     });
                     deleteServiceMutation.reset();
                  } else {
                     setDeleteDialog((prev) => ({ ...prev, open: true }));
                  }
               }}
            >
               <AlertDialogContent>
                  <AlertDialogHeader>
                     <AlertDialogTitle>
                        Delete Portfolio Service
                     </AlertDialogTitle>
                     <AlertDialogDescription className="space-y-3">
                        <span className="space-y-1 block">
                           <span className="block">
                              Are you sure you want to delete the portfolio
                              service{" "}
                              <span className="font-semibold">
                                 {deleteDialog.serviceName}
                              </span>
                              ?
                           </span>
                           {deleteDialog.imageCount > 0 && (
                              <span className="block">
                                 This service contains {deleteDialog.imageCount}{" "}
                                 images that will also be deleted.
                              </span>
                           )}
                           <span className="block font-bold text-destructive">
                              This action cannot be undone.
                           </span>
                        </span>
                     </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                     <AlertDialogCancel
                        onClick={() => {
                           setDeleteDialog({
                              open: false,
                              serviceId: "",
                              serviceName: "",
                              imageCount: 0,
                           });
                           deleteServiceMutation.reset();
                        }}
                        disabled={deleteServiceMutation.isPending}
                     >
                        Cancel
                     </AlertDialogCancel>
                     <button
                        type="button"
                        className={buttonVariants({
                           className: "bg-destructive text-white",
                        })}
                        disabled={deleteServiceMutation.isPending}
                        onClick={handleDeleteService}
                     >
                        {deleteServiceMutation.isPending ? (
                           <>
                              <Loader2 className="w-4 h-4 animate-spin" />
                              Deleting...
                           </>
                        ) : (
                           "Delete"
                        )}
                     </button>
                  </AlertDialogFooter>
               </AlertDialogContent>
            </AlertDialog>
         </div>
      </PortfolioErrorBoundary>
   );
}
