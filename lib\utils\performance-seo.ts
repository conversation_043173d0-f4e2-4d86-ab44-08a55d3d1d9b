/**
 * Performance SEO Utilities
 * Helpers for optimizing Core Web Vitals and page speed for better SEO
 */

/**
 * Image optimization settings for different use cases
 */
export const imageOptimization = {
   hero: {
      sizes: "(max-width: 640px) 100vw, (max-width: 1024px) 100vw, 100vw",
      quality: 85,
      priority: true,
      placeholder: "blur" as const,
   },

   gallery: {
      sizes: "(max-width: 640px) 50vw, (max-width: 1024px) 33vw, 25vw",
      quality: 80,
      priority: false,
      loading: "lazy" as const,
   },

   thumbnail: {
      sizes: "(max-width: 640px) 25vw, (max-width: 1024px) 20vw, 15vw",
      quality: 75,
      priority: false,
      loading: "lazy" as const,
   },

   avatar: {
      sizes: "64px",
      quality: 85,
      priority: false,
      loading: "lazy" as const,
   },
};

/**
 * Critical resource preloading configuration
 */
export const criticalResources = {
   fonts: [
      {
         href: "https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap",
         as: "style" as const,
         crossOrigin: "anonymous" as const,
      },
   ],

   images: ["/images/hero.JPG", "/images/astral-logo.svg"],

   scripts: [
      // Add critical third-party scripts here
   ],
};

/**
 * Generate optimized loading strategy for images
 */
export function getImageLoadingStrategy(
   imageType: keyof typeof imageOptimization,
   isAboveFold: boolean = false
) {
   const config = imageOptimization[imageType];

   return {
      ...config,
      priority: isAboveFold || config.priority,
      loading: isAboveFold
         ? ("eager" as const)
         : "loading" in config
         ? config.loading
         : ("lazy" as const),
   };
}

/**
 * Lazy loading configuration for different content types
 */
export const lazyLoadingConfig = {
   images: {
      rootMargin: "50px 0px",
      threshold: 0.1,
   },

   components: {
      rootMargin: "100px 0px",
      threshold: 0.1,
   },

   videos: {
      rootMargin: "200px 0px",
      threshold: 0.1,
   },
};

/**
 * Generate resource hints for better performance
 */
export function generateResourceHints() {
   return {
      preconnect: ["https://fonts.googleapis.com", "https://fonts.gstatic.com"],

      dnsPrefetch: ["//www.google-analytics.com", "//www.googletagmanager.com"],

      preload: criticalResources.fonts.map((font) => ({
         href: font.href,
         as: font.as,
         crossOrigin: font.crossOrigin,
      })),
   };
}

/**
 * Core Web Vitals optimization helpers
 */
export const coreWebVitals = {
   // Largest Contentful Paint (LCP) optimization
   optimizeLCP: {
      // Preload hero images
      preloadHeroImage: true,
      // Use appropriate image formats
      useModernFormats: true,
      // Optimize server response time
      serverResponseTime: "<200ms",
   },

   // First Input Delay (FID) optimization
   optimizeFID: {
      // Minimize JavaScript execution time
      splitCodeBundles: true,
      // Use web workers for heavy tasks
      useWebWorkers: true,
      // Defer non-critical JavaScript
      deferNonCriticalJS: true,
   },

   // Cumulative Layout Shift (CLS) optimization
   optimizeCLS: {
      // Set dimensions for images and videos
      setImageDimensions: true,
      // Reserve space for dynamic content
      reserveSpaceForAds: true,
      // Use CSS aspect-ratio
      useCSSAspectRatio: true,
   },
};

/**
 * Generate performance budget recommendations
 */
export const performanceBudget = {
   // Total page weight targets
   totalPageWeight: {
      mobile: "1.5MB",
      desktop: "2MB",
   },

   // Resource-specific budgets
   resources: {
      images: "800KB",
      javascript: "300KB",
      css: "100KB",
      fonts: "150KB",
   },

   // Performance metrics targets
   metrics: {
      LCP: "2.5s",
      FID: "100ms",
      CLS: "0.1",
      TTFB: "200ms",
      FCP: "1.8s",
   },
};

/**
 * Mobile-first optimization settings
 */
export const mobileOptimization = {
   // Viewport configuration
   viewport: {
      width: "device-width",
      initialScale: 1,
      shrinkToFit: "no",
      viewportFit: "cover",
   },

   // Touch optimization
   touch: {
      minTouchTarget: "44px",
      touchAction: "manipulation",
      userSelect: "none", // for buttons
   },

   // Network optimization
   network: {
      // Reduce requests on slow connections
      reduceRequestsOn2G: true,
      // Use service worker for caching
      useServiceWorker: true,
      // Implement offline functionality
      offlineSupport: true,
   },
};

/**
 * Generate critical CSS extraction configuration
 */
export function getCriticalCSSConfig(route: string) {
   const criticalSelectors = {
      "/": [
         "header",
         "nav",
         ".hero",
         ".intro",
         "h1",
         "h2",
         ".btn-primary",
         ".container",
      ],
      "/services": [
         "header",
         "nav",
         ".services-grid",
         ".service-card",
         "h1",
         "h2",
         ".btn-primary",
         ".container",
      ],
      "/portfolio": [
         "header",
         "nav",
         ".portfolio-grid",
         ".portfolio-item",
         "h1",
         "h2",
         ".btn-primary",
         ".container",
      ],
      "/contact": [
         "header",
         "nav",
         ".contact-form",
         ".form-field",
         "h1",
         "h2",
         ".btn-primary",
         ".container",
      ],
   };

   return {
      selectors:
         criticalSelectors[route as keyof typeof criticalSelectors] ||
         criticalSelectors["/"],
      minify: true,
      extract: true,
      inlineThreshold: 10000, // 10KB
   };
}

/**
 * SEO-friendly loading states
 */
export const loadingStates = {
   // Skeleton loaders that maintain layout
   skeleton: {
      useSkeletonLoaders: true,
      maintainAspectRatio: true,
      showProgressIndicators: true,
   },

   // Error states that don't break SEO
   error: {
      showFallbackContent: true,
      maintainPageStructure: true,
      provideRetryMechanism: true,
   },

   // Loading indicators
   indicators: {
      useProgressBars: true,
      showLoadingText: true,
      maintainAccessibility: true,
   },
};
