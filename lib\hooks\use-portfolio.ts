"use client";

import {
   addExistingImagesToPortfolioService,
   bulkDeletePortfolioImagesAction,
   bulkMovePortfolioImagesToServiceAction,
   createPortfolioServiceAction,
   deletePortfolioImageAction,
   deletePortfolioServiceAction,
   generatePortfolioUploadUrl,
   getPortfolioServiceByIdAction,
   getPortfolioServicesAction,
   movePortfolioImageToServiceAction,
   reorderPortfolioImagesAction,
   reorderPortfolioServicesAction,
   savePortfolioImageMetadata,
   updatePortfolioImageAction,
   updatePortfolioServiceAction,
} from "@/lib/actions/portfolio-actions";
import { PaginationOptions } from "@/lib/models";
import {
   getActivePortfolioServices,
   getAllActivePortfolioServices,
   getAllPortfolioImages,
   getPortfolioImages,
   getPortfolioImagesByServiceId,
   getPortfolioServiceStats,
   searchPortfolioImages,
   searchPortfolioServices,
} from "@/lib/services/portfolio-service";
import {
   categorizeError,
   isRetryableError,
} from "@/lib/utils/portfolio-error-handling";
import { ToastManager } from "@/lib/utils/toast-notifications";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useMemo } from "react";
import { toast } from "sonner";
import { invalidationKeys, queryKeys } from "./query-keys";

// ===== Portfolio Services Hooks =====

/**
 * Hook to fetch all portfolio services with pagination
 */
export function usePortfolioServices(options: PaginationOptions = {}) {
   return useQuery({
      queryKey: queryKeys.portfolioServices.list(options),
      queryFn: async () => {
         const result = await getPortfolioServicesAction();
         if (!result.success) {
            throw new Error(result.error);
         }
         return result.data || [];
      },
      staleTime: 5 * 60 * 1000, // 5 minutes
   });
}

/**
 * Hook to fetch active portfolio services only (for public display)
 */
export function useActivePortfolioServices(options: PaginationOptions = {}) {
   return useQuery({
      queryKey: queryKeys.portfolioServices.active(options),
      queryFn: () => getActivePortfolioServices(options),
      staleTime: 5 * 60 * 1000, // 5 minutes
   });
}

/**
 * Hook to fetch active portfolio services with their cover images (first image of each service)
 */
export function useActivePortfolioServicesWithCoverImages(
   options: PaginationOptions = {}
) {
   // Check if pagination options are provided - if not, use the non-paginated version
   const hasPaginationOptions = Object.keys(options).length > 0;

   return useQuery({
      queryKey: [
         "portfolio-services",
         "active-with-cover-images",
         hasPaginationOptions ? options : "all",
      ],
      queryFn: async () => {
         // First, get all active portfolio services
         const services = hasPaginationOptions
            ? await getActivePortfolioServices(options).then(
                 (result) => result.data
              )
            : await getAllActivePortfolioServices();

         // Process each service to set coverImageUrl (if not already set)
         const servicesWithCoverImages = await Promise.all(
            services.map(async (service) => {
               // If service already has a coverImageUrl, keep it as is
               if (service.coverImageUrl) {
                  return service;
               }

               // If no coverImageUrl, query for the first image in this service
               try {
                  const serviceImages = await getAllPortfolioImages({
                     serviceId: service._id?.toString(),
                     sortBy: "createdAt",
                     sortOrder: "asc",
                  });

                  // Set coverImageUrl to the first image's URL if it exists, otherwise null
                  const coverImageUrl =
                     serviceImages.length > 0 ? serviceImages[0].url : null;

                  return {
                     ...service,
                     coverImageUrl,
                  };
               } catch (error) {
                  console.error(
                     `Error fetching images for service ${service._id}:`,
                     error
                  );
                  return service;
               }
            })
         );

         return servicesWithCoverImages;
      },
      staleTime: 5 * 60 * 1000, // 5 minutes
   });
}

/**
 * Hook to fetch a single portfolio service by ID
 */
export function usePortfolioService(id: string) {
   return useQuery({
      queryKey: queryKeys.portfolioServices.detail(id),
      queryFn: async () => {
         const result = await getPortfolioServiceByIdAction(id);
         if (!result.success) {
            throw new Error(result.error);
         }
         return result.data;
      },
      enabled: !!id,
      staleTime: 10 * 60 * 1000, // 10 minutes
   });
}

/**
 * Hook to search portfolio services by name
 */
export function useSearchPortfolioServices(
   query: string,
   options: PaginationOptions = {}
) {
   return useQuery({
      queryKey: queryKeys.portfolioServices.search(query, options),
      queryFn: () => searchPortfolioServices(query, options),
      enabled: !!query.trim(),
      staleTime: 2 * 60 * 1000, // 2 minutes
   });
}

/**
 * Hook to create a new portfolio service
 */
export function useCreatePortfolioService() {
   const queryClient = useQueryClient();
   const toastManager = useMemo(() => new ToastManager(), []);

   return useMutation({
      mutationFn: createPortfolioServiceAction,
      onMutate: () => {
         // Show loading toast
         toastManager.startLoading(
            "create-service",
            "Creating portfolio service..."
         );
      },
      onSuccess: (result, variables) => {
         if (result.success && result.data) {
            // Invalidate portfolio services list
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.portfolioServicesList(),
            });

            // Extract service name from form data
            const serviceName = (variables.get("name") as string) || "service";
            toastManager.success(
               "create-service",
               `Portfolio service "${serviceName}" created successfully`
            );
         } else {
            const error = categorizeError(
               new Error(result.error || "Failed to create portfolio service")
            );
            toastManager.error("create-service", error, {
               onRetry: isRetryableError(error)
                  ? async () => {
                       // Retry the mutation
                       await createPortfolioServiceAction(variables);
                    }
                  : undefined,
            });
         }
      },
      onError: (error, variables) => {
         const portfolioError = categorizeError(error);
         toastManager.error("create-service", portfolioError, {
            onRetry: portfolioError.retryable
               ? async () => {
                    // Retry the mutation
                    await createPortfolioServiceAction(variables);
                 }
               : undefined,
         });
      },
      retry: (failureCount, error) => {
         const portfolioError = categorizeError(error);
         return portfolioError.retryable && failureCount < 2;
      },
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
   });
}

/**
 * Hook to update a portfolio service
 */
export function useUpdatePortfolioService() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: ({ id, formData }: { id: string; formData: FormData }) =>
         updatePortfolioServiceAction(id, formData),
      onSuccess: (result, variables) => {
         if (result.success && result.data) {
            // Update the specific service in cache
            queryClient.setQueryData(
               queryKeys.portfolioServices.detail(variables.id),
               result.data
            );

            // Invalidate lists to ensure consistency
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.portfolioServicesList(),
            });

            toast.success(
               result.message || "Portfolio service updated successfully"
            );
         } else {
            toast.error(result.error || "Failed to update portfolio service");
         }
      },
      onError: (error) => {
         toast.error(
            error instanceof Error
               ? error.message
               : "Failed to update portfolio service"
         );
      },
   });
}

/**
 * Hook to delete a portfolio service
 */
export function useDeletePortfolioService() {
   const queryClient = useQueryClient();
   const toastManager = useMemo(() => new ToastManager(), []);

   return useMutation({
      mutationFn: deletePortfolioServiceAction,
      onMutate: () => {
         toastManager.startLoading(
            "delete-service",
            "Deleting portfolio service..."
         );
      },
      onSuccess: (result, serviceId) => {
         if (result.success) {
            // Invalidate all portfolio service queries
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.allPortfolioServices(),
            });
            // Also invalidate portfolio images since they might be affected
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.allPortfolioImages(),
            });

            toastManager.success(
               "delete-service",
               "Portfolio service deleted successfully"
            );
         } else {
            const error = categorizeError(
               new Error(result.error || "Failed to delete portfolio service")
            );
            toastManager.error("delete-service", error, {
               onRetry: error.retryable
                  ? async () => {
                       await deletePortfolioServiceAction(serviceId);
                    }
                  : undefined,
            });
         }
      },
      onError: (error, serviceId) => {
         const portfolioError = categorizeError(error);
         toastManager.error("delete-service", portfolioError, {
            onRetry: portfolioError.retryable
               ? async () => {
                    await deletePortfolioServiceAction(serviceId);
                 }
               : undefined,
         });
      },
      retry: (failureCount, error) => {
         const portfolioError = categorizeError(error);
         return portfolioError.retryable && failureCount < 2;
      },
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
   });
}

// ===== Portfolio Images Hooks =====

/**
 * Hook to fetch portfolio images with pagination and filtering
 */
export function usePortfolioImages(
   options: PaginationOptions & {
      serviceId?: string;
   } = {}
) {
   return useQuery({
      queryKey: queryKeys.portfolioImages.list(options),
      queryFn: () => getPortfolioImages(options),
      staleTime: 5 * 60 * 1000, // 5 minutes
   });
}

/**
 * Hook to fetch portfolio images by service ID
 */
export function usePortfolioImagesByService(
   serviceId: string,
   options: PaginationOptions = {}
) {
   return useQuery({
      queryKey: queryKeys.portfolioImages.byService(serviceId, options),
      queryFn: () => getPortfolioImagesByServiceId(serviceId, options),
      enabled: !!serviceId,
      staleTime: 5 * 60 * 1000, // 5 minutes
   });
}

/**
 * Hook to search portfolio images
 */
export function useSearchPortfolioImages(
   query: string,
   options: PaginationOptions & { serviceId?: string } = {}
) {
   return useQuery({
      queryKey: queryKeys.portfolioImages.search(query, options),
      queryFn: () => searchPortfolioImages(query, options),
      enabled: !!query && query.length > 0,
      staleTime: 2 * 60 * 1000, // 2 minutes
   });
}

/**
 * Hook to update portfolio image metadata
 */
export function useUpdatePortfolioImage() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: ({ id, formData }: { id: string; formData: FormData }) =>
         updatePortfolioImageAction(id, formData),
      onSuccess: (result, variables) => {
         if (result.success && result.data) {
            // Update the specific image in cache
            queryClient.setQueryData(
               queryKeys.portfolioImages.detail(variables.id),
               result.data
            );

            // Invalidate lists to ensure consistency
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.portfolioImagesList(),
            });

            toast.success(
               result.message || "Portfolio image updated successfully"
            );
         } else {
            toast.error(result.error || "Failed to update portfolio image");
         }
      },
      onError: (error) => {
         toast.error(
            error instanceof Error
               ? error.message
               : "Failed to update portfolio image"
         );
      },
   });
}

/**
 * Hook to delete a portfolio image
 */
export function useDeletePortfolioImage() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: deletePortfolioImageAction,
      onSuccess: (result) => {
         if (result.success) {
            // Invalidate all portfolio image queries
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.allPortfolioImages(),
            });
            // Also invalidate portfolio services since image counts might change
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.portfolioServicesList(),
            });
            toast.success(
               result.message || "Portfolio image deleted successfully"
            );
         } else {
            toast.error(result.error || "Failed to delete portfolio image");
         }
      },
      onError: (error) => {
         toast.error(
            error instanceof Error
               ? error.message
               : "Failed to delete portfolio image"
         );
      },
   });
}

/**
 * Hook to move portfolio image to different service
 */
export function useMovePortfolioImageToService() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: ({
         imageId,
         serviceId,
      }: {
         imageId: string;
         serviceId: string;
      }) => movePortfolioImageToServiceAction(imageId, serviceId),
      onSuccess: (result, variables) => {
         if (result.success && result.data) {
            // Update the specific image in cache
            queryClient.setQueryData(
               queryKeys.portfolioImages.detail(variables.imageId),
               result.data
            );

            // Invalidate all portfolio image lists and service data
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.allPortfolioImages(),
            });
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.portfolioServicesList(),
            });

            toast.success(
               result.message || "Portfolio image moved successfully"
            );
         } else {
            toast.error(result.error || "Failed to move portfolio image");
         }
      },
      onError: (error) => {
         toast.error(
            error instanceof Error
               ? error.message
               : "Failed to move portfolio image"
         );
      },
   });
}

/**
 * Hook to add existing images to portfolio service
 */
export function useAddExistingImagesToPortfolioService() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: addExistingImagesToPortfolioService,
      onSuccess: (result) => {
         if (result.success) {
            // Invalidate all portfolio image lists and service data
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.allPortfolioImages(),
            });
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.portfolioServicesList(),
            });

            toast.success(
               result.message ||
                  "Images added to portfolio service successfully"
            );
         } else {
            toast.error(
               result.error || "Failed to add images to portfolio service"
            );
         }
      },
      onError: (error) => {
         toast.error(
            error instanceof Error
               ? error.message
               : "Failed to add images to portfolio service"
         );
      },
   });
}

/**
 * Hook to bulk move portfolio images to service
 */
export function useBulkMovePortfolioImagesToService() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: bulkMovePortfolioImagesToServiceAction,
      onSuccess: (result) => {
         if (result.success) {
            // Invalidate all portfolio image lists and service data
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.allPortfolioImages(),
            });
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.portfolioServicesList(),
            });

            toast.success(
               result.message || "Portfolio images moved successfully"
            );
         } else {
            toast.error(result.error || "Failed to move portfolio images");
         }
      },
      onError: (error) => {
         toast.error(
            error instanceof Error
               ? error.message
               : "Failed to move portfolio images"
         );
      },
   });
}

/**
 * Hook to bulk delete portfolio images
 */
export function useBulkDeletePortfolioImages() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: bulkDeletePortfolioImagesAction,
      onSuccess: (result) => {
         if (result.success) {
            // Invalidate all portfolio image queries
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.allPortfolioImages(),
            });
            // Also invalidate portfolio services since image counts might change
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.portfolioServicesList(),
            });

            toast.success(
               result.message || "Portfolio images deleted successfully"
            );
         } else {
            toast.error(result.error || "Failed to delete portfolio images");
         }
      },
      onError: (error) => {
         toast.error(
            error instanceof Error
               ? error.message
               : "Failed to delete portfolio images"
         );
      },
   });
}

/**
 * Hook to reorder portfolio images within a service
 */
export function useReorderPortfolioImages() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: reorderPortfolioImagesAction,
      onSuccess: (result) => {
         if (result.success) {
            // Invalidate all portfolio image queries to refresh order
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.allPortfolioImages(),
            });
         } else {
            toast.error(result.error || "Failed to reorder portfolio images");
         }
      },
      onError: (error) => {
         toast.error(
            error instanceof Error
               ? error.message
               : "Failed to reorder portfolio images"
         );
      },
   });
}

/**
 * Hook to reorder portfolio services
 */
export function useReorderPortfolioServices() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: reorderPortfolioServicesAction,
      onSuccess: (result) => {
         if (result.success) {
            // Invalidate all portfolio service queries to refresh order
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.portfolioServicesList(),
            });
         } else {
            toast.error(result.error || "Failed to reorder portfolio services");
         }
      },
      onError: (error) => {
         toast.error(
            error instanceof Error
               ? error.message
               : "Failed to reorder portfolio services"
         );
      },
   });
}

/**
 * Convenience hook for bulk move operations
 */
export function useBulkMovePortfolioImages() {
   return useBulkMovePortfolioImagesToService();
}

// ===== Portfolio Service Statistics Hook =====

/**
 * Hook to fetch portfolio service statistics
 */
export function usePortfolioServiceStats(serviceId: string) {
   return useQuery({
      queryKey: [...queryKeys.portfolioServices.detail(serviceId), "stats"],
      queryFn: () => getPortfolioServiceStats(serviceId),
      enabled: !!serviceId,
      staleTime: 5 * 60 * 1000, // 5 minutes
   });
}

/**
 * Hook to fetch recent portfolio images for dashboard
 */
export function useRecentPortfolioImages(limit: number = 6) {
   return useQuery({
      queryKey: queryKeys.portfolioImages.recent(limit),
      queryFn: () =>
         getPortfolioImages({
            limit,
            sortBy: "createdAt",
            sortOrder: "desc",
         }),
      staleTime: 2 * 60 * 1000, // 2 minutes
   });
}

// ===== Portfolio Upload Hooks =====

/**
 * Hook to generate portfolio upload URL
 */
export function useGeneratePortfolioUploadUrl() {
   return useMutation({
      mutationFn: ({
         filename,
         contentType,
         fileSize,
      }: {
         filename: string;
         contentType: string;
         fileSize: number;
      }) => generatePortfolioUploadUrl(filename, contentType, fileSize),
      onError: (error) => {
         toast.error(
            error instanceof Error
               ? error.message
               : "Failed to generate upload URL"
         );
      },
   });
}

/**
 * Hook to save portfolio image metadata after upload
 */
export function useSavePortfolioImageMetadata() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: ({
         key,
         filename,
         contentType,
         fileSize,
         width,
         height,
         serviceId,
      }: {
         key: string;
         filename: string;
         contentType: string;
         fileSize: number;
         width: number;
         height: number;
         serviceId: string;
      }) =>
         savePortfolioImageMetadata(
            key,
            filename,
            contentType,
            fileSize,
            width,
            height,
            serviceId
         ),
      onSuccess: (result) => {
         if (result.success) {
            // Invalidate portfolio image queries
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.allPortfolioImages(),
            });
            // Also invalidate portfolio services since image counts might change
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.portfolioServicesList(),
            });
         } else {
            toast.error(result.error || "Failed to save image metadata");
         }
      },
      onError: (error) => {
         toast.error(
            error instanceof Error
               ? error.message
               : "Failed to save image metadata"
         );
      },
   });
}
