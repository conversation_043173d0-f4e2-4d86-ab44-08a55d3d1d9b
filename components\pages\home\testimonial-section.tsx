import TextReveal from "@/components/animations/text-reveal";
import { testimonials } from "@/data";
import { Quote } from "lucide-react";
import { motion } from "motion/react";

export default function TestimonialSection() {
   return (
      <>
         <section className="py-20 bg-astral-grey">
            <div className="container mx-auto px-4">
               <div className="text-center mb-16">
                  <TextReveal>
                     <h2 className="text-4xl md:text-5xl font-playfair font-bold text-foreground mb-6">
                        Love from{" "}
                        <span className="bg-gradient-accent bg-clip-text text-transparent">
                           Our Clients
                        </span>
                     </h2>
                  </TextReveal>
                  <TextReveal>
                     <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                        Don&apos;t just take our word for it. Here&apos;s what
                        our happy clients have to say about their experience
                        with Astral Studios.
                     </p>
                  </TextReveal>
               </div>

               <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {testimonials.map((testimonial, index) => (
                     <motion.div
                        key={index}
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: index * 0.1 + 0.4 }}
                        viewport={{
                           once: true,
                        }}
                        className="bg-card border border-astral-grey-light rounded-2xl p-8 shadow-card"
                     >
                        <div className="flex items-center space-x-1 mb-4">
                           {[...Array(5)].map((_, i) => (
                              <span key={i} className="text-yellow-400 text-xl">
                                 ★
                              </span>
                           ))}
                        </div>
                        <div className="relative">
                           <Quote className="h-10 w-10 text-astral-grey-light mb-2 absolute -top-5 -right-5" />
                           <p className="text-muted-foreground font-montserrat mb-4 italic z-10 relative">
                              &quot;{testimonial.text}&quot;
                           </p>
                        </div>
                        <div className="flex items-center space-x-3">
                           <div className="w-12 h-12 bg-gradient-accent rounded-full flex items-center justify-center">
                              <span className="text-white font-playfair font-semibold">
                                 {testimonial.initials}{" "}
                              </span>
                           </div>
                           <div>
                              <p className="font-playfair font-semibold text-foreground">
                                 {testimonial.name}
                              </p>
                              <p className="text-sm text-muted-foreground font-montserrat">
                                 {testimonial.service}
                              </p>
                           </div>
                        </div>
                     </motion.div>
                  ))}
               </div>
            </div>
         </section>
      </>
   );
}
