import ElementReveal from "@/components/animations/element-reveal";
import StaggerReveal from "@/components/animations/stagger-reveal";
import TextReveal from "@/components/animations/text-reveal";
import { Button } from "@/components/ui/button";
import {
   Card,
   CardContent,
   CardDescription,
   CardHeader,
   CardTitle,
} from "@/components/ui/card";
import { ArrowRight, Camera, Settings, Shield, Snowflake } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

const features = [
   {
      icon: Snowflake,
      title: "Professional Equipment",
      description:
         "High-quality dry ice machines that produce consistent, safe, and dramatic fog effects for any event.",
   },
   {
      icon: Camera,
      title: "Photography Perfect",
      description:
         "Creates stunning atmospheric effects that enhance photography and videography with magical ambiance.",
   },
   {
      icon: Shield,
      title: "Safe Operation",
      description:
         "All equipment includes safety features and we provide comprehensive safety guidelines and equipment.",
   },
   {
      icon: Settings,
      title: "Customizable Effects",
      description:
         "Various effect options from subtle ambiance to dramatic reveals, perfectly timed for your event.",
   },
];

const applications = [
   {
      title: "Wedding Photography",
      description:
         "Create romantic, dreamy atmospheres for wedding photos with low-lying fog effects that add magic to your shots.",
   },
   {
      title: "First Dance",
      description:
         "Transform your first dance into a fairytale moment with beautiful fog effects that create an intimate atmosphere.",
   },
   {
      title: "Event Entrances",
      description:
         "Make dramatic entrances at corporate events, parties, or celebrations with impressive fog effects.",
   },
   {
      title: "Photography Sessions",
      description:
         "Enhance portrait and fashion photography sessions with atmospheric effects that add depth and drama.",
   },
   {
      title: "Stage Performances",
      description:
         "Perfect for theatrical performances, concerts, and presentations requiring atmospheric enhancement.",
   },
   {
      title: "Product Launches",
      description:
         "Create memorable product reveals and launches with dramatic fog effects that captivate audiences.",
   },
];

export default function DryIcePage() {
   return (
      <div className="min-h-screen">
         {/* Hero Section */}
         <section className="relative !pt-38 pb-16">
            <div className="absolute inset-0 z-0">
               <Image
                  src="/images/dry-ice-machine.jpg"
                  alt="Dry ice machine rental hero"
                  fill
                  className="object-cover"
                  priority
                  sizes="100vw"
               />
               <div className="absolute inset-0 bg-black/50" />
            </div>

            <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
               <TextReveal>
                  <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
                     Dry Ice Machine Rental
                  </h1>
               </TextReveal>
               <TextReveal className="mb-8">
                  <p className="text-lg max-w-3xl mx-auto leading-relaxed">
                     Create magical atmospheric effects for your events and
                     photography sessions with our professional dry ice machines
                  </p>
               </TextReveal>
               <ElementReveal>
                  <div className="flex flex-col sm:flex-row gap-4 items-center justify-center">
                     <Button asChild size="lg">
                        <Link href="/contact">Rent Dry Ice Machine</Link>
                     </Button>
                     <Button
                        asChild
                        variant="outline"
                        size="lg"
                        className="text-white hover:bg-white hover:text-black"
                     >
                        <Link href="/portfolio">See Effects Gallery</Link>
                     </Button>
                  </div>
               </ElementReveal>
            </div>
         </section>

         {/* Features Section */}
         <section className="py-20">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
               <div className="text-center mb-16">
                  <TextReveal>
                     <h2 className="text-3xl md:text-4xl font-bold mb-4">
                        Why Choose Our Dry Ice Machines
                     </h2>
                  </TextReveal>
                  <TextReveal>
                     <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                        Our professional-grade dry ice machines provide safe,
                        reliable, and stunning atmospheric effects for any
                        occasion.
                     </p>
                  </TextReveal>
               </div>

               <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                  {features.map((feature, index) => (
                     <StaggerReveal key={feature.title} index={index}>
                        <Card className="text-center">
                           <CardHeader>
                              <div className="flex justify-center mb-4">
                                 <feature.icon className="h-12 w-12 text-primary" />
                              </div>
                              <CardTitle>{feature.title}</CardTitle>
                           </CardHeader>
                           <CardContent>
                              <CardDescription className="text-center">
                                 {feature.description}
                              </CardDescription>
                           </CardContent>
                        </Card>
                     </StaggerReveal>
                  ))}
               </div>
            </div>
         </section>

         {/* Applications Section */}
         <section className="py-20 bg-astral-grey">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
               <div className="text-center mb-16">
                  <TextReveal>
                     <h2 className="text-3xl md:text-4xl font-bold mb-4">
                        Perfect Applications
                     </h2>
                  </TextReveal>
                  <TextReveal>
                     <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                        Dry ice effects can transform any event or photography
                        session, creating memorable moments and stunning
                        visuals.
                     </p>
                  </TextReveal>
               </div>

               <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {applications.map((application, index) => (
                     <StaggerReveal key={application.title} index={index}>
                        <Card>
                           <CardHeader>
                              <CardTitle>{application.title}</CardTitle>
                           </CardHeader>
                           <CardContent>
                              <CardDescription>
                                 {application.description}
                              </CardDescription>
                           </CardContent>
                        </Card>
                     </StaggerReveal>
                  ))}
               </div>
            </div>
         </section>

         {/* CTA Section */}
         <section className="py-20 bg-background">
            <div className="container mx-auto px-4 text-center">
               <div className="max-w-3xl mx-auto">
                  <TextReveal>
                     <h2 className="text-4xl font-playfair font-bold text-foreground mb-6">
                        Ready to{" "}
                        <span className="bg-gradient-accent bg-clip-text text-transparent">
                           Create Magical Effects?{" "}
                        </span>
                     </h2>
                  </TextReveal>
                  <TextReveal className="mb-8">
                     <p className="text-lg text-muted-foreground font-montserrat leading-relaxed">
                        Transform your event or photography session with
                        stunning dry ice effects that will leave a lasting
                        impression.
                     </p>
                  </TextReveal>
                  <ElementReveal>
                     <div className="flex flex-col sm:flex-row gap-4 items-center justify-center">
                        <Button
                           asChild
                           size="lg"
                           className="bg-gradient-accent hover:opacity-90 font-montserrat font-semibold text-lg px-8"
                        >
                           <Link href="/contact">
                              Contact Now{" "}
                              <ArrowRight className="ml-2 h-5 w-5" />
                           </Link>
                        </Button>
                     </div>
                  </ElementReveal>
               </div>
            </div>
         </section>
      </div>
   );
}
