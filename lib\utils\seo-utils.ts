/**
 * SEO Utility Functions
 * Provides helper functions for SEO optimization
 */

/**
 * Optimize title for SEO
 * - Keeps title under 60 characters for optimal display
 * - Ensures brand consistency
 * - Includes primary keywords
 */
export function optimizeTitle(
   title: string,
   includeBrand: boolean = true
): string {
   const brand = "Astral Studios";
   const maxLength = 60;

   if (!includeBrand) {
      return title.length > maxLength
         ? title.substring(0, maxLength - 3) + "..."
         : title;
   }

   const titleWithBrand = `${title} | ${brand}`;

   if (titleWithBrand.length <= maxLength) {
      return titleWithBrand;
   }

   // If too long, truncate the title part but keep the brand
   const availableLength = maxLength - brand.length - 3; // 3 for " | "
   const truncatedTitle = title.substring(0, availableLength - 3) + "...";

   return `${truncatedTitle} | ${brand}`;
}

/**
 * Optimize meta description for SEO
 * - Keeps description between 150-160 characters
 * - Includes call-to-action
 * - Incorporates relevant keywords naturally
 */
export function optimizeDescription(description: string, cta?: string): string {
   const maxLength = 160;
   const minLength = 150;

   let optimizedDescription = description;

   // Add CTA if provided and there's space
   if (cta && description.length + cta.length + 1 <= maxLength) {
      optimizedDescription = `${description} ${cta}`;
   }

   // Truncate if too long
   if (optimizedDescription.length > maxLength) {
      optimizedDescription =
         optimizedDescription.substring(0, maxLength - 3) + "...";
   }

   // Warn if too short (for development)
   if (
      process.env.NODE_ENV === "development" &&
      optimizedDescription.length < minLength
   ) {
      console.warn(
         `Meta description might be too short (${optimizedDescription.length} chars): ${optimizedDescription}`
      );
   }

   return optimizedDescription;
}

/**
 * Generate keyword-optimized content
 * - Ensures natural keyword density (1-3%)
 * - Avoids keyword stuffing
 * - Maintains readability
 */
export function optimizeKeywords(
   content: string,
   primaryKeywords: string[],
   maxDensity: number = 0.03
): string {
   const words = content.toLowerCase().split(/\s+/);
   const totalWords = words.length;

   primaryKeywords.forEach((keyword) => {
      const keywordWords = keyword.toLowerCase().split(/\s+/);
      const keywordCount = countKeywordOccurrences(words, keywordWords);
      const currentDensity = keywordCount / totalWords;

      if (process.env.NODE_ENV === "development") {
         if (currentDensity > maxDensity) {
            console.warn(
               `Keyword "${keyword}" density too high: ${(
                  currentDensity * 100
               ).toFixed(2)}%`
            );
         } else if (currentDensity === 0) {
            console.warn(`Primary keyword "${keyword}" not found in content`);
         }
      }
   });

   return content;
}

/**
 * Count keyword occurrences in text
 */
function countKeywordOccurrences(
   words: string[],
   keywordWords: string[]
): number {
   let count = 0;

   for (let i = 0; i <= words.length - keywordWords.length; i++) {
      let match = true;
      for (let j = 0; j < keywordWords.length; j++) {
         if (words[i + j] !== keywordWords[j]) {
            match = false;
            break;
         }
      }
      if (match) count++;
   }

   return count;
}

/**
 * Generate schema.org breadcrumb markup
 */
export function generateBreadcrumbSchema(
   breadcrumbs: Array<{ name: string; url: string }>
) {
   return {
      "@context": "https://schema.org",
      "@type": "BreadcrumbList",
      itemListElement: breadcrumbs.map((crumb, index) => ({
         "@type": "ListItem",
         position: index + 1,
         name: crumb.name,
         item: crumb.url,
      })),
   };
}

/**
 * Generate FAQ schema markup
 */
export function generateFAQSchema(
   faqs: Array<{ question: string; answer: string }>
) {
   return {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      mainEntity: faqs.map((faq) => ({
         "@type": "Question",
         name: faq.question,
         acceptedAnswer: {
            "@type": "Answer",
            text: faq.answer,
         },
      })),
   };
}

/**
 * Generate review/rating schema markup
 */
export function generateReviewSchema(
   reviews: Array<{
      author: string;
      rating: number;
      reviewBody: string;
      datePublished?: string;
   }>
) {
   return reviews.map((review) => ({
      "@context": "https://schema.org",
      "@type": "Review",
      author: {
         "@type": "Person",
         name: review.author,
      },
      reviewRating: {
         "@type": "Rating",
         ratingValue: review.rating,
         bestRating: 5,
      },
      reviewBody: review.reviewBody,
      datePublished: review.datePublished || new Date().toISOString(),
   }));
}

/**
 * Validate and optimize URL structure
 */
export function optimizeURL(url: string): string {
   return url
      .toLowerCase()
      .replace(/[^a-z0-9\-\/]/g, "-")
      .replace(/-+/g, "-")
      .replace(/^-|-$/g, "");
}

/**
 * Generate canonical URL
 */
export function generateCanonicalURL(
   path: string,
   baseUrl: string = "https://www.astralstudios.co.uk"
): string {
   const cleanPath = path.startsWith("/") ? path : `/${path}`;
   return `${baseUrl}${cleanPath}`;
}

/**
 * Check if content meets accessibility standards for SEO
 */
export function validateAccessibility(content: {
   images: Array<{ src: string; alt?: string }>;
   headings: Array<{ level: number; text: string }>;
   links: Array<{ href: string; text: string }>;
}): { isValid: boolean; issues: string[] } {
   const issues: string[] = [];

   // Check image alt texts
   content.images.forEach((img, index) => {
      if (!img.alt || img.alt.trim() === "") {
         issues.push(`Image ${index + 1} missing alt text: ${img.src}`);
      }
   });

   // Check heading hierarchy
   let previousLevel = 0;
   content.headings.forEach((heading, index) => {
      if (index === 0 && heading.level !== 1) {
         issues.push("First heading should be H1");
      }
      if (heading.level > previousLevel + 1) {
         issues.push(
            `Heading level jumps from H${previousLevel} to H${heading.level}: "${heading.text}"`
         );
      }
      previousLevel = heading.level;
   });

   // Check link text
   content.links.forEach((link, index) => {
      if (
         !link.text ||
         link.text.trim() === "" ||
         ["click here", "read more", "here", "more"].includes(
            link.text.toLowerCase().trim()
         )
      ) {
         issues.push(
            `Link ${index + 1} has poor descriptive text: "${link.text}"`
         );
      }
   });

   return {
      isValid: issues.length === 0,
      issues,
   };
}
