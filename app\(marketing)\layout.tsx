import { Footer } from "@/components/footer";
import { Navigation } from "@/components/navigation";
import { generateMetadata as generateSEOMetadata } from "@/components/seo";
import type { Metadata } from "next";

export const metadata: Metadata = generateSEOMetadata({
   page: "home",
});

export default function RootLayout({
   children,
}: Readonly<{
   children: React.ReactNode;
}>) {
   return (
      <div>
         <Navigation />
         <main className="min-h-screen">{children}</main>
         <Footer />
      </div>
   );
}
