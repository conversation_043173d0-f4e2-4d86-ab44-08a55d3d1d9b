/**
 * Enhanced error handling utilities for portfolio operations
 */

export enum PortfolioErrorType {
   VALIDATION_ERROR = "VALIDATION_ERROR",
   NETWORK_ERROR = "NETWORK_ERROR",
   DATABASE_ERROR = "DATABASE_ERROR",
   FILE_UPLOAD_ERROR = "FILE_UPLOAD_ERROR",
   PERMISSION_ERROR = "PERMISSION_ERROR",
   NOT_FOUND_ERROR = "NOT_FOUND_ERROR",
   RATE_LIMIT_ERROR = "RATE_LIMIT_ERROR",
   UNKNOWN_ERROR = "UNKNOWN_ERROR",
}

export interface PortfolioError {
   type: PortfolioErrorType;
   message: string;
   details?: string;
   retryable: boolean;
   retryAfter?: number; // seconds
   code?: string;
}

/**
 * Creates a standardized portfolio error
 */
export function createPortfolioError(
   type: PortfolioErrorType,
   message: string,
   details?: string,
   retryable: boolean = false,
   retryAfter?: number,
   code?: string
): PortfolioError {
   return {
      type,
      message,
      details,
      retryable,
      retryAfter,
      code,
   };
}

/**
 * Determines if an error is retryable based on its characteristics
 */
export function isRetryableError(error: unknown): boolean {
   if (error instanceof Error) {
      // Network errors are usually retryable
      if (
         error.message.includes("fetch") ||
         error.message.includes("network")
      ) {
         return true;
      }

      // Timeout errors are retryable
      if (error.message.includes("timeout")) {
         return true;
      }

      // Rate limit errors are retryable after delay
      if (
         error.message.includes("rate limit") ||
         error.message.includes("429")
      ) {
         return true;
      }

      // Server errors (5xx) are retryable
      if (
         error.message.includes("500") ||
         error.message.includes("502") ||
         error.message.includes("503") ||
         error.message.includes("504")
      ) {
         return true;
      }
   }

   return false;
}

/**
 * Extracts retry delay from error message or headers
 */
export function getRetryDelay(error: unknown): number {
   if (error instanceof Error) {
      // Look for retry-after in error message
      const retryAfterMatch = error.message.match(
         /retry[_\s-]?after[:\s]*(\d+)/i
      );
      if (retryAfterMatch) {
         return parseInt(retryAfterMatch[1], 10);
      }

      // Rate limit errors - exponential backoff
      if (error.message.includes("rate limit")) {
         return 60; // 1 minute default
      }

      // Server errors - shorter delay
      if (error.message.includes("500") || error.message.includes("502")) {
         return 5; // 5 seconds
      }
   }

   return 3; // Default 3 seconds
}

/**
 * Categorizes an error into a portfolio error type
 */
export function categorizeError(error: unknown): PortfolioError {
   if (error instanceof Error) {
      const message = error.message.toLowerCase();

      // Validation errors
      if (
         message.includes("validation") ||
         message.includes("invalid") ||
         message.includes("required") ||
         message.includes("format")
      ) {
         return createPortfolioError(
            PortfolioErrorType.VALIDATION_ERROR,
            error.message,
            undefined,
            false
         );
      }

      // Network errors
      if (
         message.includes("fetch") ||
         message.includes("network") ||
         message.includes("connection")
      ) {
         return createPortfolioError(
            PortfolioErrorType.NETWORK_ERROR,
            "Network connection failed. Please check your internet connection.",
            error.message,
            true,
            getRetryDelay(error)
         );
      }

      // File upload errors
      if (
         message.includes("upload") ||
         message.includes("file") ||
         message.includes("size") ||
         message.includes("type")
      ) {
         return createPortfolioError(
            PortfolioErrorType.FILE_UPLOAD_ERROR,
            error.message,
            undefined,
            message.includes("network") || message.includes("timeout")
         );
      }

      // Database errors
      if (
         message.includes("database") ||
         message.includes("mongodb") ||
         message.includes("collection")
      ) {
         return createPortfolioError(
            PortfolioErrorType.DATABASE_ERROR,
            "Database operation failed. Please try again.",
            error.message,
            true,
            getRetryDelay(error)
         );
      }

      // Permission errors
      if (
         message.includes("permission") ||
         message.includes("unauthorized") ||
         message.includes("forbidden") ||
         message.includes("401") ||
         message.includes("403")
      ) {
         return createPortfolioError(
            PortfolioErrorType.PERMISSION_ERROR,
            "You don't have permission to perform this action.",
            error.message,
            false
         );
      }

      // Not found errors
      if (message.includes("not found") || message.includes("404")) {
         return createPortfolioError(
            PortfolioErrorType.NOT_FOUND_ERROR,
            "The requested resource was not found.",
            error.message,
            false
         );
      }

      // Rate limit errors
      if (message.includes("rate limit") || message.includes("429")) {
         return createPortfolioError(
            PortfolioErrorType.RATE_LIMIT_ERROR,
            "Too many requests. Please wait before trying again.",
            error.message,
            true,
            getRetryDelay(error)
         );
      }

      // Server errors
      if (
         message.includes("500") ||
         message.includes("502") ||
         message.includes("503") ||
         message.includes("504")
      ) {
         return createPortfolioError(
            PortfolioErrorType.NETWORK_ERROR,
            "Server error occurred. Please try again.",
            error.message,
            true,
            getRetryDelay(error)
         );
      }
   }

   // Unknown error
   return createPortfolioError(
      PortfolioErrorType.UNKNOWN_ERROR,
      error instanceof Error ? error.message : "An unexpected error occurred",
      undefined,
      true,
      3
   );
}

/**
 * Formats error message for user display
 */
export function formatErrorMessage(error: PortfolioError): string {
   switch (error.type) {
      case PortfolioErrorType.VALIDATION_ERROR:
         return `Validation Error: ${error.message}`;

      case PortfolioErrorType.NETWORK_ERROR:
         return `Connection Error: ${error.message}`;

      case PortfolioErrorType.DATABASE_ERROR:
         return `Database Error: ${error.message}`;

      case PortfolioErrorType.FILE_UPLOAD_ERROR:
         return `Upload Error: ${error.message}`;

      case PortfolioErrorType.PERMISSION_ERROR:
         return `Permission Error: ${error.message}`;

      case PortfolioErrorType.NOT_FOUND_ERROR:
         return `Not Found: ${error.message}`;

      case PortfolioErrorType.RATE_LIMIT_ERROR:
         return `Rate Limited: ${error.message}`;

      default:
         return error.message;
   }
}

/**
 * Retry configuration for different operation types
 */
export interface RetryConfig {
   maxAttempts: number;
   baseDelay: number; // milliseconds
   maxDelay: number; // milliseconds
   backoffMultiplier: number;
   retryableErrors: PortfolioErrorType[];
}

export const DEFAULT_RETRY_CONFIG: RetryConfig = {
   maxAttempts: 3,
   baseDelay: 1000, // 1 second
   maxDelay: 30000, // 30 seconds
   backoffMultiplier: 2,
   retryableErrors: [
      PortfolioErrorType.NETWORK_ERROR,
      PortfolioErrorType.DATABASE_ERROR,
      PortfolioErrorType.RATE_LIMIT_ERROR,
      PortfolioErrorType.UNKNOWN_ERROR,
   ],
};

export const UPLOAD_RETRY_CONFIG: RetryConfig = {
   maxAttempts: 5,
   baseDelay: 2000, // 2 seconds
   maxDelay: 60000, // 1 minute
   backoffMultiplier: 1.5,
   retryableErrors: [
      PortfolioErrorType.NETWORK_ERROR,
      PortfolioErrorType.FILE_UPLOAD_ERROR,
      PortfolioErrorType.RATE_LIMIT_ERROR,
   ],
};

/**
 * Calculates retry delay with exponential backoff
 */
export function calculateRetryDelay(
   attempt: number,
   config: RetryConfig = DEFAULT_RETRY_CONFIG
): number {
   const delay =
      config.baseDelay * Math.pow(config.backoffMultiplier, attempt - 1);
   return Math.min(delay, config.maxDelay);
}

/**
 * Determines if an error should be retried based on config
 */
export function shouldRetryError(
   error: PortfolioError,
   attempt: number,
   config: RetryConfig = DEFAULT_RETRY_CONFIG
): boolean {
   if (attempt >= config.maxAttempts) {
      return false;
   }

   if (!error.retryable) {
      return false;
   }

   return config.retryableErrors.includes(error.type);
}

/**
 * Sleep utility for retry delays
 */
export function sleep(ms: number): Promise<void> {
   return new Promise((resolve) => setTimeout(resolve, ms));
}
