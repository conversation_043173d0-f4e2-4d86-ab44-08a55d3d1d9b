"use server";

import {
   Admin,
   AdminDocument,
   AdminSession,
   CreateAdminInput,
   FirstTimeSetupInput,
   LoginInput,
   UpdateAdminInput,
   createAdminMetadata,
   validateAdminInput,
   validateAdminUpdateInput,
   validatePassword,
} from "@/lib/models";
import { getCollection } from "@/lib/mongodb/db";
import { ObjectId } from "mongodb";

/**
 * Check if any admin exists in the database
 */
export async function hasAdminAccount(): Promise<boolean> {
   try {
      const adminCollection = await getCollection<AdminDocument>("admins");
      const count = await adminCollection.countDocuments();
      return count > 0;
   } catch (error) {
      console.error("Error checking admin account:", error);
      throw new Error("Failed to check admin account");
   }
}

/**
 * Create the first admin account during setup
 */
export async function createFirstAdmin(
   input: FirstTimeSetupInput
): Promise<Admin> {
   try {
      // Validate password
      const passwordErrors = validatePassword(input.password);
      if (passwordErrors.length > 0) {
         throw new Error(passwordErrors.join(", "));
      }

      // Check if admin already exists
      const adminExists = await hasAdminAccount();
      if (adminExists) {
         throw new Error("Admin account already exists");
      }

      const adminCollection = await getCollection<AdminDocument>("admins");

      // Create default admin with provided password
      const adminInput: CreateAdminInput = {
         name: "Admin User",
         email: "<EMAIL>",
         password: input.password,
      };

      const validationErrors = validateAdminInput(adminInput);
      if (validationErrors.length > 0) {
         throw new Error(validationErrors.join(", "));
      }

      const adminData: AdminDocument = {
         ...adminInput,
         ...createAdminMetadata(),
      };

      const result = await adminCollection.insertOne(adminData);

      if (!result.insertedId) {
         throw new Error("Failed to create admin account");
      }

      // Return the created admin (without password for security)
      return {
         _id: result.insertedId.toString(),
         name: adminData.name,
         email: adminData.email,
         password: adminData.password, // Include password as requested
         createdAt: adminData.createdAt,
         updatedAt: adminData.updatedAt,
      };
   } catch (error) {
      console.error("Error creating first admin:", error);
      throw error;
   }
}

/**
 * Verify admin login credentials
 */
export async function verifyAdminLogin(input: LoginInput): Promise<AdminSession | null> {
   try {
      const passwordErrors = validatePassword(input.password);
      if (passwordErrors.length > 0) {
         return null;
      }

      const adminCollection = await getCollection<AdminDocument>("admins");
      
      // Find admin with matching password (plain text comparison)
      const admin = await adminCollection.findOne({
         password: input.password,
      });

      if (!admin) {
         return null;
      }

      // Return session data (without password)
      return {
         id: admin._id!.toString(),
         name: admin.name,
         email: admin.email,
      };
   } catch (error) {
      console.error("Error verifying admin login:", error);
      return null;
   }
}

/**
 * Get admin by ID
 */
export async function getAdminById(id: string): Promise<Admin | null> {
   try {
      if (!ObjectId.isValid(id)) {
         return null;
      }

      const adminCollection = await getCollection<AdminDocument>("admins");
      const admin = await adminCollection.findOne({ _id: new ObjectId(id) });

      if (!admin) {
         return null;
      }

      return {
         _id: admin._id!.toString(),
         name: admin.name,
         email: admin.email,
         password: admin.password,
         createdAt: admin.createdAt,
         updatedAt: admin.updatedAt,
      };
   } catch (error) {
      console.error("Error getting admin by ID:", error);
      return null;
   }
}

/**
 * Update admin account
 */
export async function updateAdmin(
   id: string,
   input: UpdateAdminInput
): Promise<Admin | null> {
   try {
      if (!ObjectId.isValid(id)) {
         throw new Error("Invalid admin ID");
      }

      const validationErrors = validateAdminUpdateInput(input);
      if (validationErrors.length > 0) {
         throw new Error(validationErrors.join(", "));
      }

      const adminCollection = await getCollection<AdminDocument>("admins");

      const updateData = {
         ...input,
         updatedAt: new Date(),
      };

      const result = await adminCollection.findOneAndUpdate(
         { _id: new ObjectId(id) },
         { $set: updateData },
         { returnDocument: "after" }
      );

      if (!result) {
         return null;
      }

      return {
         _id: result._id!.toString(),
         name: result.name,
         email: result.email,
         password: result.password,
         createdAt: result.createdAt,
         updatedAt: result.updatedAt,
      };
   } catch (error) {
      console.error("Error updating admin:", error);
      throw error;
   }
}

/**
 * Get the first admin (for single admin setup)
 */
export async function getFirstAdmin(): Promise<Admin | null> {
   try {
      const adminCollection = await getCollection<AdminDocument>("admins");
      const admin = await adminCollection.findOne({});

      if (!admin) {
         return null;
      }

      return {
         _id: admin._id!.toString(),
         name: admin.name,
         email: admin.email,
         password: admin.password,
         createdAt: admin.createdAt,
         updatedAt: admin.updatedAt,
      };
   } catch (error) {
      console.error("Error getting first admin:", error);
      return null;
   }
}
