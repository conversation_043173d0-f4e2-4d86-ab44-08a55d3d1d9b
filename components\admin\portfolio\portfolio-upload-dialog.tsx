"use client";

import PortfolioUpload from "@/components/admin/portfolio/portfolio-upload";
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogHeader,
   DialogTitle,
} from "@/components/ui/dialog";

interface PortfolioUploadDialogProps {
   open: boolean;
   onOpenChange: (open: boolean) => void;
   serviceId?: string;
   onUploadComplete?: () => void;
   title?: string;
   subtitle?: string;
}

export default function PortfolioUploadDialog({
   open,
   onOpenChange,
   serviceId,
   onUploadComplete,
   title = "Upload Portfolio Images",
   subtitle = "Add new images to your portfolio service",
}: PortfolioUploadDialogProps) {
   const handleUploadComplete = () => {
      if (onUploadComplete) {
         onUploadComplete();
      }
      // Close dialog after successful upload
      onOpenChange(false);
   };

   return (
      <Dialog open={open} onOpenChange={onOpenChange}>
         <DialogContent className="sm:max-w-4xl">
            <DialogHeader>
               <DialogTitle>{title}</DialogTitle>
               <DialogDescription>{subtitle}</DialogDescription>
            </DialogHeader>

            <div className="mt-4">
               <PortfolioUpload
                  defaultServiceId={serviceId}
                  options={{
                     maxFiles: 20,
                     maxSizeBytes: 50 * 1024 * 1024, // 50MB
                  }}
                  onUploadComplete={handleUploadComplete}
                  title="Upload Images to Portfolio Service"
                  subtitle="Drag and drop your images here, or click to browse"
               />
            </div>
         </DialogContent>
      </Dialog>
   );
}
