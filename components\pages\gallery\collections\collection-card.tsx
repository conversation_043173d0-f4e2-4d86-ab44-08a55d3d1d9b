"use client";

import { Badge } from "@/components/ui/badge";
import { CollectionWithStats } from "@/lib/models";
import { cn } from "@/lib/utils";
import { PhotoIcon, SparklesIcon, TagIcon } from "@heroicons/react/24/solid";
import { motion } from "framer-motion";
import { Calendar, Image as ImageIcon } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

interface CollectionCardProps {
   collection: CollectionWithStats;
   className?: string;
   variant?: "default" | "minimal" | "artistic";
   showStats?: boolean;
   showDescription?: boolean;
}

export default function CollectionCard({
   collection,
   className,
   variant = "default",
   showStats = true,
   showDescription = true,
}: CollectionCardProps) {
   const [isHovered, setIsHovered] = useState(false);

   const formatDate = (date: Date | string) => {
      return new Date(date).toLocaleDateString("en-US", {
         year: "numeric",
         month: "short",
      });
   };

   // Generate a gradient based on collection color or use default
   const getGradient = () => {
      if (collection.color) {
         return `linear-gradient(135deg, ${collection.color}20, ${collection.color}05)`;
      }
      return "linear-gradient(135deg, hsl(var(--primary) / 0.1), hsl(var(--accent) / 0.05))";
   };

   const getBorderColor = () => {
      if (collection.color) {
         return `${collection.color}40`;
      }
      return "hsl(var(--border))";
   };

   const cardVariants = {
      default: "p-6",
      minimal: "p-6",
      artistic: "p-10",
   };

   const iconVariants = {
      default: "w-10 h-10",
      minimal: "w-8 h-8",
      artistic: "w-16 h-16",
   };

   return (
      <Link href={`/gallery/collections/${collection._id}`} className="block">
         <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            onHoverStart={() => setIsHovered(true)}
            onHoverEnd={() => setIsHovered(false)}
            className={cn(
               "group relative overflow-hidden rounded-3xl border transition-all duration-500 cursor-pointer",
               "hover:shadow-2xl hover:-translate-y-1",
               variant === "artistic" && "hover:rotate-1",
               className
            )}
            style={{
               background: getGradient(),
               borderColor: getBorderColor(),
            }}
         >
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-5">
               <div className="absolute top-4 right-4">
                  <PhotoIcon className="w-32 h-32 text-current rotate-12" />
               </div>
               <div className="absolute bottom-4 left-4">
                  <SparklesIcon className="w-24 h-24 text-current -rotate-12" />
               </div>
            </div>

            {/* Content */}
            <div className={cn("relative z-10", cardVariants[variant])}>
               {/* Header */}
               <div className="flex items-start justify-between mb-3">
                  {/* Icon */}
                  <motion.div
                     // animate={{
                     //    rotate: isHovered ? 360 : 0,
                     //    scale: isHovered ? 1.1 : 1,
                     // }}
                     transition={{ duration: 0.6, ease: "easeInOut" }}
                     className={cn(
                        "flex items-center justify-center rounded-2xl",
                        "bg-gradient-to-br from-white/20 to-white/5 backdrop-blur-sm",
                        "border border-white/20",
                        "rounded-full",
                        iconVariants[variant]
                     )}
                     style={{
                        backgroundColor: collection.color
                           ? `${collection.color}20`
                           : undefined,
                        borderColor: collection.color
                           ? `${collection.color}40`
                           : undefined,
                     }}
                  >
                     <TagIcon
                        className="w-1/2 h-1/2"
                        style={{
                           color: collection.color || "hsl(var(--primary))",
                        }}
                     />
                  </motion.div>
               </div>

               {/* Title */}
               <h3 className="text-xl font-bold text-foreground mb-2 line-clamp-2">
                  {collection.name}
               </h3>

               {/* Description */}
               {showDescription && collection.description && (
                  <p className="text-muted-foreground mb-3 line-clamp-3 leading-relaxed">
                     {collection.description}
                  </p>
               )}

               {/* Stats */}
               {showStats && (
                  <div className="flex items-center gap-6 text-sm text-muted-foreground">
                     <div className="flex items-center gap-2">
                        <ImageIcon className="w-4 h-4" />
                        <span>{collection.imageCount} images</span>
                     </div>
                     <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4" />
                        <span>{formatDate(collection.createdAt)}</span>
                     </div>
                  </div>
               )}

               {/* Decorative Badge */}
               {variant === "artistic" && (
                  <motion.div
                     initial={{ scale: 0, rotate: -45 }}
                     animate={{
                        scale: isHovered ? 1 : 0.8,
                        rotate: isHovered ? 0 : -45,
                     }}
                     transition={{ duration: 0.4, delay: 0.1 }}
                     className="absolute top-4 right-4"
                  >
                     <Badge
                        variant="secondary"
                        className="bg-white/10 backdrop-blur-sm text-foreground border-white/20"
                     >
                        <SparklesIcon className="w-3 h-3 mr-1" />
                        Collection
                     </Badge>
                  </motion.div>
               )}
            </div>

            {/* Hover Effects */}
            <motion.div
               initial={{ opacity: 0 }}
               animate={{ opacity: isHovered ? 1 : 0 }}
               transition={{ duration: 0.3 }}
               className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent pointer-events-none"
            />

            {/* Animated Border */}
            <motion.div
               initial={{ pathLength: 0 }}
               animate={{ pathLength: isHovered ? 1 : 0 }}
               transition={{ duration: 0.8, ease: "easeInOut" }}
               className="absolute inset-0 pointer-events-none"
            >
               <svg className="w-full h-full">
                  <rect
                     x="1"
                     y="1"
                     width="calc(100% - 2px)"
                     height="calc(100% - 2px)"
                     rx="24"
                     fill="none"
                     stroke={collection.color || "hsl(var(--primary))"}
                     strokeWidth="2"
                     strokeDasharray="10 5"
                     className="opacity-50"
                  />
               </svg>
            </motion.div>

            {/* Floating Elements */}
            <motion.div
               animate={{
                  y: isHovered ? -10 : 0,
                  rotate: isHovered ? 5 : 0,
               }}
               transition={{ duration: 0.6, ease: "easeOut" }}
               className="absolute -top-2 -right-2 w-6 h-6 bg-primary/20 rounded-full blur-sm"
            />
            <motion.div
               animate={{
                  y: isHovered ? 5 : 0,
                  rotate: isHovered ? -3 : 0,
               }}
               transition={{ duration: 0.8, ease: "easeOut", delay: 0.1 }}
               className="absolute -bottom-1 -left-1 w-4 h-4 bg-accent/20 rounded-full blur-sm"
            />
         </motion.div>
      </Link>
   );
}
