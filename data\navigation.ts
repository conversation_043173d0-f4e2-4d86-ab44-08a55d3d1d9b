export const navigationItems = [
   { name: "Home", href: "/" },
   { name: "About", href: "/about" },
   {
      name: "Services",
      href: "/services",
      submenu: [
         { name: "Wedding Photography", href: "/services/wedding" },
         { name: "Pre-Wedding Shoots", href: "/services/pre-wedding" },
         { name: "Pregnancy Photography", href: "/services/pregnancy" },
         { name: "Child Dedication", href: "/services/child-dedication" },
         { name: "Birthday Shoots", href: "/services/birthday-shoot" },
         { name: "Bridal Shower", href: "/services/bridal-shower" },
         { name: "Videography", href: "/services/videography" },
         { name: "360 Video Booth", href: "/services/360-booth" },
         { name: "Dry Ice Machine", href: "/services/dry-ice" },
      ],
   },
   {
      name: "Gallery",
      href: "/gallery",
      submenu: [
         { name: "All Galleries", href: "/gallery" },
         { name: "Albums", href: "/gallery/albums" },
         { name: "Collections", href: "/gallery/collections" },
      ],
   },
   { name: "Portfolio", href: "/portfolio" },
   { name: "Contact", href: "/contact" },
];

export const footerNavigation = {
   services: [
      { name: "Wedding Photography", href: "/services/wedding" },
      { name: "Pre-Wedding Shoots", href: "/services/pre-wedding" },
      { name: "Pregnancy Photography", href: "/services/pregnancy" },
      { name: "Child Dedication", href: "/services/child-dedication" },
      { name: "Birthday Shoots", href: "/services/birthday-shoot" },
      { name: "Bridal Shower", href: "/services/bridal-shower" },
      { name: "Videography", href: "/services/videography" },
      // { name: "360 Video Booth", href: "/services/360-booth" },
      // { name: "Dry Ice Machine", href: "/services/dry-ice" },
   ],
   quickLinks: [
      { name: "Home", href: "/" },
      { name: "About", href: "/about" },
      { name: "Services", href: "/services" },
      { name: "Gallery", href: "/gallery" },
      { name: "Portfolio", href: "/portfolio" },
      { name: "Contact", href: "/contact" },
   ],
};
