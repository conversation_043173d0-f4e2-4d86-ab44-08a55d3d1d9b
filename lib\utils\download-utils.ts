import JSZ<PERSON> from "jszip";

export async function downloadImagesAsZip(
   images: Array<{ url: string; name: string }>,
   zipFileName: string = "images.zip"
): Promise<void> {
   try {
      const zip = new JSZip();

      // Download all images and add them to the zip
      const downloadPromises = images.map(async (image, index) => {
         try {
            const response = await fetch(image.url);
            if (!response.ok) {
               throw new Error(`Failed to fetch ${image.name}`);
            }
            const blob = await response.blob();
            
            // Use the image name or generate a fallback name
            const fileName = image.name || `image_${index + 1}.jpg`;
            zip.file(fileName, blob);
         } catch (error) {
            console.error(`Error downloading ${image.name}:`, error);
            // Continue with other images even if one fails
         }
      });

      await Promise.all(downloadPromises);

      // Generate and download the zip file
      const zipBlob = await zip.generateAsync({ type: "blob" });
      const url = URL.createObjectURL(zipBlob);
      
      const link = document.createElement("a");
      link.href = url;
      link.download = zipFileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // Clean up the URL object
      URL.revokeObjectURL(url);
   } catch (error) {
      console.error("Error creating zip file:", error);
      throw error;
   }
}
