"use client";

import { GalleryBreadcrumb, MasonryGallery } from "@/components/pages/gallery";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { useImages } from "@/lib/hooks/use-images";
import { CollectionWithStats } from "@/lib/models";
import { TagIcon } from "@heroicons/react/24/solid";
import { motion } from "framer-motion";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { useState } from "react";
import CollectionHero from "./collection-hero";

interface CollectionPageContentProps {
   collection: CollectionWithStats;
}

export default function CollectionPageContent({
   collection,
}: CollectionPageContentProps) {
   const [isLiked, setIsLiked] = useState(false);

   // Fetch images for this collection
   const { data: imagesData, isLoading: imagesLoading } = useImages({
      collectionId: collection._id as string,
      limit: 50, // Load more images for collection view
   });

   const images = imagesData?.data || [];

   // Breadcrumb items
   const breadcrumbItems = [
      { label: "Gallery", href: "/gallery" },
      { label: "Collections", href: "/gallery/collections" },
      { label: collection.name, isActive: true },
   ];

   // Handle share
   const handleShare = async () => {
      const shareData = {
         title: collection.name,
         text:
            collection.description ||
            `Check out this collection: ${collection.name}`,
         url: window.location.href,
      };

      if (navigator.share) {
         try {
            await navigator.share(shareData);
         } catch (error) {
            console.error("Error sharing collection:", error);
            // Fallback to copying URL
            navigator.clipboard.writeText(window.location.href);
         }
      } else {
         navigator.clipboard.writeText(window.location.href);
      }
   };

   // Handle like toggle
   const handleLike = () => {
      setIsLiked(!isLiked);
      // Here you would typically make an API call to save the like
   };

   return (
      <div className="min-h-screen py-8 mt-20">
         <div className="container mx-auto px-4">
            {/* Breadcrumb */}
            <GalleryBreadcrumb items={breadcrumbItems} />

            {/* Collection Header */}
            <CollectionHero
               collection={collection}
               isLiked={isLiked}
               handleLike={handleLike}
               handleShare={handleShare}
            />

            {/* Images Gallery */}
            <motion.div
               initial={{ opacity: 0, y: 20 }}
               animate={{ opacity: 1, y: 0 }}
               transition={{ duration: 0.6, delay: 0.2 }}
            >
               <div className="mb-8">
                  <h2 className="text-2xl font-bold text-foreground mb-4 flex items-center gap-3">
                     <TagIcon className="w-6 h-6 text-primary" />
                     Collection Gallery
                  </h2>
                  <p className="text-muted-foreground">
                     Explore the curated images in this collection. Click on any
                     image to view it in full size.
                  </p>
               </div>

               {imagesLoading ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                     {Array.from({ length: 12 }).map((_, i) => (
                        <Skeleton key={i} className="aspect-[4/3] rounded-xl" />
                     ))}
                  </div>
               ) : images.length > 0 ? (
                  <MasonryGallery
                     images={images}
                     columns={4}
                     gap={16}
                     enableLightbox={true}
                     showLoadingStates={true}
                  />
               ) : (
                  <div className="text-center py-20">
                     <TagIcon className="w-20 h-20 mx-auto mb-6 text-muted-foreground/50" />
                     <h3 className="text-2xl font-semibold text-foreground mb-4">
                        No Images Found
                     </h3>
                     <p className="text-muted-foreground text-lg mb-8 max-w-md mx-auto">
                        This collection doesn&apos;t contain any images yet.
                        Check back later for updates.
                     </p>
                     <Button asChild variant="outline">
                        <Link href="/gallery/collections">
                           <ArrowLeft className="w-4 h-4 mr-2" />
                           Back to Collections
                        </Link>
                     </Button>
                  </div>
               )}
            </motion.div>
         </div>
      </div>
   );
}
