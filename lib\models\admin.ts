import { ObjectId } from "mongodb";

// Admin interface for database storage
export interface Admin {
   _id?: ObjectId | string;
   name: string;
   email: string;
   password: string; // Plain text as requested
   createdAt: Date;
   updatedAt: Date;
}

// Admin document interface for MongoDB operations
export interface AdminDocument {
   _id?: ObjectId;
   name: string;
   email: string;
   password: string; // Plain text as requested
   createdAt: Date;
   updatedAt: Date;
}

// Admin creation input
export interface CreateAdminInput {
   name: string;
   email: string;
   password: string;
}

// Admin update input
export interface UpdateAdminInput {
   name?: string;
   email?: string;
   password?: string;
}

// Admin session data (without password)
export interface AdminSession {
   id: string;
   name: string;
   email: string;
}

// First-time setup input
export interface FirstTimeSetupInput {
   password: string;
}

// Login input
export interface LoginInput {
   password: string;
}

// Admin metadata creation helper
export function createAdminMetadata(): {
   createdAt: Date;
   updatedAt: Date;
} {
   const now = new Date();
   return {
      createdAt: now,
      updatedAt: now,
   };
}

// Admin input validation
export function validateAdminInput(input: CreateAdminInput): string[] {
   const errors: string[] = [];

   if (!input.name || input.name.trim().length === 0) {
      errors.push("Name is required");
   }

   if (input.name && input.name.length > 100) {
      errors.push("Name must be less than 100 characters");
   }

   if (!input.email || input.email.trim().length === 0) {
      errors.push("Email is required");
   }

   if (input.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(input.email)) {
      errors.push("Invalid email format");
   }

   if (!input.password || input.password.length === 0) {
      errors.push("Password is required");
   }

   if (input.password && input.password.length < 6) {
      errors.push("Password must be at least 6 characters long");
   }

   return errors;
}

// Admin update validation
export function validateAdminUpdateInput(input: UpdateAdminInput): string[] {
   const errors: string[] = [];

   if (input.name !== undefined) {
      if (!input.name || input.name.trim().length === 0) {
         errors.push("Name cannot be empty");
      }
      if (input.name && input.name.length > 100) {
         errors.push("Name must be less than 100 characters");
      }
   }

   if (input.email !== undefined) {
      if (!input.email || input.email.trim().length === 0) {
         errors.push("Email cannot be empty");
      }
      if (input.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(input.email)) {
         errors.push("Invalid email format");
      }
   }

   if (input.password !== undefined) {
      if (!input.password || input.password.length === 0) {
         errors.push("Password cannot be empty");
      }
      if (input.password && input.password.length < 6) {
         errors.push("Password must be at least 6 characters long");
      }
   }

   return errors;
}

// Password validation
export function validatePassword(password: string): string[] {
   const errors: string[] = [];

   if (!password || password.length === 0) {
      errors.push("Password is required");
   }

   if (password && password.length < 6) {
      errors.push("Password must be at least 6 characters long");
   }

   return errors;
}
