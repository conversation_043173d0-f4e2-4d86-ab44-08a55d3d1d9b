import ElementReveal from "@/components/animations/element-reveal";
import StaggerReveal from "@/components/animations/stagger-reveal";
import TextReveal from "@/components/animations/text-reveal";
import { Button } from "@/components/ui/button";
import {
   Card,
   CardContent,
   CardDescription,
   CardHeader,
   CardTitle,
} from "@/components/ui/card";
import { ArrowRight, Camera, Edit, Music, Video } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

const features = [
   {
      icon: Video,
      title: "4K Quality",
      description:
         "Professional 4K recording ensures crystal clear video quality that looks stunning on any screen.",
   },
   {
      icon: Camera,
      title: "Multi-Camera Setup",
      description:
         "Multiple camera angles capture every moment and emotion from different perspectives.",
   },
   {
      icon: Music,
      title: "Professional Audio",
      description:
         "High-quality audio recording and music selection create an immersive viewing experience.",
   },
   {
      icon: Edit,
      title: "Expert Editing",
      description:
         "Professional editing and color grading bring your story to life with cinematic quality.",
   },
];

export default function VideographyPage() {
   return (
      <div className="min-h-screen">
         {/* Hero Section */}
         <section className="relative !pt-38 pb-16">
            <div className="absolute inset-0 z-0">
               <Image
                  src="/images/videography.jpg"
                  alt="Videography services hero"
                  fill
                  className="object-cover"
                  priority
                  sizes="100vw"
               />
               <div className="absolute inset-0 bg-black/50" />
            </div>

            <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
               <TextReveal>
                  <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
                     Professional Videography
                  </h1>
               </TextReveal>
               <TextReveal className="mb-8">
                  <p className="text-lg max-w-3xl mx-auto leading-relaxed">
                     Capture your special moments in motion with cinematic
                     quality videography that tells your story beautifully
                  </p>
               </TextReveal>
               <ElementReveal>
                  <div className="flex flex-col sm:flex-row gap-4 align-center justify-center">
                     <Button asChild size="lg">
                        <Link href="/contact">Book Your Videography</Link>
                     </Button>
                     <Button
                        asChild
                        variant="outline"
                        size="lg"
                        className="text-white hover:bg-white hover:text-black"
                     >
                        <Link href="/portfolio">View Our Work</Link>
                     </Button>
                  </div>
               </ElementReveal>
            </div>
         </section>

         {/* Features Section */}
         <section className="py-20">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
               <div className="text-center mb-16">
                  <TextReveal>
                     <h2 className="text-3xl md:text-4xl font-bold mb-4">
                        Why Choose Our Videography Services
                     </h2>
                  </TextReveal>
                  <TextReveal>
                     <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                        We combine technical expertise with creative
                        storytelling to produce videos that capture the emotion
                        and beauty of your special day.
                     </p>
                  </TextReveal>
               </div>

               <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                  {features.map((feature, index) => (
                     <StaggerReveal key={feature.title} index={index}>
                        <Card className="text-center">
                           <CardHeader>
                              <div className="flex justify-center mb-4">
                                 <feature.icon className="h-12 w-12 text-primary" />
                              </div>
                              <CardTitle>{feature.title}</CardTitle>
                           </CardHeader>
                           <CardContent>
                              <CardDescription className="text-center">
                                 {feature.description}
                              </CardDescription>
                           </CardContent>
                        </Card>
                     </StaggerReveal>
                  ))}
               </div>
            </div>
         </section>

         {/* Services Section */}
         <section className="py-20 bg-astral-grey">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
               <div className="text-center mb-16">
                  <TextReveal>
                     <h2 className="text-3xl md:text-4xl font-bold mb-4">
                        Our Videography Services
                     </h2>
                  </TextReveal>
                  <TextReveal>
                     <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                        We offer comprehensive videography services for various
                        events and occasions.
                     </p>
                  </TextReveal>
               </div>

               <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {[
                     {
                        title: "Wedding Videography",
                        description:
                           "Complete wedding day coverage including ceremony, reception, and highlight reels that tell your love story.",
                     },
                     {
                        title: "Event Documentation",
                        description:
                           "Professional documentation of corporate events, celebrations, and special occasions with full editing services.",
                     },
                     {
                        title: "Promotional Videos",
                        description:
                           "Business promotional videos, testimonials, and marketing content that showcases your brand professionally.",
                     },
                  ].map((svc, index) => (
                     <StaggerReveal key={svc.title} index={index}>
                        <Card>
                           <CardHeader>
                              <CardTitle>{svc.title}</CardTitle>
                           </CardHeader>
                           <CardContent>
                              <CardDescription>
                                 {svc.description}
                              </CardDescription>
                           </CardContent>
                        </Card>
                     </StaggerReveal>
                  ))}
               </div>
            </div>
         </section>

         {/* CTA Section */}
         <section className="py-20 bg-background">
            <div className="container mx-auto px-4 text-center">
               <div className="max-w-3xl mx-auto">
                  <TextReveal>
                     <h2 className="text-4xl font-playfair font-bold text-foreground mb-6">
                        Ready to Create Your{" "}
                        <span className="bg-gradient-accent bg-clip-text text-transparent">
                           Video Story?{" "}
                        </span>
                     </h2>
                  </TextReveal>
                  <TextReveal className="mb-8">
                     <p className="text-lg text-muted-foreground font-montserrat leading-relaxed">
                        Let&apos;s discuss your videography needs and create a
                        cinematic experience that captures your special moments
                        perfectly.
                     </p>
                  </TextReveal>
                  <ElementReveal>
                     <div className="flex flex-col sm:flex-row gap-4 items-center justify-center">
                        <Button
                           asChild
                           size="lg"
                           className="bg-gradient-accent hover:opacity-90 font-montserrat font-semibold text-lg px-8"
                        >
                           <Link href="/contact">
                              Contact Now{" "}
                              <ArrowRight className="ml-2 h-5 w-5" />
                           </Link>
                        </Button>
                     </div>
                  </ElementReveal>
               </div>
            </div>
         </section>
      </div>
   );
}
