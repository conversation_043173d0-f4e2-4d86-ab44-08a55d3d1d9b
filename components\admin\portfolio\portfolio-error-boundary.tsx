"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
   categorizeError,
   PortfolioError,
   PortfolioErrorType,
} from "@/lib/utils/portfolio-error-handling";
import { showErrorToast } from "@/lib/utils/toast-notifications";
import { AlertTriangle, Home, RefreshCw } from "lucide-react";
import { useRouter } from "next/navigation";
import React, { Component, ErrorInfo, ReactNode } from "react";

interface Props {
   children: ReactNode;
   fallback?: ReactNode;
   onError?: (error: Error, errorInfo: ErrorInfo) => void;
   showRetry?: boolean;
   showHome?: boolean;
}

interface State {
   hasError: boolean;
   error: Error | null;
   errorId: string;
   retryCount: number;
}

export class PortfolioErrorBoundary extends Component<Props, State> {
   private maxRetries = 3;

   constructor(props: Props) {
      super(props);
      this.state = {
         hasError: false,
         error: null,
         errorId: "",
         retryCount: 0,
      };
   }

   static getDerivedStateFromError(error: Error): State {
      return {
         hasError: true,
         error,
         errorId: `error_${Date.now()}_${Math.random()
            .toString(36)
            .substring(2, 9)}`,
         retryCount: 0,
      };
   }

   componentDidCatch(error: Error, errorInfo: ErrorInfo) {
      console.error(
         "Portfolio Error Boundary caught an error:",
         error,
         errorInfo
      );

      // Categorize the error
      const portfolioError = categorizeError(error);

      // Show error toast
      showErrorToast(portfolioError, {
         duration: 8000,
         onRetry: portfolioError.retryable ? this.handleRetry : undefined,
      });

      // Call custom error handler if provided
      this.props.onError?.(error, errorInfo);

      // Report to error tracking service (if available)
      if (
         typeof window !== "undefined" &&
         (window as unknown as { gtag?: (...args: unknown[]) => void }).gtag
      ) {
         (window as unknown as { gtag: (...args: unknown[]) => void }).gtag(
            "event",
            "exception",
            {
               description: error.message,
               fatal: false,
               error_id: this.state.errorId,
            }
         );
      }
   }

   handleRetry = () => {
      if (this.state.retryCount < this.maxRetries) {
         this.setState((prevState) => ({
            hasError: false,
            error: null,
            retryCount: prevState.retryCount + 1,
         }));
      }
   };

   handleReset = () => {
      this.setState({
         hasError: false,
         error: null,
         errorId: "",
         retryCount: 0,
      });
   };

   render() {
      if (this.state.hasError && this.state.error) {
         // Use custom fallback if provided
         if (this.props.fallback) {
            return this.props.fallback;
         }

         const portfolioError = categorizeError(this.state.error);
         const canRetry =
            portfolioError.retryable && this.state.retryCount < this.maxRetries;

         return (
            <PortfolioErrorFallback
               error={portfolioError}
               errorId={this.state.errorId}
               retryCount={this.state.retryCount}
               maxRetries={this.maxRetries}
               onRetry={canRetry ? this.handleRetry : undefined}
               onReset={this.handleReset}
               showRetry={this.props.showRetry !== false}
               showHome={this.props.showHome !== false}
            />
         );
      }

      return this.props.children;
   }
}

interface ErrorFallbackProps {
   error: PortfolioError;
   errorId: string;
   retryCount: number;
   maxRetries: number;
   onRetry?: () => void;
   onReset: () => void;
   showRetry: boolean;
   showHome: boolean;
}

function PortfolioErrorFallback({
   error,
   errorId,
   retryCount,
   maxRetries,
   onRetry,
   onReset,
   showRetry,
   showHome,
}: ErrorFallbackProps) {
   const router = useRouter();

   const getErrorIcon = () => {
      switch (error.type) {
         case PortfolioErrorType.NETWORK_ERROR:
            return "🌐";
         case PortfolioErrorType.DATABASE_ERROR:
            return "💾";
         case PortfolioErrorType.FILE_UPLOAD_ERROR:
            return "📁";
         case PortfolioErrorType.PERMISSION_ERROR:
            return "🔒";
         case PortfolioErrorType.VALIDATION_ERROR:
            return "⚠️";
         default:
            return "❌";
      }
   };

   const getErrorTitle = () => {
      switch (error.type) {
         case PortfolioErrorType.NETWORK_ERROR:
            return "Connection Problem";
         case PortfolioErrorType.DATABASE_ERROR:
            return "Data Access Error";
         case PortfolioErrorType.FILE_UPLOAD_ERROR:
            return "Upload Failed";
         case PortfolioErrorType.PERMISSION_ERROR:
            return "Access Denied";
         case PortfolioErrorType.VALIDATION_ERROR:
            return "Invalid Input";
         case PortfolioErrorType.NOT_FOUND_ERROR:
            return "Not Found";
         case PortfolioErrorType.RATE_LIMIT_ERROR:
            return "Too Many Requests";
         default:
            return "Something Went Wrong";
      }
   };

   const getSuggestion = () => {
      switch (error.type) {
         case PortfolioErrorType.NETWORK_ERROR:
            return "Please check your internet connection and try again.";
         case PortfolioErrorType.DATABASE_ERROR:
            return "There was a problem accessing the data. Please try again in a moment.";
         case PortfolioErrorType.FILE_UPLOAD_ERROR:
            return "The file upload failed. Please check the file and try again.";
         case PortfolioErrorType.PERMISSION_ERROR:
            return "You don't have permission to access this resource. Please contact an administrator.";
         case PortfolioErrorType.VALIDATION_ERROR:
            return "Please check your input and try again.";
         case PortfolioErrorType.NOT_FOUND_ERROR:
            return "The requested resource could not be found.";
         case PortfolioErrorType.RATE_LIMIT_ERROR:
            return "You're making too many requests. Please wait a moment before trying again.";
         default:
            return "An unexpected error occurred. Please try refreshing the page.";
      }
   };

   return (
      <div className="flex items-center justify-center min-h-[400px] p-4">
         <Card className="w-full max-w-md">
            <CardHeader className="text-center">
               <div className="text-4xl mb-2">{getErrorIcon()}</div>
               <CardTitle className="flex items-center justify-center gap-2">
                  <AlertTriangle className="w-5 h-5 text-destructive" />
                  {getErrorTitle()}
               </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
               <div className="text-center space-y-2">
                  <p className="text-muted-foreground">{getSuggestion()}</p>

                  {error.details && (
                     <details className="text-sm">
                        <summary className="cursor-pointer text-muted-foreground hover:text-foreground">
                           Technical Details
                        </summary>
                        <div className="mt-2 p-2 bg-muted rounded text-xs font-mono">
                           <p>
                              <strong>Error ID:</strong> {errorId}
                           </p>
                           <p>
                              <strong>Type:</strong> {error.type}
                           </p>
                           <p>
                              <strong>Message:</strong> {error.message}
                           </p>
                           {error.details && (
                              <p>
                                 <strong>Details:</strong> {error.details}
                              </p>
                           )}
                           {retryCount > 0 && (
                              <p>
                                 <strong>Retry Attempts:</strong> {retryCount}/
                                 {maxRetries}
                              </p>
                           )}
                        </div>
                     </details>
                  )}
               </div>

               <div className="flex flex-col gap-2">
                  {showRetry && onRetry && (
                     <Button onClick={onRetry} className="w-full">
                        <RefreshCw className="w-4 h-4 mr-2" />
                        Try Again{" "}
                        {retryCount > 0 && `(${retryCount}/${maxRetries})`}
                     </Button>
                  )}

                  <Button
                     variant="outline"
                     onClick={onReset}
                     className="w-full"
                  >
                     <RefreshCw className="w-4 h-4 mr-2" />
                     Reset Component
                  </Button>

                  {showHome && (
                     <Button
                        variant="ghost"
                        onClick={() => router.push("/admin")}
                        className="w-full"
                     >
                        <Home className="w-4 h-4 mr-2" />
                        Go to Dashboard
                     </Button>
                  )}
               </div>
            </CardContent>
         </Card>
      </div>
   );
}

// Hook version for functional components
export function useErrorBoundary() {
   const [error, setError] = React.useState<Error | null>(null);

   const resetError = React.useCallback(() => {
      setError(null);
   }, []);

   const captureError = React.useCallback((error: Error) => {
      setError(error);
   }, []);

   React.useEffect(() => {
      if (error) {
         throw error;
      }
   }, [error]);

   return { captureError, resetError };
}

// HOC version
export function withPortfolioErrorBoundary<P extends object>(
   Component: React.ComponentType<P>,
   errorBoundaryProps?: Omit<Props, "children">
) {
   const WrappedComponent = (props: P) => (
      <PortfolioErrorBoundary {...errorBoundaryProps}>
         <Component {...props} />
      </PortfolioErrorBoundary>
   );

   WrappedComponent.displayName = `withPortfolioErrorBoundary(${
      Component.displayName || Component.name
   })`;

   return WrappedComponent;
}
