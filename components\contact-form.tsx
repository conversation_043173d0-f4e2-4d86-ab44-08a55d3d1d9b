"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
   Card,
   CardContent,
   CardDescription,
   CardHeader,
   CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useState } from "react";

interface ContactFormProps {
   className?: string;
}

export function ContactForm({ className = "" }: ContactFormProps) {
   const [formData, setFormData] = useState({
      name: "",
      email: "",
      phone: "",
      service: "",
      eventDate: "",
      message: "",
   });

   const [isSubmitting, setIsSubmitting] = useState(false);

   const handleChange = (
      e: React.ChangeEvent<
         HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
      >
   ) => {
      const { name, value } = e.target;
      setFormData((prev) => ({
         ...prev,
         [name]: value,
      }));
   };

   const handleSubmit = async (e: React.FormEvent) => {
      e.preventDefault();
      setIsSubmitting(true);

      // Here you would typically send the form data to your backend
      // For now, we'll just simulate a submission
      await new Promise((resolve) => setTimeout(resolve, 1000));

      console.log("Form submitted:", formData);
      alert("Thank you for your inquiry! We'll get back to you soon.");

      // Reset form
      setFormData({
         name: "",
         email: "",
         phone: "",
         service: "",
         eventDate: "",
         message: "",
      });

      setIsSubmitting(false);
   };

   return (
      <Card className={className}>
         <CardHeader>
            <CardTitle>Get in Touch</CardTitle>
            <CardDescription>
               Ready to capture your special moments? Send us a message and
               we&apos;ll get back to you within 24 hours.
            </CardDescription>
         </CardHeader>
         <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
               <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                     <Label htmlFor="name">Full Name *</Label>
                     <Input
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleChange}
                        required
                        placeholder="Your full name"
                        className="text-base" // Prevents zoom on iOS
                     />
                  </div>

                  <div className="space-y-2">
                     <Label htmlFor="email">Email *</Label>
                     <Input
                        id="email"
                        name="email"
                        type="email"
                        value={formData.email}
                        onChange={handleChange}
                        required
                        placeholder="<EMAIL>"
                        className="text-base" // Prevents zoom on iOS
                     />
                  </div>
               </div>

               <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                     <Label htmlFor="phone">Phone Number</Label>
                     <Input
                        id="phone"
                        name="phone"
                        type="tel"
                        value={formData.phone}
                        onChange={handleChange}
                        placeholder="+44 7XXX XXX XXX"
                        className="text-base"
                     />
                  </div>

                  <div className="space-y-2">
                     <Label htmlFor="service">Service Interested In</Label>
                     <select
                        id="service"
                        name="service"
                        value={formData.service}
                        onChange={handleChange}
                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                     >
                        <option value="">Select a service</option>
                        <option value="wedding">Wedding Photography</option>
                        <option value="pre-wedding">Pre-Wedding Shoots</option>
                        <option value="pregnancy">Pregnancy Photography</option>
                        <option value="child-dedication">
                           Child Dedication
                        </option>
                        <option value="videography">Videography</option>
                        <option value="360-booth">
                           360 Video Booth Rental
                        </option>
                        <option value="dry-ice">Dry Ice Machine Rental</option>
                     </select>
                  </div>
               </div>

               <div className="space-y-2">
                  <Label htmlFor="eventDate">Event Date (if applicable)</Label>
                  <Input
                     id="eventDate"
                     name="eventDate"
                     type="date"
                     value={formData.eventDate}
                     onChange={handleChange}
                  />
               </div>

               <div className="space-y-2">
                  <Label htmlFor="message">Message *</Label>
                  <Textarea
                     id="message"
                     name="message"
                     value={formData.message}
                     onChange={handleChange}
                     required
                     placeholder="Tell us about your event, vision, and any specific requirements..."
                     rows={5}
                  />
               </div>

               <Button type="submit" className="w-full" disabled={isSubmitting}>
                  {isSubmitting ? "Sending..." : "Send Message"}
               </Button>
            </form>
         </CardContent>
      </Card>
   );
}
