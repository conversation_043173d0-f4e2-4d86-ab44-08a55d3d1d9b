import { Camera, Sparkles, Video, Wind } from "lucide-react";

export interface Service {
   title: string;
   description: string;
   image: string;
   href: string;
   icon: React.ElementType;
}

export const services: Service[] = [
   {
      title: "Photography",
      description:
         "Wedding, Pre-wedding, Pregnancy, and Child-Dedication photography services.",
      image: "/images/wedding-shoots/wedding-shoot-5.JPG",
      href: "/services/photography",
      icon: Camera,
   },
   {
      title: "Videography",
      description:
         "Cinematic video coverage for weddings, events, and special moments.",
      image: "/images/videography.jpg",
      href: "/services/videography",
      icon: Video,
   },
   {
      title: "360 Video Booth",
      description:
         "Interactive 360-degree video experiences for unforgettable event entertainment.",
      image: "/images/360-photo-booth.jpg",
      href: "/services/360-video-booth",
      icon: Sparkles,
   },
   {
      title: "Dry Ice Machine",
      description:
         "Dramatic atmospheric effects for first dances and special moments.",
      image: "/images/dry-ice-machine.jpg",
      href: "/services/dry-ice-machine",
      icon: Wind,
   },
];

// Service categories for better organization
export const serviceCategories = {
   photography: {
      title: "Photography Services",
      description: "Professional photography for all your special moments",
      services: [
         "wedding",
         "pre-wedding",
         "pregnancy",
         "child-dedication",
         "birthday-shoot",
         "bridal-shower",
      ],
   },
   videography: {
      title: "Videography Services",
      description: "Cinematic video coverage that tells your story",
      services: ["wedding-videography", "event-videography"],
   },
   equipment: {
      title: "Equipment Rental",
      description: "Professional equipment to enhance your events",
      services: ["360-booth", "dry-ice"],
   },
};

export const videographyServices = [
   {
      title: "Wedding Films",
      description:
         "Cinematic wedding films capturing the emotion and beauty of your special day.",
      features: [
         "Full ceremony coverage",
         "Reception highlights",
         "Professional editing",
         "HD/4K quality",
      ],
   },
   {
      title: "Pre-wedding Videos",
      description:
         "Romantic pre-wedding video sessions showcasing your love story.",
      features: [
         "Multiple locations",
         "Story-driven narrative",
         "Drone footage available",
         "Music synchronization",
      ],
   },
   {
      title: "Event Documentation",
      description:
         "Professional event videography for special occasions and celebrations.",
      features: [
         "Multi-camera setup",
         "Live streaming options",
         "Professional audio",
         "Quick turnaround",
      ],
   },
   {
      title: "Highlight Reels",
      description:
         "Short, impactful highlight videos perfect for social media sharing.",
      features: [
         "60-90 second duration",
         "Social media optimized",
         "Dynamic editing",
         "Multiple formats",
      ],
   },
];

export const specialServices = [
   {
      title: "360 Video Booth",
      description:
         "Create stunning 360-degree videos that capture your guests from every angle. Our interactive video booth is perfect for weddings, parties, and corporate events, providing entertainment and memorable keepsakes for your guests.",
      features: [
         "Professional setup",
         "High-quality cameras",
         "Instant social sharing",
         "Custom branding",
         "Props included",
         "Professional attendant",
      ],
      icon: Sparkles,
      ctaText: "Book the 360 Booth",
      href: "/contact",
   },
   {
      title: "Dry Ice Machine",
      description:
         "Add dramatic flair to your event with our professional dry ice machine. Perfect for first dances, grand entrances, and creating magical atmospheric effects that will leave your guests in awe and provide stunning photo opportunities.",
      features: [
         "Safe operation",
         "Dramatic effects",
         "Perfect for first dances",
         "Professional grade",
         "Venue approved",
         "Stunning visuals",
      ],
      icon: Wind,
      ctaText: "Add Dry Ice Effects",
      href: "/contact",
   },
];
