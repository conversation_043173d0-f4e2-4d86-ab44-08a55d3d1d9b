"use server";

import {
   ApiResponse,
   AdminCommentInput,
   AdminCommentFilters,
} from "@/lib/models";
import {
   getAdminCommentsByAlbum,
   getAdminCommentCountByAlbum,
   softDeleteComment,
   restoreComment,
   updateAdminResponse,
   removeAdminResponse,
   getAdminCommentById,
   permanentlyDeleteComment,
} from "@/lib/services/admin-comment-service";

/**
 * Get comments for admin management
 */
export async function getAdminAlbumComments(
   albumId: string,
   filters?: AdminCommentFilters
): Promise<ApiResponse> {
   try {
      if (!albumId) {
         return {
            success: false,
            error: "Album ID is required",
         };
      }

      const comments = await getAdminCommentsByAlbum(albumId, filters);

      return {
         success: true,
         data: comments,
         message: "Admin comments fetched successfully",
      };
   } catch (error) {
      console.error("Error fetching admin album comments:", error);
      return {
         success: false,
         error:
            error instanceof Error ? error.message : "Failed to fetch admin comments",
      };
   }
}

/**
 * Get comment count for admin
 */
export async function getAdminAlbumCommentCount(
   albumId: string,
   filters?: AdminCommentFilters
): Promise<ApiResponse> {
   try {
      if (!albumId) {
         return {
            success: false,
            error: "Album ID is required",
         };
      }

      const count = await getAdminCommentCountByAlbum(albumId, filters);

      return {
         success: true,
         data: count,
         message: "Admin comment count fetched successfully",
      };
   } catch (error) {
      console.error("Error fetching admin comment count:", error);
      return {
         success: false,
         error:
            error instanceof Error ? error.message : "Failed to fetch admin comment count",
      };
   }
}

/**
 * Soft delete a comment
 */
export async function softDeleteCommentAction(id: string): Promise<ApiResponse> {
   try {
      if (!id) {
         return {
            success: false,
            error: "Comment ID is required",
         };
      }

      const deleted = await softDeleteComment(id);

      if (!deleted) {
         return {
            success: false,
            error: "Comment not found or already deleted",
         };
      }

      return {
         success: true,
         message: "Comment deleted successfully",
      };
   } catch (error) {
      console.error("Error soft deleting comment:", error);
      return {
         success: false,
         error:
            error instanceof Error ? error.message : "Failed to delete comment",
      };
   }
}

/**
 * Restore a soft-deleted comment
 */
export async function restoreCommentAction(id: string): Promise<ApiResponse> {
   try {
      if (!id) {
         return {
            success: false,
            error: "Comment ID is required",
         };
      }

      const restored = await restoreComment(id);

      if (!restored) {
         return {
            success: false,
            error: "Comment not found or not deleted",
         };
      }

      return {
         success: true,
         message: "Comment restored successfully",
      };
   } catch (error) {
      console.error("Error restoring comment:", error);
      return {
         success: false,
         error:
            error instanceof Error ? error.message : "Failed to restore comment",
      };
   }
}

/**
 * Add or update admin response
 */
export async function updateAdminResponseAction(
   id: string,
   input: AdminCommentInput
): Promise<ApiResponse> {
   try {
      if (!id) {
         return {
            success: false,
            error: "Comment ID is required",
         };
      }

      if (!input.adminResponse || input.adminResponse.trim().length === 0) {
         return {
            success: false,
            error: "Admin response is required",
         };
      }

      const comment = await updateAdminResponse(id, input);

      if (!comment) {
         return {
            success: false,
            error: "Comment not found",
         };
      }

      return {
         success: true,
         data: comment,
         message: "Admin response updated successfully",
      };
   } catch (error) {
      console.error("Error updating admin response:", error);
      return {
         success: false,
         error:
            error instanceof Error ? error.message : "Failed to update admin response",
      };
   }
}

/**
 * Remove admin response
 */
export async function removeAdminResponseAction(id: string): Promise<ApiResponse> {
   try {
      if (!id) {
         return {
            success: false,
            error: "Comment ID is required",
         };
      }

      const removed = await removeAdminResponse(id);

      if (!removed) {
         return {
            success: false,
            error: "Comment not found or no admin response to remove",
         };
      }

      return {
         success: true,
         message: "Admin response removed successfully",
      };
   } catch (error) {
      console.error("Error removing admin response:", error);
      return {
         success: false,
         error:
            error instanceof Error ? error.message : "Failed to remove admin response",
      };
   }
}

/**
 * Get a single comment for admin
 */
export async function getAdminCommentAction(id: string): Promise<ApiResponse> {
   try {
      if (!id) {
         return {
            success: false,
            error: "Comment ID is required",
         };
      }

      const comment = await getAdminCommentById(id);

      if (!comment) {
         return {
            success: false,
            error: "Comment not found",
         };
      }

      return {
         success: true,
         data: comment,
         message: "Admin comment fetched successfully",
      };
   } catch (error) {
      console.error("Error fetching admin comment:", error);
      return {
         success: false,
         error:
            error instanceof Error ? error.message : "Failed to fetch admin comment",
      };
   }
}

/**
 * Permanently delete a comment (hard delete)
 */
export async function permanentlyDeleteCommentAction(id: string): Promise<ApiResponse> {
   try {
      if (!id) {
         return {
            success: false,
            error: "Comment ID is required",
         };
      }

      const deleted = await permanentlyDeleteComment(id);

      if (!deleted) {
         return {
            success: false,
            error: "Comment not found or already deleted",
         };
      }

      return {
         success: true,
         message: "Comment permanently deleted successfully",
      };
   } catch (error) {
      console.error("Error permanently deleting comment:", error);
      return {
         success: false,
         error:
            error instanceof Error ? error.message : "Failed to permanently delete comment",
      };
   }
}
