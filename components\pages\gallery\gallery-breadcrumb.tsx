"use client";

import { cn } from "@/lib/utils";
import { HomeIcon } from "@heroicons/react/24/solid";
import { ChevronRight } from "lucide-react";
import Link from "next/link";

export interface BreadcrumbItem {
   label: string;
   href?: string;
   isActive?: boolean;
}

interface GalleryBreadcrumbProps {
   items: BreadcrumbItem[];
   className?: string;
}

export default function GalleryBreadcrumb({
   items,
   className,
}: GalleryBreadcrumbProps) {
   return (
      <nav
         className={cn(
            "flex items-center space-x-2 text-sm text-muted-foreground mb-8",
            className
         )}
         aria-label="Breadcrumb"
      >
         {/* Home Icon */}
         <Link
            href="/"
            className="flex items-center hover:text-primary transition-colors duration-200"
         >
            <HomeIcon className="w-4 h-4" />
            <span className="sr-only">Home</span>
         </Link>

         {/* Breadcrumb Items */}
         {items.map((item, index) => (
            <div key={index} className="flex items-center space-x-2">
               {/* Separator */}
               <ChevronRight className="w-4 h-4 text-muted-foreground/50" />

               {/* Breadcrumb Item */}
               {item.href && !item.isActive ? (
                  <Link
                     href={item.href}
                     className={cn(
                        "hover:text-primary transition-colors duration-200",
                        "font-medium"
                     )}
                  >
                     {item.label}
                  </Link>
               ) : (
                  <span
                     className={cn(
                        item.isActive
                           ? "text-foreground font-semibold"
                           : "text-muted-foreground"
                     )}
                  >
                     {item.label}
                  </span>
               )}
            </div>
         ))}
      </nav>
   );
}
