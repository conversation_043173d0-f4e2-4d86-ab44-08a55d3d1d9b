"use client";

import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Loader2 } from "lucide-react";

interface LoadMoreButtonProps {
   onClick: () => void;
   isLoading?: boolean;
   hasMore?: boolean;
   loadingText?: string;
   buttonText?: string;
   className?: string;
   variant?: "default" | "outline" | "ghost";
}

export default function LoadMoreButton({
   onClick,
   isLoading = false,
   hasMore = true,
   loadingText = "Loading more...",
   buttonText = "Load More",
   className,
   variant = "outline",
}: LoadMoreButtonProps) {
   if (!hasMore) {
      return (
         <div className={cn("text-center py-8", className)}>
            <p className="text-muted-foreground">No more items to load</p>
         </div>
      );
   }

   return (
      <div className={cn("flex justify-center py-8", className)}>
         <Button
            onClick={onClick}
            disabled={isLoading}
            variant={variant}
            size="lg"
            className={cn(
               "px-8 py-3 transition-all duration-300",
               "hover:shadow-lg",
               variant === "outline" && "border-2 hover:border-primary",
               isLoading && "cursor-not-allowed"
            )}
         >
            {isLoading ? (
               <>
                  <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                  {loadingText}
               </>
            ) : (
               <>{buttonText}</>
            )}
         </Button>
      </div>
   );
}
