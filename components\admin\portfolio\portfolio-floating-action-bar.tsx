"use client";

import { But<PERSON> } from "@/components/ui/button";
import { CheckSquare, Settings, Square, X } from "lucide-react";

interface PortfolioFloatingActionBarProps {
   selectedCount: number;
   totalCount: number;
   onClearSelection: () => void;
   onSelectAll: () => void;
   onBulkActions: () => void;
}

export default function PortfolioFloatingActionBar({
   selectedCount,
   totalCount,
   onClearSelection,
   onSelectAll,
   onBulkActions,
}: PortfolioFloatingActionBarProps) {
   if (selectedCount === 0) return null;

   const isAllSelected = selectedCount === totalCount && totalCount > 0;

   return (
      <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50">
         <div className="bg-background/95 backdrop-blur-sm border border-border/50 rounded-lg shadow-lg px-4 py-3">
            <div className="flex items-center space-x-2">
               <span className="text-sm font-medium text-foreground whitespace-nowrap pr-2">
                  {selectedCount} image{selectedCount !== 1 ? "s" : ""} selected
               </span>

               <Button
                  variant="ghost"
                  onClick={onSelectAll}
                  className="h-8 px-2 hover:bg-transparent"
               >
                  {isAllSelected ? (
                     <CheckSquare className="w-4 h-4" />
                  ) : (
                     <Square className="w-4 h-4" />
                  )}
                  <span className="hidden md:inline">
                     {isAllSelected ? "Deselect All" : "Select All"}
                  </span>
               </Button>

               <Button
                  variant="outline"
                  onClick={onClearSelection}
                  className="h-8 px-2"
               >
                  <X className="w-4 h-4" />
                  <span className="hidden md:inline">Clear</span>
               </Button>

               <Button
                  variant="outline"
                  onClick={onBulkActions}
                  className="h-8 px-2 sm:px-3"
               >
                  <Settings className="w-4 h-4" />
                  <span className="hidden md:inline">Move or Delete</span>
               </Button>
            </div>
         </div>
      </div>
   );
}
