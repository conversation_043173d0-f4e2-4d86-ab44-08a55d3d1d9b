"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogFooter,
   DialogHeader,
   DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useUpdatePortfolioImage } from "@/lib/hooks/use-portfolio";
import { PortfolioImage } from "@/lib/models/portfolio";
import { editImageMetadataSchema } from "@/lib/schemas/portfolio-schemas";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2, Save } from "lucide-react";
import { useEffect } from "react";
import { useForm } from "react-hook-form";

interface PortfolioImageMetadataDialogProps {
   image: PortfolioImage | null;
   serviceName: string;
   isOpen: boolean;
   onClose: () => void;
}

export default function PortfolioImageMetadataDialog({
   image,
   serviceName,
   isOpen,
   onClose,
}: PortfolioImageMetadataDialogProps) {
   const updateImageMutation = useUpdatePortfolioImage();

   const form = useForm({
      resolver: zodResolver(editImageMetadataSchema),
      defaultValues: {
         name: "",
         altText: "",
         generateAltText: false,
      },
      mode: "onChange",
   });

   const {
      register,
      handleSubmit,
      formState: { errors, isDirty },
      reset,
      watch,
      setValue,
   } = form;

   const generateAltText = watch("generateAltText");
   const name = watch("name");

   // Update form when image changes
   useEffect(() => {
      if (image) {
         reset({
            name: image.name,
            altText: image.altText,
            generateAltText: false,
         });
      }
   }, [image, reset]);

   // Auto-generate alt text when checkbox is checked or name changes
   useEffect(() => {
      if (generateAltText && name && serviceName) {
         const baseText = name.replace(/\.[^/.]+$/, ""); // Remove file extension
         const generatedAltText = `${serviceName} - ${baseText}`;
         setValue("altText", generatedAltText);
      }
   }, [generateAltText, name, serviceName, setValue]);

   const onSubmit = async (data: {
      name: string;
      altText?: string;
      generateAltText?: boolean;
   }) => {
      if (!image) return;

      try {
         const formData = new FormData();
         formData.append("name", data.name);
         if (data.altText) {
            formData.append("altText", data.altText);
         }

         await updateImageMutation.mutateAsync({
            id: image._id!.toString(),
            formData,
         });

         onClose();
      } catch (error) {
         console.error("Failed to update image metadata:", error);
      }
   };

   const handleClose = () => {
      reset();
      onClose();
   };

   if (!image) return null;

   return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
         <DialogContent className="sm:max-w-md">
            <DialogHeader>
               <DialogTitle>Edit Image Metadata</DialogTitle>
               <DialogDescription>
                  Update the name and alt text for this portfolio image.
               </DialogDescription>
            </DialogHeader>

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
               <div className="space-y-2">
                  <Label htmlFor="name">Image Name *</Label>
                  <Input
                     id="name"
                     placeholder="Enter image name"
                     {...register("name")}
                     className={errors.name ? "border-destructive" : ""}
                  />
                  {errors.name && (
                     <p className="text-sm text-destructive">
                        {errors.name.message}
                     </p>
                  )}
               </div>

               <div className="space-y-2">
                  <Label htmlFor="altText">Alt Text</Label>
                  <Textarea
                     id="altText"
                     placeholder="Enter alt text for accessibility"
                     rows={3}
                     {...register("altText")}
                     className={errors.altText ? "border-destructive" : ""}
                  />
                  {errors.altText && (
                     <p className="text-sm text-destructive">
                        {errors.altText.message}
                     </p>
                  )}
                  <div className="flex items-center space-x-2">
                     <Checkbox
                        id="generateAltText"
                        checked={generateAltText}
                        onCheckedChange={(checked) =>
                           setValue("generateAltText", checked as boolean)
                        }
                     />
                     <Label
                        htmlFor="generateAltText"
                        className="text-sm font-normal"
                     >
                        Auto-generate alt text with service name pattern
                     </Label>
                  </div>
               </div>

               <DialogFooter>
                  <Button
                     type="button"
                     variant="outline"
                     onClick={handleClose}
                     disabled={updateImageMutation.isPending}
                  >
                     Cancel
                  </Button>
                  <Button
                     type="submit"
                     disabled={updateImageMutation.isPending || !isDirty}
                     className="bg-gradient-accent hover:opacity-90"
                  >
                     {updateImageMutation.isPending ? (
                        <>
                           <Loader2 className="w-4 h-4 animate-spin mr-2" />
                           Saving...
                        </>
                     ) : (
                        <>
                           <Save className="w-4 h-4 mr-2" />
                           Save Changes
                        </>
                     )}
                  </Button>
               </DialogFooter>
            </form>
         </DialogContent>
      </Dialog>
   );
}
