import {
   AdminSidebar,
   AdminSidebarMobileTrigger,
} from "@/components/admin/admin-sidebar";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import type { Metadata } from "next";

export const metadata: Metadata = {
   title: {
      default: "Gallery Admin - Astral Studios",
      template: "%s | Gallery Admin - Astral Studios",
   },
   description: "Gallery administration interface for Astral Studios",
};

export default function AdminLayout({
   children,
}: {
   children: React.ReactNode;
}) {
   return (
      <SidebarProvider>
         <div className="flex min-h-screen bg-background w-full">
            <AdminSidebar />
            <SidebarInset className="flex-1 overflow-y-auto min-h-screen">
               <AdminSidebarMobileTrigger />
               <main className="lg:pt-0 pt-16">{children}</main>
            </SidebarInset>
         </div>
      </SidebarProvider>
   );
}
