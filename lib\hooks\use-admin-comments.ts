"use client";

import {
   getAdminAlbumComments,
   getAdminAlbumCommentCount,
   softDeleteCommentAction,
   restoreCommentAction,
   updateAdminResponseAction,
   removeAdminResponseAction,
   getAdminCommentAction,
   permanentlyDeleteCommentAction,
} from "@/lib/actions/admin-comment-actions";
import { AdminCommentInput, AdminCommentFilters } from "@/lib/models";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { queryKeys, invalidationKeys } from "./query-keys";

/**
 * Hook to fetch admin comments for an album
 */
export function useAdminAlbumComments(albumId: string, filters?: AdminCommentFilters) {
   return useQuery({
      queryKey: queryKeys.adminComments.byAlbum(albumId, filters),
      queryFn: () => getAdminAlbumComments(albumId, filters),
      enabled: !!albumId,
      staleTime: 30 * 1000, // 30 seconds - shorter for admin interface
   });
}

/**
 * Hook to fetch admin comment count for an album
 */
export function useAdminAlbumCommentCount(albumId: string, filters?: AdminCommentFilters) {
   return useQuery({
      queryKey: queryKeys.adminComments.countByAlbum(albumId, filters),
      queryFn: () => getAdminAlbumCommentCount(albumId, filters),
      enabled: !!albumId,
      staleTime: 30 * 1000, // 30 seconds
   });
}

/**
 * Hook to fetch a single admin comment
 */
export function useAdminComment(commentId: string) {
   return useQuery({
      queryKey: queryKeys.adminComments.byId(commentId),
      queryFn: () => getAdminCommentAction(commentId),
      enabled: !!commentId,
      staleTime: 30 * 1000, // 30 seconds
   });
}

/**
 * Hook to soft delete a comment
 */
export function useSoftDeleteComment() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: softDeleteCommentAction,
      onSuccess: (response) => {
         if (response.success) {
            toast.success("Comment deleted successfully");
            // Invalidate admin comment queries
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.adminComments.all,
            });
            // Also invalidate regular comment queries to update client view
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.comments.all,
            });
         } else {
            toast.error(response.error || "Failed to delete comment");
         }
      },
      onError: (error) => {
         console.error("Error deleting comment:", error);
         toast.error("Failed to delete comment");
      },
   });
}

/**
 * Hook to restore a soft-deleted comment
 */
export function useRestoreComment() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: restoreCommentAction,
      onSuccess: (response) => {
         if (response.success) {
            toast.success("Comment restored successfully");
            // Invalidate admin comment queries
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.adminComments.all,
            });
            // Also invalidate regular comment queries to update client view
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.comments.all,
            });
         } else {
            toast.error(response.error || "Failed to restore comment");
         }
      },
      onError: (error) => {
         console.error("Error restoring comment:", error);
         toast.error("Failed to restore comment");
      },
   });
}

/**
 * Hook to update admin response
 */
export function useUpdateAdminResponse() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: ({ commentId, input }: { commentId: string; input: AdminCommentInput }) =>
         updateAdminResponseAction(commentId, input),
      onSuccess: (response) => {
         if (response.success) {
            toast.success("Admin response updated successfully");
            // Invalidate admin comment queries
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.adminComments.all,
            });
            // Also invalidate regular comment queries to update client view
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.comments.all,
            });
         } else {
            toast.error(response.error || "Failed to update admin response");
         }
      },
      onError: (error) => {
         console.error("Error updating admin response:", error);
         toast.error("Failed to update admin response");
      },
   });
}

/**
 * Hook to remove admin response
 */
export function useRemoveAdminResponse() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: removeAdminResponseAction,
      onSuccess: (response) => {
         if (response.success) {
            toast.success("Admin response removed successfully");
            // Invalidate admin comment queries
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.adminComments.all,
            });
            // Also invalidate regular comment queries to update client view
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.comments.all,
            });
         } else {
            toast.error(response.error || "Failed to remove admin response");
         }
      },
      onError: (error) => {
         console.error("Error removing admin response:", error);
         toast.error("Failed to remove admin response");
      },
   });
}

/**
 * Hook to permanently delete a comment
 */
export function usePermanentlyDeleteComment() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: permanentlyDeleteCommentAction,
      onSuccess: (response) => {
         if (response.success) {
            toast.success("Comment permanently deleted");
            // Invalidate admin comment queries
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.adminComments.all,
            });
            // Also invalidate regular comment queries to update client view
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.comments.all,
            });
         } else {
            toast.error(response.error || "Failed to permanently delete comment");
         }
      },
      onError: (error) => {
         console.error("Error permanently deleting comment:", error);
         toast.error("Failed to permanently delete comment");
      },
   });
}
