"use server";

import {
   AdminCommentFilters,
   AdminCommentInput,
   Comment,
   CommentWithReplies,
} from "@/lib/models";
import { getCollection } from "@/lib/mongodb/db";
import { ObjectId } from "mongodb";

/**
 * Get comments for admin management with filtering options
 */
export async function getAdminCommentsByAlbum(
   albumId: string,
   filters: AdminCommentFilters = {}
): Promise<CommentWithReplies[]> {
   try {
      const commentCollection = await getCollection<Comment>("comments");
      const {
         showDeleted = false,
         searchQuery,
         limit = 50,
         offset = 0,
      } = filters;

      // Build query
      const query: Record<string, unknown> = { albumId };

      // Filter by deletion status
      if (showDeleted) {
         query.deletedAt = { $exists: true };
      } else {
         query.deletedAt = { $exists: false };
      }

      // Add search functionality
      if (searchQuery && searchQuery.trim()) {
         const searchRegex = new RegExp(searchQuery.trim(), "i");
         query.$or = [
            { content: searchRegex },
            { authorName: searchRegex },
            { authorEmail: searchRegex },
            { adminResponse: searchRegex },
         ];
      }

      // Get comments with pagination
      const comments = await commentCollection
         .find(query)
         .sort({ createdAt: -1 }) // Latest first for admin view
         .skip(offset)
         .limit(limit)
         .toArray();

      // Build the comment tree (similar to regular comment service but include deleted)
      const commentMap = new Map<string, CommentWithReplies>();
      const rootComments: CommentWithReplies[] = [];

      // First pass: create all comment objects
      comments.forEach((comment) => {
         const commentWithReplies: CommentWithReplies = {
            ...comment,
            _id: comment._id?.toString(),
            replies: [],
            replyCount: 0,
         };
         commentMap.set(comment._id?.toString() || "", commentWithReplies);
      });

      // Second pass: build the tree structure
      comments.forEach((comment) => {
         const commentWithReplies = commentMap.get(
            comment._id?.toString() || ""
         );
         if (!commentWithReplies) return;

         if (comment.parentId) {
            // This is a reply
            const parent = commentMap.get(comment.parentId);
            if (parent) {
               parent.replies.push(commentWithReplies);
               parent.replyCount++;
            }
         } else {
            // This is a root comment
            rootComments.push(commentWithReplies);
         }
      });

      return rootComments;
   } catch (error) {
      console.error("Error fetching admin comments by album:", error);
      throw new Error("Failed to fetch admin comments");
   }
}

/**
 * Get comment count for admin with filtering
 */
export async function getAdminCommentCountByAlbum(
   albumId: string,
   filters: AdminCommentFilters = {}
): Promise<number> {
   try {
      const commentCollection = await getCollection<Comment>("comments");
      const { showDeleted = false, searchQuery } = filters;

      // Build query
      const query: Record<string, unknown> = { albumId };

      // Filter by deletion status
      if (showDeleted) {
         query.deletedAt = { $exists: true };
      } else {
         query.deletedAt = { $exists: false };
      }

      // Add search functionality
      if (searchQuery && searchQuery.trim()) {
         const searchRegex = new RegExp(searchQuery.trim(), "i");
         query.$or = [
            { content: searchRegex },
            { authorName: searchRegex },
            { authorEmail: searchRegex },
            { adminResponse: searchRegex },
         ];
      }

      return await commentCollection.countDocuments(query);
   } catch (error) {
      console.error("Error fetching admin comment count:", error);
      throw new Error("Failed to fetch admin comment count");
   }
}

/**
 * Soft delete a comment (set deletedAt timestamp)
 */
export async function softDeleteComment(id: string): Promise<boolean> {
   try {
      const collection = await getCollection<Comment>("comments");

      const result = await collection.findOneAndUpdate(
         { _id: new ObjectId(id) },
         {
            $set: {
               deletedAt: new Date(),
               updatedAt: new Date(),
            },
         },
         { returnDocument: "after" }
      );

      return !!result;
   } catch (error) {
      console.error("Error soft deleting comment:", error);
      throw new Error("Failed to soft delete comment");
   }
}

/**
 * Restore a soft-deleted comment (remove deletedAt timestamp)
 */
export async function restoreComment(id: string): Promise<boolean> {
   try {
      const collection = await getCollection<Comment>("comments");

      const result = await collection.findOneAndUpdate(
         { _id: new ObjectId(id) },
         {
            $unset: { deletedAt: "" },
            $set: { updatedAt: new Date() },
         },
         { returnDocument: "after" }
      );

      return !!result;
   } catch (error) {
      console.error("Error restoring comment:", error);
      throw new Error("Failed to restore comment");
   }
}

/**
 * Add or update admin response to a comment
 */
export async function updateAdminResponse(
   id: string,
   input: AdminCommentInput
): Promise<Comment | null> {
   try {
      const collection = await getCollection<Comment>("comments");

      const result = await collection.findOneAndUpdate(
         { _id: new ObjectId(id) },
         {
            $set: {
               adminResponse: input.adminResponse.trim(),
               updatedAt: new Date(),
            },
         },
         { returnDocument: "after" }
      );

      if (!result) return null;

      // Convert ObjectId to string for client component serialization
      return {
         ...result,
         _id: result._id?.toString(),
      };
   } catch (error) {
      console.error("Error updating admin response:", error);
      throw new Error("Failed to update admin response");
   }
}

/**
 * Remove admin response from a comment
 */
export async function removeAdminResponse(id: string): Promise<boolean> {
   try {
      const collection = await getCollection<Comment>("comments");

      const result = await collection.findOneAndUpdate(
         { _id: new ObjectId(id) },
         {
            $unset: { adminResponse: "" },
            $set: { updatedAt: new Date() },
         },
         { returnDocument: "after" }
      );

      return !!result;
   } catch (error) {
      console.error("Error removing admin response:", error);
      throw new Error("Failed to remove admin response");
   }
}

/**
 * Get a single comment by ID for admin (includes deleted comments)
 */
export async function getAdminCommentById(id: string): Promise<Comment | null> {
   try {
      const collection = await getCollection<Comment>("comments");
      const comment = await collection.findOne({ _id: new ObjectId(id) });

      if (!comment) return null;

      // Convert ObjectId to string for client component serialization
      return {
         ...comment,
         _id: comment._id?.toString(),
      };
   } catch (error) {
      console.error("Error fetching admin comment by ID:", error);
      throw new Error("Failed to fetch admin comment");
   }
}

/**
 * Permanently delete a comment and all its replies (hard delete)
 */
export async function permanentlyDeleteComment(id: string): Promise<boolean> {
   try {
      const collection = await getCollection<Comment>("comments");

      // First, delete all replies to this comment
      await collection.deleteMany({ parentId: id });

      // Then delete the comment itself
      const result = await collection.deleteOne({ _id: new ObjectId(id) });

      return result.deletedCount > 0;
   } catch (error) {
      console.error("Error permanently deleting comment:", error);
      throw new Error("Failed to permanently delete comment");
   }
}
