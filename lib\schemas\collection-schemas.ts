import { z } from "zod";

/**
 * Schema for creating a new collection
 */
export const createCollectionSchema = z.object({
   name: z
      .string()
      .min(1, "Name is required")
      .max(100, "Name must be less than 100 characters")
      .trim(),
   description: z
      .string()
      .max(500, "Description must be less than 500 characters")
      .optional()
      .or(z.literal("")),
   color: z
      .string()
      .regex(
         /^#[0-9A-F]{6}$/i,
         "Color must be a valid hex color (e.g., #FF0000)"
      )
      .optional(),
});

/**
 * Schema for updating a collection
 */
export const updateCollectionSchema = z.object({
   name: z
      .string()
      .min(1, "Name is required")
      .max(100, "Name must be less than 100 characters")
      .trim()
      .optional(),
   description: z
      .string()
      .max(500, "Description must be less than 500 characters")
      .optional()
      .or(z.literal("")),
   color: z
      .string()
      .regex(
         /^#[0-9A-F]{6}$/i,
         "Color must be a valid hex color (e.g., #FF0000)"
      )
      .optional(),
});

/**
 * Type inference for create collection form
 */
export type CreateCollectionFormData = z.infer<typeof createCollectionSchema>;

/**
 * Type inference for update collection form
 */
export type UpdateCollectionFormData = z.infer<typeof updateCollectionSchema>;
