"use server";

import { AdminSession } from "@/lib/models";
import { JWTPayload, SignJWT, jwtVerify } from "jose";
import { cookies } from "next/headers";

// Session configuration
const SESSION_COOKIE_NAME = "admin-session";
const SESSION_DURATION = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds
const JWT_SECRET = new TextEncoder().encode(
   process.env.JWT_SECRET || "your-secret-key-change-this-in-production"
);

// Session payload interface
interface SessionPayload extends JWTPayload {
   adminId: string;
   name: string;
   email: string;
   exp: number;
}

/**
 * Create a new session for an admin
 */
export async function createSession(admin: AdminSession): Promise<void> {
   try {
      const expiresAt = new Date(Date.now() + SESSION_DURATION);

      // Create JWT payload
      const payload: SessionPayload = {
         adminId: admin.id,
         name: admin.name,
         email: admin.email,
         exp: Math.floor(expiresAt.getTime() / 1000),
      };

      // Sign the JWT
      const token = await new SignJWT(payload)
         .setProtectedHeader({ alg: "HS256" })
         .setIssuedAt()
         .setExpirationTime(expiresAt)
         .sign(JWT_SECRET);

      // Set the session cookie
      const cookieStore = await cookies();
      cookieStore.set(SESSION_COOKIE_NAME, token, {
         httpOnly: true,
         secure: process.env.NODE_ENV === "production",
         sameSite: "lax",
         expires: expiresAt,
         path: "/",
      });
   } catch (error) {
      console.error("Error creating session:", error);
      throw new Error("Failed to create session");
   }
}

/**
 * Get the current session
 */
export async function getSession(): Promise<AdminSession | null> {
   try {
      const cookieStore = await cookies();
      const token = cookieStore.get(SESSION_COOKIE_NAME)?.value;

      if (!token) {
         return null;
      }

      // Verify and decode the JWT
      const { payload } = await jwtVerify(token, JWT_SECRET);
      const sessionPayload = payload as SessionPayload;

      // Check if token is expired
      if (sessionPayload.exp * 1000 < Date.now()) {
         await destroySession();
         return null;
      }

      return {
         id: sessionPayload.adminId,
         name: sessionPayload.name,
         email: sessionPayload.email,
      };
   } catch (error) {
      console.error("Error getting session:", error);
      // If there's an error with the token, destroy the session
      await destroySession();
      return null;
   }
}

/**
 * Destroy the current session
 */
export async function destroySession(): Promise<void> {
   try {
      const cookieStore = await cookies();
      cookieStore.delete(SESSION_COOKIE_NAME);
   } catch (error) {
      console.error("Error destroying session:", error);
      // Don't throw error here as this is used in error handling
   }
}

/**
 * Check if user is authenticated
 */
export async function isAuthenticated(): Promise<boolean> {
   const session = await getSession();
   return session !== null;
}

/**
 * Require authentication - throws error if not authenticated
 */
export async function requireAuth(): Promise<AdminSession> {
   const session = await getSession();

   if (!session) {
      throw new Error("Authentication required");
   }

   return session;
}

/**
 * Refresh session (extend expiration)
 */
export async function refreshSession(): Promise<void> {
   try {
      const session = await getSession();

      if (session) {
         // Create a new session with extended expiration
         await createSession(session);
      }
   } catch (error) {
      console.error("Error refreshing session:", error);
      // Don't throw error here as this is used in middleware
   }
}
